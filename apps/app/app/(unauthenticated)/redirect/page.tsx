import PaymentRedirect from '@/app/(unauthenticated)/redirect/components/payment-redirect';
import { log } from '@repo/observability/log';
import { notFound } from 'next/navigation';

export default async function PaymentCallbackPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Extract parameters from the search params
  const { eventId, status, slug } = await searchParams;

  if (!eventId || !slug) {
    log.error('Missing required parameters in payment callback', {
      slug,
      eventId,
      status,
    });
    return notFound();
  }

  log.info('Payment callback session check', {
    status,
    eventId,
    slug,
  });

  // Construct the target URL for redirection
  const targetUrl = `/events/${slug}/upgrade-confirmation?eventId=${eventId}&status=${status}`;

  return <PaymentRedirect targetUrl={targetUrl} />;
}
