'use client';

import { Loader2 } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
} from '@repo/design-system/components/ui/card';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function PaymentRedirect({ targetUrl }: { targetUrl: string }) {
  const router = useRouter();
  const [countdown, setCountdown] = useState(2);
  const [shouldRedirect, setShouldRedirect] = useState(false);

  // Handle the redirect in a separate effect
  useEffect(() => {
    if (shouldRedirect && targetUrl) {
      router.push(targetUrl);
    }
  }, [shouldRedirect, router, targetUrl]);

  // Handle the countdown
  useEffect(() => {
    // Only start countdown if there's a target URL
    if (!targetUrl) {
      return;
    }

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setShouldRedirect(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [targetUrl]);

  // If no target URL is provided, don't show the redirect UI
  if (!targetUrl) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 z-50 flex min-h-screen min-w-screen flex-col items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center space-y-4 pt-6 pb-4 text-center">
          <div className="mb-2 rounded-full bg-primary/10 p-3">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
          <h2 className="font-semibold text-xl">You are being redirected</h2>
          <p className="text-muted-foreground">
            Please wait while we redirect you to the payment confirmation
            page...
          </p>
          {countdown > 0 && (
            <p className="text-muted-foreground text-sm">
              Redirecting in {countdown} second{countdown !== 1 ? 's' : ''}
            </p>
          )}
        </CardContent>
        <CardFooter className="flex justify-center pb-6">
          <Button variant="outline" onClick={() => setShouldRedirect(true)}>
            Redirect now
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
