import { env } from '@/env';
import darkLogo from '@/public/logo-dark.png';
import loginBackground from '@/public/organizer-login.jpg';
import { organizerAuth } from '@repo/auth/organizer-auth';
import { ModeToggle } from '@repo/design-system/components/mode-toggle';
import { headers } from 'next/headers';
import Image from 'next/image';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import type { ReactNode } from 'react';

type AuthLayoutProps = {
  readonly children: ReactNode;
};

const SignInLayout = async ({ children }: AuthLayoutProps) => {
  // Check if user is already authenticated
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (session?.user) {
    // If user is already authenticated, redirect to home page
    return redirect('/');
  }

  return (
    <div className="container relative grid h-dvh flex-col items-center justify-center lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted text-white lg:flex dark:border-r">
        <div className="relative size-full">
          <Image
            src={loginBackground}
            alt="login-background"
            fill
            className="object-cover object-left-bottom"
          />
        </div>
        <div className="absolute top-10 left-10 z-20">
          {/* use dark logo only for now */}
          <Image
            src={darkLogo}
            alt="Logo"
            width={124}
            height={24}
            // className="hidden dark:block"
          />
          {/* <Image
            src={lightLogo}
            alt="Logo"
            width={124}
            height={24}
            className="block dark:hidden"
          /> */}
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full max-w-[400px] flex-col justify-center space-y-6">
          {children}
          <p className="px-8 text-center text-muted-foreground text-sm">
            By clicking continue, you agree to our{' '}
            <Link
              href={new URL(
                '/legal/terms-of-service',
                env.NEXT_PUBLIC_WEB_URL
              ).toString()}
              className="underline underline-offset-4 hover:text-primary"
            >
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link
              href={new URL(
                '/legal/event-organizer-privacy-policy',
                env.NEXT_PUBLIC_WEB_URL
              ).toString()}
              className="underline underline-offset-4 hover:text-primary"
            >
              Privacy Policy
            </Link>
            .
          </p>
        </div>
        <div className="absolute top-4 right-4">
          <ModeToggle />
        </div>
      </div>
    </div>
  );
};

export default SignInLayout;
