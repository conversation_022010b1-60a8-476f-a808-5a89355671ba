import { createMetadata } from '@repo/seo/metadata';
import type { Metadata } from 'next';
import dynamic from 'next/dynamic';

const title = 'Thank you';
const description = 'Thank you for signing up.';
const SignUpSuccess = dynamic(() =>
  import('@repo/auth/components/sign-up-success').then(
    (mod) => mod.SignUpSuccess
  )
);

export const metadata: Metadata = createMetadata({ title, description });

export default function ThankYouPage() {
  return <SignUpSuccess />;
}
