import { organizerAuth } from '@repo/auth/organizer-auth';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import type { ReactNode } from 'react';

type AuthLayoutProps = {
  readonly children: ReactNode;
};

const AuthLayout = async ({ children }: AuthLayoutProps) => {
  // Check if user is already authenticated
  const session = await organizerAuth.api.getSession({
    headers: await headers(),
  });

  if (session?.user) {
    // If user is already authenticated, redirect to home page
    return redirect('/');
  }

  return children;
};

export default AuthLayout;
