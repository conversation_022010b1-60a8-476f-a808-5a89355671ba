'use client';

import { OnboardingForm } from '@repo/auth/components/onboarding';
import { toast } from '@repo/design-system/components/ui/sonner';
import { extractErrorMessage } from '@repo/design-system/lib/error-utils';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { completeOnboarding } from '../actions';

export function OnboardingFormClient() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);

  // Show success state instead of form after completion
  if (isCompleted) {
    return (
      <div className="flex flex-col items-center justify-center space-y-4 py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        <p className="text-muted-foreground">Redirecting to dashboard...</p>
      </div>
    );
  }

  return (
    <OnboardingForm
      isSubmitting={isSubmitting}
      onComplete={async (formData) => {
        try {
          setIsSubmitting(true);

          // Submit organizer details via server action
          const result = await completeOnboarding(formData);

          if (result.success) {
            toast.success('Onboarding completed successfully!');

            // Set completed state to show success message
            setIsCompleted(true);

            // Redirect to dashboard after successful onboarding
            router.push('/');
            router.refresh(); // Refresh to update session
            return;
          }

          throw new Error('Failed to complete onboarding');
        } catch (error) {
          log.warn('Failed to complete onboarding:', { error });

          // Use the centralized error message extraction utility
          const errorMessage = extractErrorMessage(
            error,
            'Failed to complete onboarding. Please try again.'
          );

          // Display the specific error message
          toast.error(errorMessage);

          // Only reset isSubmitting on error
          setIsSubmitting(false);
        }
      }}
      onError={(error) => {
        // Error handling is done in the component
        log.warn('Failed to complete onboarding:', { error });

        // Use the centralized error message extraction utility
        const errorMessage = extractErrorMessage(
          error,
          'Failed to complete onboarding. Please try again.'
        );

        // Display the specific error message
        toast.error(errorMessage);
      }}
    />
  );
}
