import { createMetadata } from '@repo/seo/metadata';
import type { Metadata } from 'next';
import dynamic from 'next/dynamic';

const title = 'Create your organization';
const description = 'Set up your organization to get started.';
const CreateOrganizationWrapper = dynamic(() =>
  import('../components/create-organization-wrapper').then(
    (mod) => mod.CreateOrganizationWrapper
  )
);

export const metadata: Metadata = createMetadata({ title, description });

const CreateOrganizationPage = () => {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="w-full max-w-md space-y-6 p-8">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="font-semibold text-2xl tracking-tight">{title}</h1>
          <p className="text-muted-foreground text-sm">{description}</p>
        </div>
        <div className="px-4">
          <CreateOrganizationWrapper />
        </div>
      </div>
    </div>
  );
};

export default CreateOrganizationPage;
