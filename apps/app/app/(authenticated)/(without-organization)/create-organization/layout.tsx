import { auth } from '@repo/auth/server';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import type { ReactNode } from 'react';

type CreateOrgLayoutProps = {
  readonly children: ReactNode;
};

const CreateOrgLayout = async ({ children }: CreateOrgLayoutProps) => {
  // Check if user is authenticated
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return redirect('/sign-in');
  }

  // Check if user already has an organization
  const organizations = await auth.api.listOrganizations({
    headers: await headers(),
  });

  if (organizations.length > 0) {
    return redirect('/');
  }

  return (
    <div className="flex h-screen w-full flex-col">
      <main className="flex flex-1 flex-col">{children}</main>
    </div>
  );
};

export default CreateOrgLayout;
