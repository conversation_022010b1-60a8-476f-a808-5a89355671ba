'use server';

import { DEFAULT_EVENT_TERMS } from '@/app/(authenticated)/(with-organization)/events/components/default-terms';
import {
  canAccessOrganizer,
  isOrganizer,
  isSuperAdmin,
  toAuthResult,
} from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import type { Prisma } from '@repo/database/types';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

// Helper function to get dates between two dates
function getDatesInRange(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = [];
  const currentDate = new Date(startDate);

  // Strip time part for date comparison
  const endDateWithoutTime = new Date(endDate);
  endDateWithoutTime.setHours(0, 0, 0, 0);

  // Set time to beginning of day for comparison
  currentDate.setHours(0, 0, 0, 0);

  // Add each date in the range
  while (currentDate <= endDateWithoutTime) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

// TODO: start/end date should be > today
export async function createEvent(values: Prisma.EventUncheckedCreateInput) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    throw new Error('Unauthorized');
  }

  // Super admins can create events for any organizer
  // Regular users must be organizers
  if (
    !isSuperAdmin(toAuthResult(session)) &&
    !isOrganizer(toAuthResult(session))
  ) {
    throw new Error('Not an organizer');
  }

  // Use provided organizerId or fall back to session organizerId
  const organizerId = values.organizerId || session.session?.organizerId;

  if (!organizerId) {
    throw new Error('Organizer ID is required');
  }

  // Get the organizer
  const organizer = await database.organizer.findUnique({
    where: { id: organizerId },
  });

  if (!organizer) {
    throw new Error('Organizer not found');
  }

  // Handle premium event creation
  if (values.isPremiumEvent && !values.premiumTierId) {
    throw new Error('Premium tier must be specified for premium events');
  }

  // For premium events, we'll create the event first, then handle the premium upgrade separately
  // The EventPremiumUpgrade model will track the premium status

  // Enforce ticket limits for free users
  let maxTickets = values.maxTicketsPerEvent || 20;
  if (!values.isPremiumEvent && maxTickets > 20) {
    maxTickets = 20; // Enforce 20 ticket limit for free events
  }

  // Handle slug uniqueness
  let finalSlug = values.slug;
  let counter = 1;

  // Check if the slug already exists
  while (await database.event.findUnique({ where: { slug: finalSlug } })) {
    // Extract the parts (format is 'prefix-userpart')
    const parts = finalSlug.split('-');
    if (parts.length >= 2) {
      const prefix = parts[0];
      const userPart = parts.slice(1).join('-');

      // Append counter to the user part
      finalSlug = `${prefix}-${userPart}-${counter}`;
    } else {
      // Fallback if the slug doesn't have the expected format
      finalSlug = `${finalSlug}-${counter}`;
    }
    counter++;
  }

  // Create the event first
  const newEvent = await database.event.create({
    data: {
      ...values,
      slug: finalSlug, // Use the unique slug
      organizerId: organizer.id,
      maxTicketsPerEvent: maxTickets,
      isPremiumEvent: !!values.isPremiumEvent,
      terms: JSON.stringify(DEFAULT_EVENT_TERMS), // default event tnc
    },
  });

  // Get the dates between startTime and endTime
  if (values.startTime && values.endTime) {
    const startTime = new Date(values.startTime);
    const endTime = new Date(values.endTime);

    // Get all dates between start and end
    const dates = getDatesInRange(startTime, endTime);

    // Create an EventDate for each day with a TimeSlot
    for (const date of dates) {
      // For each date, create a time slot with the same time part as the original event
      const dateStartTime = new Date(date);
      dateStartTime.setHours(
        startTime.getHours(),
        startTime.getMinutes(),
        startTime.getSeconds()
      );

      const dateEndTime = new Date(date);
      dateEndTime.setHours(
        endTime.getHours(),
        endTime.getMinutes(),
        endTime.getSeconds()
      );

      // Create the EventDate with TimeSlot
      await database.eventDate.create({
        data: {
          eventId: newEvent.id,
          date: date,
          // Just use the same time as event on default
          timeSlots: {
            create: [
              {
                startTime: dateStartTime,
                endTime: dateEndTime,
                doorsOpen: dateStartTime,
              },
            ],
          },
        },
      });
    }
  }

  revalidatePath('/events');

  return {
    success: true,
    data: serializePrisma(newEvent),
  };
}

export async function updateEvent(
  id: string,
  values: Prisma.EventUncheckedUpdateInput
) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    throw new Error('Unauthorized');
  }

  // Get the current event to check premium status change && time changes
  const currentEvent = await database.event.findUnique({
    where: { id },
    select: {
      slug: true,
      isPremiumEvent: true,
      organizerId: true,
      startTime: true,
      endTime: true,
    },
  });

  if (!currentEvent) {
    throw new Error('Event not found');
  }

  // Check if user has access to update this event
  if (!canAccessOrganizer(toAuthResult(session), currentEvent.organizerId)) {
    throw new Error('Unauthorized: You can only update your own events');
  }

  // Check if upgrading to premium event
  if (
    !currentEvent.isPremiumEvent &&
    values.isPremiumEvent &&
    !values.premiumTierId
  ) {
    throw new Error('Premium tier must be specified for premium events');
  }

  // For premium events, the EventPremiumUpgrade model will track the premium status
  // No need to check organizer status as we're using the premium upgrade model

  // Enforce ticket limits for free events
  let maxTickets = values.maxTicketsPerEvent as number;
  if (maxTickets && !values.isPremiumEvent && maxTickets > 20) {
    maxTickets = 20; // Enforce 20 ticket limit for free events
  }

  // Handle slug uniqueness
  let finalSlug = values.slug as string;
  let counter = 1;
  const slugChanged = finalSlug !== currentEvent.slug;

  if (finalSlug && slugChanged) {
    // Check if the slug already exists
    while (await database.event.findUnique({ where: { slug: finalSlug } })) {
      // Extract the parts (format is 'prefix-userpart')
      const parts = finalSlug.split('-');
      if (parts.length >= 2) {
        const prefix = parts[0];
        const userPart = parts.slice(1).join('-');

        // Append counter to the user part
        finalSlug = `${prefix}-${userPart}-${counter}`;
      } else {
        // Fallback if the slug doesn't have the expected format
        finalSlug = `${finalSlug}-${counter}`;
      }
      counter++;
    }
  }

  // Get the new start and end times
  const oldStartTime = new Date(currentEvent.startTime);
  const oldEndTime = new Date(currentEvent.endTime);
  const newStartTime = new Date(values.startTime as string);
  const newEndTime = new Date(values.endTime as string);

  // Check if start or end time has changed by comparing timestamps
  const startTimeChanged = oldStartTime.getTime() !== newStartTime.getTime();
  const endTimeChanged = oldEndTime.getTime() !== newEndTime.getTime();

  // Update event dates if start or end time has changed
  if (
    (startTimeChanged || endTimeChanged) &&
    values.startTime &&
    values.endTime
  ) {
    // First, delete all existing event dates for this event
    await database.eventDate.deleteMany({
      where: {
        eventId: id,
      },
    });

    // Get all dates between new start and end
    const dates = getDatesInRange(newStartTime, newEndTime);

    // Create new event dates with time slots
    for (const date of dates) {
      // For each date, create a time slot with the same time part as the updated event
      const dateStartTime = new Date(date);
      dateStartTime.setHours(
        newStartTime.getHours(),
        newStartTime.getMinutes(),
        newStartTime.getSeconds()
      );
      const dateEndTime = new Date(date);
      dateEndTime.setHours(
        newEndTime.getHours(),
        newEndTime.getMinutes(),
        newEndTime.getSeconds()
      );
      // Create the EventDate with TimeSlot
      await database.eventDate.create({
        data: {
          eventId: id,
          date: date,
          timeSlots: {
            create: [
              {
                startTime: dateStartTime,
                endTime: dateEndTime,
                doorsOpen: dateStartTime,
              },
            ],
          },
        },
      });
    }
  }

  const updatedEvent = await database.event.update({
    where: { id },
    data: {
      ...values,
      ...(finalSlug && { slug: finalSlug }),
      ...(maxTickets && { maxTicketsPerEvent: maxTickets }),
    },
  });

  revalidatePath('/events');
  revalidatePath(`/events${updatedEvent.slug}`);

  return {
    success: true,
    data: serializePrisma(updatedEvent),
    redirect: slugChanged ? `/events/${updatedEvent.slug}` : undefined,
  };
}
