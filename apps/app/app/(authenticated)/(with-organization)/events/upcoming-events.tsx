import { database } from '@repo/database';
import {
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  StarIcon,
} from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDate, formatTime } from '@repo/design-system/lib/format';
import Link from 'next/link';

interface UpcomingEventsProps {
  organizerId?: string | null;
}

export const UpcomingEvents = async ({ organizerId }: UpcomingEventsProps) => {
  // Null means no access; show empty state and skip querying
  if (organizerId === null) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <p className="font-medium text-lg text-muted-foreground">
          No upcoming events
        </p>
        <p className="text-muted-foreground text-sm">
          Create a new event to get started
        </p>
      </div>
    );
  }

  const events = await database.event.findMany({
    where: {
      startTime: {
        gte: new Date(),
      },
      ...(organizerId ? { organizerId: organizerId } : {}),
    },
    select: {
      id: true,
      slug: true,
      title: true,
      description: true,
      startTime: true,
      venueName: true,
      venueAddress: true,
      // venue: {
      //   select: {
      //     name: true,
      //   },
      // },
      isPremiumEvent: true,
      maxTicketsPerEvent: true,
    },
    orderBy: {
      startTime: 'desc',
    },
  });

  if (events.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <p className="font-medium text-lg text-muted-foreground">
          No upcoming events
        </p>
        <p className="text-muted-foreground text-sm">
          Create a new event to get started
        </p>
      </div>
    );
  }

  return (
    <div className="grid min-h-[100vh] flex-1 auto-rows-max gap-4 md:min-h-min lg:grid-cols-3">
      {events.map((event) => (
        <Link key={event.id} href={`/events/${event.slug}`}>
          <Card className="transition-shadow hover:shadow-md h-full">
            <CardHeader className="font-semibold text-lg">
              <div className="flex items-start justify-between">
                <CardTitle>{event.title}</CardTitle>
                {event.isPremiumEvent && (
                  <Badge variant="premium" className="ml-2">
                    <StarIcon className="h-3 w-3" />
                    Premium
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-1 text-sm">
              <p className="flex items-center">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formatDate(event.startTime, 'dd-MM-yyyy')}
              </p>
              <p className="flex items-center">
                <ClockIcon className="mr-2 h-4 w-4" />
                {formatTime(event.startTime)}
              </p>
              <div className="flex items-start">
                <MapPinIcon className="mt-1 mr-2 h-4 w-4" />
                <div className="flex-1">
                  <p>{event.venueName}</p>
                  <p className="text-muted-foreground">{event.venueAddress}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
};
