import { getOrganizer } from '@/app/(authenticated)/(with-organization)/admin/organizers/actions';
import { EventEditorialsManager } from '@/app/(authenticated)/(with-organization)/events/components/event-editorials-manager';
import type { SerializedEvent, SerializedOrganizer } from '@/types';
import { auth } from '@repo/auth/server';
import { ExternalLink, StarIcon } from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { log } from '@repo/observability/log';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { EventDeleteDialog } from '../components/event-delete-dialog';
import { EventDialog } from '../components/event-dialog';
import { getEvent } from './actions';
import { EventDates } from './components/event-dates';
import { EventModules } from './components/event-modules';
import { EventReports } from './components/event-reports';
import { EventStatusDialog } from './components/event-status';
import { EventSummary } from './components/event-summary';
import { EventTickets } from './components/event-tickets';
import { EventUpgradeDialog } from './components/event-upgrade-dialog';
import { EventVisibilityDialog } from './components/event-visibility';
import {
  ScrollArea,
  ScrollBar,
} from '@repo/design-system/components/ui/scroll-area';
import { MobileEventActions } from './components/mobile-event-actions';

type PageProps = {
  readonly params: Promise<{
    slug: string;
  }>;
};

export default async function EventDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { slug } = await params;

  // Get event by slug
  let event: SerializedEvent;
  let eventOrganizer: SerializedOrganizer;
  try {
    const sessionData = await auth.api.getSession({
      headers: await headers(),
    });
    const { organizerId } = sessionData?.session ?? {};

    const [eventResponse, eventOrganizerResponse] = await Promise.all([
      getEvent(slug),
      getOrganizer(organizerId ?? ''),
    ]);

    if (!eventResponse || !eventOrganizerResponse) {
      if (!eventResponse) {
        log.error('eventResponse is null');
      }
      if (!eventOrganizerResponse) {
        log.error('eventOrganizerResponse is null');
      }
      notFound();
    }

    event = eventResponse;
    eventOrganizer = eventOrganizerResponse;
  } catch (error) {
    log.error('Failed to fetch event', { error });
    notFound();
  }

  return (
    <>
      {/* <UpgradeNotification /> */}

      <Header pages={['Events']} page={event.title} />

      <main className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between">
          <div className="flex flex-col-reverse lg:flex-row items-start lg:items-center justify-start gap-2">
            <a
              href={`${process.env.NEXT_PUBLIC_WEB_URL}/events/${event.slug}`}
              target="_blank"
              rel="noreferrer"
              className="hover:underline"
            >
              <div className="flex space-x-2">
                <h2 className="font-bold text-3xl tracking-tight">
                  {event.title}
                </h2>
                <ExternalLink className="size-[16px]" />
              </div>
            </a>
            {event.isPremiumEvent && (
              <Badge variant="premium" className="lg:ml-2">
                <StarIcon className="h-3 w-3 fill-white" />
                Premium Event
              </Badge>
            )}
          </div>
          <EventUpgradeDialog event={event} />
        </div>
        <Tabs defaultValue="summary">
          <div className="flex items-center justify-between space-x-2">
            <ScrollArea className="flex-1">
              <TabsList>
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="editorial">Editorial</TabsTrigger>
                <TabsTrigger value="dates">Time Slots</TabsTrigger>
                <TabsTrigger value="tickets">Tickets</TabsTrigger>
                <TabsTrigger value="reports">Reports</TabsTrigger>
                <TabsTrigger value="modules">Modules</TabsTrigger>
              </TabsList>
              <ScrollBar orientation="horizontal" />
            </ScrollArea>

            {/* Desktop: Show individual buttons */}
            <div className="hidden items-center gap-2 lg:flex">
              <EventVisibilityDialog event={event} />
              <EventStatusDialog event={event} />
              <EventDialog mode="edit" event={event} />
              <EventDeleteDialog event={event} />
            </div>

            {/* Mobile: Show dropdown menu */}
            <div className="flex lg:hidden">
              <MobileEventActions event={event} />
            </div>
          </div>

          <TabsContent value="summary" className="mt-6 space-y-4">
            <EventSummary event={event} />
          </TabsContent>
          <TabsContent value="editorial" className="mt-6 space-y-4">
            <EventEditorialsManager event={event} />
          </TabsContent>
          <TabsContent value="dates" className="mt-6">
            <EventDates event={event} />
          </TabsContent>
          <TabsContent value="tickets" className="mt-6">
            <EventTickets event={event} />
          </TabsContent>
          <TabsContent value="reports" className="mt-6">
            <EventReports event={event} />
          </TabsContent>
          <TabsContent value="modules" className="mt-6">
            <EventModules event={event} eventOrganizer={eventOrganizer} />
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
