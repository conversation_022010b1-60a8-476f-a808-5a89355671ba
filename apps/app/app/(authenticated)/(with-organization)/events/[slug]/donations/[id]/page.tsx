import { Header } from '@/app/(authenticated)/(with-organization)/components/header';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { notFound } from 'next/navigation';
import { getEvent } from '../../actions';
import { getDonation } from './action';
import { DonationDetails } from './components/donation-details';
import { DonationSummary } from './components/donation-summary';
import { DonorInfo } from './components/donor-info';

export default async function DonationDetailPage({
  params,
}: {
  params: { slug: string; id: string };
}) {
  const { slug, id } = await params;
  const event = await getEvent(slug);

  if (!event) {
    notFound();
  }

  const donation = await getDonation(id, event.id);

  if (!donation) {
    notFound();
  }

  return (
    <>
      <Header pages={['Events', event.slug, 'Donations']} page={donation.id} />

      <div className="flex-1 space-y-6 p-4 pt-6 lg:p-8">
        {/* Donation Summary Section */}
        <DonationSummary donation={donation} />

        {/* Donation Details Tabs */}
        <Tabs defaultValue="details" className="w-full">
          <TabsList>
            <TabsTrigger value="details">Donation Details</TabsTrigger>
            <TabsTrigger value="donor">Donor Information</TabsTrigger>
          </TabsList>

          {/* Donation Details Tab */}
          <TabsContent value="details" className="space-y-4 pt-4">
            <DonationDetails donation={donation} />
          </TabsContent>

          {/* Donor Information Tab */}
          <TabsContent value="donor" className="space-y-4 pt-4">
            <DonorInfo donation={donation} />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
