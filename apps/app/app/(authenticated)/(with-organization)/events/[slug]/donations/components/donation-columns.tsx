'use client';

import type { SerializedDonation } from '@/types';
import type { DonationStatus } from '@prisma/client';
import { Badge } from '@repo/design-system/components/ui/badge';
import { formatCurrency, formatDate } from '@repo/design-system/lib/format';
import type { ColumnDef } from '@tanstack/react-table';
import { title } from 'radash';

export const DonationColumns: ColumnDef<SerializedDonation>[] = [
  {
    id: 'donor',
    header: 'Donor',
    cell: ({ row }) => {
      const name = row.original.name;
      const email = row.original.email;

      return (
        <div className="space-y-1">
          <div className="font-medium">{name}</div>
          <div className="text-muted-foreground text-sm">{email}</div>
        </div>
      );
    },
    accessorFn: (row) => `${row.name} ${row.email}`,
  },
  {
    accessorKey: 'companyName',
    header: 'Company',
  },
  {
    accessorKey: 'amount',
    header: 'Amount',
    cell: ({ row }) => {
      const amount = Number.parseFloat(row.getValue('amount'));
      return formatCurrency(amount);
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as DonationStatus;

      return <Badge variant={status}>{title(status)}</Badge>;
    },
  },
  {
    accessorKey: 'donatedAt',
    header: 'Date',
    cell: ({ row }) => {
      const date = new Date(row.getValue('donatedAt'));
      return formatDate(date);
    },
  },
];
