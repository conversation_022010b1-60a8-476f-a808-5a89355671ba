import type { SerializedDonation } from '@/types';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import Image from 'next/image';

interface DonorInfoProps {
  donation: SerializedDonation;
}

export function DonorInfo({ donation }: DonorInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Donor Information</CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-4">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Name</dt>
            <dd>{donation.name}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Email</dt>
            <dd>{donation.email}</dd>
          </div>
          {donation.phone && (
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Phone
              </dt>
              <dd>{donation.phone}</dd>
            </div>
          )}
          {donation.companyName && (
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Company
              </dt>
              <dd>{donation.companyName}</dd>
            </div>
          )}
          {donation.companyLogo && (
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Company Logo
              </dt>
              <dd>
                <Image
                  src={donation.companyLogo}
                  alt={`${donation.companyName || 'Company'} logo`}
                  width={100}
                  height={50}
                  className="h-12 w-auto object-contain"
                />
              </dd>
            </div>
          )}
          {donation.message && (
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Message
              </dt>
              <dd className="whitespace-pre-line">{donation.message}</dd>
            </div>
          )}
        </dl>
      </CardContent>
    </Card>
  );
}
