'use client';

import { Loader2, Send } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { sendEmail } from '../action';

interface SendEmailButtonProps {
  donationId: string;
}

export function SendDonationConfirmationButton({
  donationId,
}: SendEmailButtonProps) {
  const [isSending, setIsSending] = useState<boolean>(false);

  const handleSendEmail = async () => {
    try {
      setIsSending(true);
      toast.loading('Sending Donation Confirmation Email', {
        id: 'donation-confirmation',
      });

      const response = await sendEmail(donationId);

      if (response.success) {
        toast.success('Email successfully sent', {
          id: 'donation-confirmation',
        });
      } else {
        console.error('Error sending email:', response.error);
        toast.error(
          response.error || 'Failed to send email. Please try again.',
          {
            id: 'donation-confirmation',
          }
        );
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send email. Please try again.', {
        id: 'donation-confirmation',
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="gap-2"
      onClick={handleSendEmail}
      disabled={isSending}
    >
      {isSending ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          Sending...
        </>
      ) : (
        <>
          <Send className="h-4 w-4" />
          Send Donation Confirmation
        </>
      )}
    </Button>
  );
}
