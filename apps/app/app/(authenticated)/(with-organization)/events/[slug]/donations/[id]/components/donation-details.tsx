import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDate } from '@repo/design-system/lib/format';
import { formatCurrency } from '@repo/design-system/lib/format';

import type { SerializedDonation } from '@/types';

interface DonationDetailsProps {
  donation: SerializedDonation;
}

export function DonationDetails({ donation }: DonationDetailsProps) {
  // Get variant for status badge
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'destructive';
      case 'void':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Get variant for payment status badge
  const getPaymentStatusVariant = (status: string) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'cancelled':
        return 'destructive';
      case 'custom':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Donation Details</CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-4">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Amount
            </dt>
            <dd className="font-bold text-xl">
              {formatCurrency(donation.amount)}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Date</dt>
            <dd>{formatDate(donation.donatedAt)}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Status
            </dt>
            <dd>
              <Badge variant={getStatusVariant(donation.status)}>
                {donation.status.charAt(0).toUpperCase() +
                  donation.status.slice(1)}
              </Badge>
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Payment Status
            </dt>
            <dd>
              <Badge variant={getPaymentStatusVariant(donation.paymentStatus)}>
                {donation.paymentStatus.charAt(0).toUpperCase() +
                  donation.paymentStatus.slice(1)}
              </Badge>
            </dd>
          </div>
          {donation.transactionId && (
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Transaction ID
              </dt>
              <dd className="font-mono text-sm">{donation.transactionId}</dd>
            </div>
          )}
          {donation.paymentMethod && (
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Payment Method
              </dt>
              <dd>{donation.paymentMethod}</dd>
            </div>
          )}
        </dl>
      </CardContent>
    </Card>
  );
}
