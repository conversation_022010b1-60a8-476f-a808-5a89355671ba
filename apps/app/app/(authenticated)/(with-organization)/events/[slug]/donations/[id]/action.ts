'use server';

import { database, serializePrisma } from '@repo/database';
import { sendDonationConfirmationEmail } from '@repo/email/utils/donation-confirmation';
import { log } from '@repo/observability/log';

export async function getDonation(donationId: string, eventId: string) {
  try {
    const donation = await database.eventDonation
      .findUnique({
        where: {
          id: donationId,
          eventId,
        },
        select: {
          id: true,
          donationModuleId: true,
          eventId: true,
          name: true,
          email: true,
          phone: true,
          companyName: true,
          companyLogo: true,
          message: true,
          amount: true,
          transactionId: true,
          paymentMethod: true,
          status: true,
          paymentStatus: true,
          donatedAt: true,
          createdAt: true,
          updatedAt: true,
        },
      })
      .then((donations) => serializePrisma(donations));

    if (!donation) {
      return null;
    }

    return donation;
  } catch (error) {
    log.error('Failed to fetch donation details', {
      error,
      donationId,
      eventId,
    });
    throw new Error('Failed to fetch donation details. Please try again.');
  }
}

/**
 * Send a donation confirmation email
 * @param donationId The ID of the donation to send confirmation for
 * @returns A promise that resolves to the email sending result
 */
export async function sendEmail(donationId: string) {
  try {
    // Check if donation exists
    const donation = await database.eventDonation.findUnique({
      where: { id: donationId },
    });

    if (!donation) {
      return { success: false, error: 'Donation not found' };
    }

    // Send confirmation email
    await sendDonationConfirmationEmail(donationId);

    return { success: true };
  } catch (error) {
    log.error(`Error sending donation confirmation email: ${error}`, {
      donationId,
      error,
    });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
