'use client';

import { DownloadIcon, Loader2 } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import { toast } from '@repo/design-system/components/ui/sonner';
import { downloadDonationReceipt } from '@repo/email/utils/pdf';
import { useState } from 'react';

interface DownloadReceiptButtonProps {
  donationId: string;
}

export function DownloadReceiptButton({
  donationId,
}: DownloadReceiptButtonProps) {
  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  const handleDownloadReceipt = async () => {
    try {
      setIsDownloading(true);
      const downloadUrl = await downloadDonationReceipt(donationId);

      // Open the PDF in a new tab
      if (downloadUrl) {
        window.open(downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Error downloading receipt:', error);
      toast.error('Unable to download receipt. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      className="gap-2"
      onClick={handleDownloadReceipt}
      disabled={isDownloading}
    >
      {isDownloading ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          Downloading...
        </>
      ) : (
        <>
          <DownloadIcon className="h-4 w-4" />
          Download Receipt
        </>
      )}
    </Button>
  );
}
