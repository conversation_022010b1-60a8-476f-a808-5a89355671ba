'use server';

import { database, serializePrisma } from '@repo/database';
import { MAX_ROWS } from '@repo/database/types';
import { log } from '@repo/observability/log';

export async function getEventDonations(eventId: string) {
  try {
    const donations = await database.eventDonation
      .findMany({
        where: {
          eventId,
        },
        select: {
          id: true,
          donationModuleId: true,
          eventId: true,
          name: true,
          email: true,
          phone: true,
          companyName: true,
          companyLogo: true,
          message: true,
          amount: true,
          transactionId: true,
          paymentMethod: true,
          status: true,
          paymentStatus: true,
          donatedAt: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: { donatedAt: 'desc' },
        take: MAX_ROWS,
      })
      .then((donations) => serializePrisma(donations));

    return donations;
  } catch (error) {
    log.error('Failed to fetch event donations', { error });
    throw new Error('Failed to fetch donations. Please try again.');
  }
}
