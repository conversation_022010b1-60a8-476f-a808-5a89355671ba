import { DownloadReceiptButton } from '@/app/(authenticated)/(with-organization)/events/[slug]/donations/[id]/components/download-receipt-button';
import { SendDonationConfirmationButton } from '@/app/(authenticated)/(with-organization)/events/[slug]/donations/[id]/components/send-donation-confirmation-button';
import type { SerializedDonation } from '@/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDate } from '@repo/design-system/lib/format';
import { formatCurrency } from '@repo/design-system/lib/format';

interface DonationSummaryProps {
  donation: SerializedDonation;
}

export function DonationSummary({ donation }: DonationSummaryProps) {
  // Get variant for status badge
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'destructive';
      case 'void':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Card className="md:col-span-4">
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle className="text-2xl">Donation #{donation.id}</CardTitle>
          <Badge variant={getStatusVariant(donation.status)}>
            {donation.status.charAt(0).toUpperCase() + donation.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Donation Date</p>
            <p className="font-medium">{formatDate(donation.donatedAt)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Amount</p>
            <p className="font-medium text-lg">
              {formatCurrency(donation.amount)}
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Donor</p>
            <p className="font-medium">{donation.name}</p>
            <p className="text-muted-foreground text-sm">{donation.email}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <SendDonationConfirmationButton donationId={donation.id} />
        <DownloadReceiptButton donationId={donation.id} />
      </CardFooter>
    </Card>
  );
}
