'use client';

import { useState } from 'react';
import type { SerializedEvent } from '@/types';
import {
  PencilIcon,
  Trash,
  Eye,
  Settings,
} from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@repo/design-system/components/ui/dropdown-menu';
import { MoreHorizontal } from 'lucide-react';
import { EventDialog } from '../../components/event-dialog';
import { EventDeleteDialog } from '../../components/event-delete-dialog';
import { MobileVisibilityDialog } from './mobile-visibility-dialog';
import { MobileStatusDialog } from './mobile-status-dialog';

interface MobileEventActionsProps {
  event: SerializedEvent;
}

export function MobileEventActions({ event }: MobileEventActionsProps) {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [visibilityDialogOpen, setVisibilityDialogOpen] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">More actions</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onSelect={() => setVisibilityDialogOpen(true)}>
            <Eye className="mr-2 h-4 w-4" />
            Change Visibility
          </DropdownMenuItem>
          <DropdownMenuItem onSelect={() => setStatusDialogOpen(true)}>
            <Settings className="mr-2 h-4 w-4" />
            Change Status
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onSelect={() => setEditDialogOpen(true)}>
            <PencilIcon className="mr-2 h-4 w-4" />
            Edit Event
          </DropdownMenuItem>
          <DropdownMenuItem
            onSelect={() => setDeleteDialogOpen(true)}
            className="text-destructive focus:text-destructive"
          >
            <Trash className="mr-2 h-4 w-4" />
            Delete Event
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Mobile dialogs */}
      <MobileVisibilityDialog
        event={event}
        open={visibilityDialogOpen}
        onOpenChange={setVisibilityDialogOpen}
      />

      <MobileStatusDialog
        event={event}
        open={statusDialogOpen}
        onOpenChange={setStatusDialogOpen}
      />

      {/* Controlled dialogs */}
      <EventDialog
        trigger={<div />}
        mode="edit"
        event={event}
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
      />

      <EventDeleteDialog
        trigger={<div />}
        event={event}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
      />
    </>
  );
}
