'use server';

import type { Prisma } from '@prisma/client';
import { database, serializePrisma } from '@repo/database';
import { revalidatePath } from 'next/cache';

export async function createInventory(
  slug: string,
  ticketTypeId: string,
  timeSlotIds: string[],
  quantity: number
) {
  try {
    if (!timeSlotIds.length) {
      throw new Error('No time slots selected');
    }

    if (quantity <= 0) {
      throw new Error('Quantity must be greater than 0');
    }

    // Use a transaction for consistency
    const createdInventories = await database.$transaction(async (tx) => {
      // Check if any inventory already exists for these time slots and ticket type
      const existingInventories = await tx.inventory.findMany({
        where: {
          ticketTypeId,
          timeSlotId: {
            in: timeSlotIds,
          },
        },
      });

      // If any exist, throw an error with details
      if (existingInventories.length > 0) {
        const existingTimeSlotIds = existingInventories.map(
          (inv) => inv.timeSlotId
        );
        throw new Error(
          `Inventory already exists for ${existingTimeSlotIds.length} of the selected time slots`
        );
      }

      // Create inventory items for each time slot
      const createdItems: Prisma.InventoryCreateManyInput[] = [];
      for (const timeSlotId of timeSlotIds) {
        const inventory = await tx.inventory.create({
          data: {
            ticketTypeId,
            timeSlotId,
            quantity,
          },
          include: {
            timeSlot: {
              select: {
                id: true,
                startTime: true,
                endTime: true,
                doorsOpen: true,
                eventDate: {
                  select: {
                    date: true,
                  },
                },
                _count: {
                  select: { tickets: true },
                },
              },
            },
          },
        });
        createdItems.push(inventory);
      }

      return createdItems;
    });

    // Revalidate paths
    revalidatePath(`/events/${decodeURIComponent(slug)}`);

    return {
      success: true,
      data: serializePrisma(createdInventories),
      message: `Created ${createdInventories.length} inventory items successfully`,
    };
  } catch (error) {
    console.error('Error creating inventory:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to create inventory',
    };
  }
}

export async function fetchInventory(ticketTypeId: string) {
  try {
    // Fetch inventory items with related time slot data for context
    const inventoryItems = await database.inventory.findMany({
      where: {
        ticketTypeId,
      },
      include: {
        timeSlot: {
          select: {
            id: true,
            startTime: true,
            endTime: true,
            doorsOpen: true,
            eventDate: {
              select: {
                date: true,
              },
            },
          },
        },
      },
    });

    // Return serialized data
    return {
      success: true,
      data: serializePrisma(inventoryItems),
    };
  } catch (error) {
    console.error('Error fetching inventory:', error);
    return {
      success: false,
      error: 'Failed to fetch inventory',
      data: [],
    };
  }
}

export async function updateInventory(
  slug: string,
  inventoryId: string,
  newQuantity: number
) {
  try {
    // Use a transaction for consistency
    await database.$transaction(async (tx) => {
      // First get the current inventory to check tickets sold
      const inventory = await tx.inventory.findUnique({
        where: { id: inventoryId },
        include: {
          timeSlot: {
            include: {
              _count: {
                select: { tickets: true },
              },
            },
          },
        },
      });

      if (!inventory) {
        throw new Error('Inventory not found');
      }

      // Verify the update doesn't reduce below tickets sold
      const ticketsSold = inventory.timeSlot?._count?.tickets || 0;
      if (newQuantity < ticketsSold) {
        throw new Error(
          `Cannot reduce inventory below tickets sold (${ticketsSold})`
        );
      }

      // If validation passes, perform the update
      return tx.inventory.update({
        where: { id: inventoryId },
        data: { quantity: newQuantity },
      });
    });

    // Revalidate paths
    revalidatePath(`/events/${decodeURIComponent(slug)}`);

    return { success: true };
  } catch (error) {
    console.error('Error updating inventory:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to update inventory',
    };
  }
}

export async function deleteInventory(slug: string, inventoryId: string) {
  try {
    // Use a transaction for consistency
    await database.$transaction(async (tx) => {
      // First get the current inventory to check tickets sold
      const inventory = await tx.inventory.findUnique({
        where: { id: inventoryId },
        include: {
          timeSlot: {
            include: {
              _count: {
                select: { tickets: true },
              },
            },
          },
        },
      });

      if (!inventory) {
        throw new Error('Inventory not found');
      }

      // Verify no tickets have been sold
      const ticketsSold = inventory.timeSlot?._count?.tickets || 0;
      if (ticketsSold > 0) {
        throw new Error(
          `Cannot delete inventory with tickets sold (${ticketsSold})`
        );
      }

      // If validation passes, perform the deletion
      return tx.inventory.delete({
        where: { id: inventoryId },
      });
    });

    // Revalidate paths
    revalidatePath(`/events/${decodeURIComponent(slug)}`);

    return { success: true };
  } catch (error) {
    console.error('Error deleting inventory:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to delete inventory',
    };
  }
}
