'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { z } from 'zod';
import { createTicket } from '../actions';

const ticketFormSchema = z.object({
  ownerName: z.string().min(1, 'Name is required'),
  ownerEmail: z.string().email('Invalid email address'),
  ownerPhone: z.string().optional(),
  ticketType: z.string().min(1, 'Ticket type is required'),
});

type TicketFormValues = z.infer<typeof ticketFormSchema>;

interface TicketDialogProps {
  eventId: string;
}

export function TicketDialog({ eventId }: TicketDialogProps) {
  const form = useForm<TicketFormValues>({
    resolver: zodResolver(ticketFormSchema),
    defaultValues: {
      ownerName: '',
      ownerEmail: '',
      ownerPhone: '',
      ticketType: 'general',
    },
  });

  async function onSubmit(values: TicketFormValues) {
    try {
      await createTicket(eventId, values);
      form.reset();
    } catch (error) {
      console.error('Failed to create ticket:', error);
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="sm">
          <Plus className="mr-2 h-4 w-4" />
          Add Ticket
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Ticket</DialogTitle>
          <DialogDescription>
            Add a new ticket for this event. Fill in the user details below.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="ownerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John Doe" {...field} />
                  </FormControl>
                  <FormDescription>
                    Full name of the ticket holder
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ownerEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Email address for ticket delivery
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ownerPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="+1234567890" {...field} />
                  </FormControl>
                  <FormDescription>
                    Contact number for important updates
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="ticketType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ticket Type</FormLabel>
                  <FormControl>
                    <Input placeholder="General Admission" {...field} />
                  </FormControl>
                  <FormDescription>Type of ticket being issued</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="submit">Create Ticket</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
