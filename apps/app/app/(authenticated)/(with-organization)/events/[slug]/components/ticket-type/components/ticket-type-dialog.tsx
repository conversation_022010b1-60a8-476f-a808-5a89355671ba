'use client';
import { useIsPastEvent } from '@/app/hooks/use-is-past-event';
import type { SerializedEvent } from '@/types';
import { Edit } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import { title } from 'radash';
import React from 'react';
import { CreateTicketTypeForm } from './create-ticket-type-form';
import { EditTicketTypeForm } from './edit-ticket-type-form';

interface TicketTypeDialogProps {
  event: SerializedEvent;
  ticketType?: SerializedEvent['ticketTypes'][number];
  mode?: 'create' | 'edit';
}

export function TicketTypeDialog({
  event,
  ticketType,
  mode = 'create',
}: TicketTypeDialogProps) {
  const { id: eventId, startTime, endTime, maxTicketsPerEvent } = event;

  const isPastEvent = useIsPastEvent(startTime);
  const [open, setOpen] = React.useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {!isPastEvent && (
          <Button size="sm" variant={mode === 'edit' ? 'outline' : 'default'}>
            {mode === 'edit' && <Edit />}
            {title(mode)}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{title(mode)} Ticket Type</DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new ticket type for this event.'
              : 'Edit the selected ticket type.'}
          </DialogDescription>
        </DialogHeader>

        {mode === 'create' ? (
          <CreateTicketTypeForm
            setOpen={setOpen}
            eventId={eventId}
            startTime={startTime}
            endTime={endTime}
            maxTickets={maxTicketsPerEvent}
          />
        ) : (
          ticketType && (
            <EditTicketTypeForm
              setOpen={setOpen}
              ticketType={ticketType}
              eventId={eventId}
              maxTickets={event.maxTicketsPerEvent}
            />
          )
        )}
      </DialogContent>
    </Dialog>
  );
}
