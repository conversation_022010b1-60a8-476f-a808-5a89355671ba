'use client';

import type { SerializedEvent, SerializedOrganizer } from '@/types';
import { Button } from '@repo/design-system/components/ui/button';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Switch } from '@repo/design-system/components/ui/switch';
import Link from 'next/link';
import { useState } from 'react';
import { updateCustomPaymentModule } from '../action';

type CustomPaymentModuleSettingsProps = {
  event: SerializedEvent;
  eventOrganizer?: SerializedOrganizer;
};

export const CustomPaymentModuleSettings = ({
  event,
  eventOrganizer,
}: CustomPaymentModuleSettingsProps) => {
  const modules = event.eventModule;
  const [isUpdating, setIsUpdating] = useState(false);

  // Default values from event module or false if not set
  const [chipsEnabled, setChipsEnabled] = useState(
    modules?.customPaymentModule?.chipsEnabled || false
  );
  // const [stripeEnabled, setStripeEnabled] = useState(
  //   modules?.customPaymentModule?.stripeEnabled || false
  // );

  // Toggle handlers for custom payment module settings
  const handleChipsEnabledToggle = async (checked: boolean) => {
    setIsUpdating(true);
    try {
      setChipsEnabled(checked);
      await updateCustomPaymentModule(event.id, {
        chipsEnabled: checked,
        stripeEnabled: false, // TODO
      });
      toast.success(`CHIPS payment ${checked ? 'enabled' : 'disabled'}`);
    } catch (error) {
      setChipsEnabled(!checked); // Revert state on error
      toast.error(
        `Failed to update setting: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsUpdating(false);
    }
  };

  // const handleStripeEnabledToggle = async (checked: boolean) => {
  //   setIsUpdating(true);
  //   try {
  //     setStripeEnabled(checked);
  //     await updateCustomPaymentModule(event.id, {
  //       chipsEnabled,
  //       stripeEnabled: checked,
  //     });
  //     toast.success(`Stripe payment ${checked ? 'enabled' : 'disabled'}`);
  //   } catch (error) {
  //     setStripeEnabled(!checked); // Revert state on error
  //     toast.error(
  //       `Failed to update setting: ${error instanceof Error ? error.message : 'Unknown error'}`
  //     );
  //   } finally {
  //     setIsUpdating(false);
  //   }
  // };

  return (
    <div className="flex flex-col space-y-6 pt-2">
      <div className="flex items-start justify-between space-x-4">
        <div className="flex-1">
          <Label htmlFor="chips-enabled-toggle" className="font-medium text-sm">
            Enable custom CHIP Payment
          </Label>
          <p className="mt-1 text-muted-foreground text-xs">
            Use your own CHIP payment gateway account instead of the default
            TicketCARE CHIP payment gateway.
          </p>
        </div>
        {eventOrganizer?.chipSecretKey &&
        eventOrganizer?.chipBrandId &&
        eventOrganizer?.chipWebhookSecret ? (
          <Switch
            id="chips-enabled-toggle"
            checked={chipsEnabled}
            disabled={isUpdating}
            onCheckedChange={handleChipsEnabledToggle}
          />
        ) : (
          <Link href="/settings">
            <Button variant="secondary">Configure</Button>
          </Link>
        )}
      </div>

      <div className="flex items-start justify-between space-x-4">
        <div className="flex-1">
          <Label
            htmlFor="stripe-enabled-toggle"
            className="font-medium text-sm"
          >
            Enable Stripe Payment (Coming Soon)
          </Label>
          <p className="mt-1 text-muted-foreground text-xs">
            Use your own Stripe payment gateway account instead of the default
            TicketCARE Stripe payment gateway.
          </p>
        </div>
        <Button variant="secondary" disabled>
          Coming soon
        </Button>
        {/* <Switch
          id="stripe-enabled-toggle"
          checked={stripeEnabled}
          disabled
          onCheckedChange={handleStripeEnabledToggle}
        /> */}
      </div>
    </div>
  );
};
