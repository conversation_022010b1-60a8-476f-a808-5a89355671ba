'use client';

import { updateDonationModule } from '@/app/(authenticated)/(with-organization)/events/[slug]/components/modules/action';
import type { SerializedEvent } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { Editor } from '@repo/design-system/components/blocks/editor-00/editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { PencilIcon } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { useState } from 'react';
import * as z from 'zod';

// Define the form schema
const donationModuleSchema = z.object({
  minAmount: z.string().min(1, 'Minimum donation amount is required'),
  description: z.string().optional(),
  thankYouMessage: z.string().optional(),
});

type DonationModuleFormValues = z.infer<typeof donationModuleSchema>;

type DonationModuleDialogProps = {
  event: SerializedEvent;
};

export function DonationModuleDialog({ event }: DonationModuleDialogProps) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const donationModule = event.eventModule?.donationModule;

  const form = useForm<DonationModuleFormValues>({
    resolver: zodResolver(donationModuleSchema),
    defaultValues: {
      minAmount: donationModule?.minAmount?.toString() || '',
      description: donationModule?.description
        ? JSON.stringify(donationModule.description)
        : '',
      thankYouMessage: donationModule?.thankYouMessage || '',
    },
  });

  async function onSubmit(values: DonationModuleFormValues) {
    setIsSubmitting(true);
    try {
      await updateDonationModule(event.id, {
        ...values,
        minAmount: Number.parseFloat(values.minAmount),
      });
      toast.success('Donation module settings updated successfully');
      setOpen(false);
    } catch (error) {
      toast.error(
        `Failed to update donation module: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="ml-auto">
          <PencilIcon className="mr-2 h-4 w-4" />
          Edit Donation Settings
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Donation Module Settings</DialogTitle>
          <DialogDescription>
            Configure the donation module for this event.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="minAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Donation Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Leave empty for no minimum"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The minimum amount that can be donated (optional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Editor
                      editorSerializedState={parseEditorContent(field.value)}
                      onSerializedChange={(value) =>
                        field.onChange(JSON.stringify(value))
                      }
                    />
                  </FormControl>
                  <FormDescription>
                    Describe what the donations will be used for
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="thankYouMessage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Thank You Message</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Thank You Message"
                      className="resize-none"
                      {...field}
                      // Convert null to empty string
                      value={field.value ?? ''}
                    />
                  </FormControl>
                  <FormDescription>
                    Appreciation message to show at donation confirmation email.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
