'use client';
import { updateTicketTypeVisibility } from '@/app/(authenticated)/(with-organization)/events/[slug]/components/ticket-type/action';
import { Visibility } from '@prisma/client';
import { Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';

interface TicketTypeVisibilityProps {
  ticketType: {
    id: string;
    visibility: Visibility;
  };
  eventSlug: string;
}

export function TicketTypeVisibility({ ticketType, eventSlug }: TicketTypeVisibilityProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<Visibility | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleStatusChange = (newStatus: Visibility) => {
    setSelectedStatus(newStatus);
    setIsOpen(true);
  };

  const handleConfirm = async () => {
    if (selectedStatus) {
      setIsLoading(true);

      try {
        await updateTicketTypeVisibility(ticketType.id, eventSlug, selectedStatus);

        // Show success message with appropriate visibility text
        const visibilityText = {
          public: 'marked as public',
          private: 'marked as private',
          unlisted: 'marked as unlisted',
        }[selectedStatus];

        toast.success(`Ticket type ${visibilityText} successfully`);
      } catch (error) {
        // Show error message
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to update ticket type visibility. Please try again.';

        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    }
  };

  return (
    <>
      <Select
        value={ticketType.visibility}
        onValueChange={handleStatusChange}
        disabled={isLoading}
      >
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder="Select visibility" />
          {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={Visibility.public}>Public</SelectItem>
          <SelectItem value={Visibility.private}>Private</SelectItem>
          <SelectItem value={Visibility.unlisted}>Unlisted</SelectItem>
        </SelectContent>
      </Select>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Ticket Type Visibility</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the ticket type visibility to{' '}
              {selectedStatus}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
