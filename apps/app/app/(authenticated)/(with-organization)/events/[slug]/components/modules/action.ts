'use server';

import { canAccessOrganizer, toAuthResult } from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

export async function updateEventModule(
  eventId: string,
  moduleSettings: Record<string, boolean>
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.session?.organizerId) {
      throw new Error('Unauthorized: Only organizers can update event modules');
    }

    // Check if the event belongs to the organizer
    const event = await database.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        slug: true,
        organizerId: true,
        eventModule: true,
        isPremiumEvent: true,
      },
    });

    if (!event) {
      throw new Error('Event not found');
    }

    if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
      throw new Error('Unauthorized: You can only update your own events');
    }

    if (!event.isPremiumEvent) {
      throw new Error('Only premium events can customize modules');
    }

    // Create or update the event module
    const updatedModule = await database.eventModule.upsert({
      where: {
        eventId,
      },
      create: {
        eventId,
        ...moduleSettings,
      },
      update: {
        ...moduleSettings,
      },
    });

    // Revalidate the event page
    revalidatePath(`/events/${event.slug}`);

    return serializePrisma(updatedModule);
  } catch (error) {
    log.error('Failed to update event module:', { error, eventId });
    throw new Error(
      `Could not update event module: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

export async function updateCustomPaymentModule(
  eventId: string,
  settings: {
    chipsEnabled: boolean;
    stripeEnabled: boolean;
  }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.session?.organizerId) {
      throw new Error(
        'Unauthorized: Only organizers can update custom payment module settings'
      );
    }

    // Check if the event belongs to the organizer
    const event = await database.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        organizerId: true,
        slug: true,
        isPremiumEvent: true,
        eventModule: {
          select: {
            id: true,
            customPaymentModule: true,
          },
        },
      },
    });

    if (!event) {
      throw new Error('Event not found');
    }

    if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
      throw new Error('Unauthorized: You can only update your own events');
    }

    if (!event.isPremiumEvent) {
      throw new Error('Only premium events can customize modules');
    }

    // Make sure the event module exists
    let eventModuleId = event.eventModule?.id;

    if (!eventModuleId) {
      // Create the event module if it doesn't exist
      const eventModule = await database.eventModule.create({
        data: {
          eventId,
          customPaymentEnabled: true, // Enable custom payment by default when configuring settings
        },
      });
      eventModuleId = eventModule.id;
    }

    // Create or update the custom payment module
    const updatedModule = await database.customPaymentModule.upsert({
      where: {
        eventModuleId,
      },
      create: {
        eventModuleId,
        chipsEnabled: settings.chipsEnabled,
        stripeEnabled: settings.stripeEnabled,
      },
      update: {
        chipsEnabled: settings.chipsEnabled,
        stripeEnabled: settings.stripeEnabled,
      },
    });

    // Revalidate the event page
    revalidatePath(`/events/${event.slug}`);

    return serializePrisma(updatedModule);
  } catch (error) {
    log.error('Failed to update custom payment module:', { error, eventId });
    throw new Error(
      `Could not update custom payment module: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

export async function updateDonationModule(
  eventId: string,
  settings: {
    minAmount: number;
    description?: string | null;
    thankYouMessage?: string | null;
  }
) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.session?.organizerId) {
      throw new Error(
        'Unauthorized: Only organizers can update donation module settings'
      );
    }

    // Check if the event belongs to the organizer
    const event = await database.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        slug: true,
        organizerId: true,
        isPremiumEvent: true,
        eventModule: {
          select: {
            id: true,
            donationModule: true,
          },
        },
      },
    });

    if (!event) {
      throw new Error('Event not found');
    }

    if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
      throw new Error('Unauthorized: You can only update your own events');
    }

    if (!event.isPremiumEvent) {
      throw new Error('Only premium events can customize modules');
    }

    // Make sure the event module exists
    let eventModuleId = event.eventModule?.id;

    if (!eventModuleId) {
      // Create the event module if it doesn't exist
      const eventModule = await database.eventModule.create({
        data: {
          eventId,
          donationEnabled: true, // Enable donation by default when configuring settings
        },
      });
      eventModuleId = eventModule.id;
    }

    // Create or update the donation module
    const updatedModule = await database.donationModule.upsert({
      where: {
        eventModuleId,
      },
      create: {
        eventModuleId,
        minAmount: settings.minAmount,
        description: settings.description
          ? JSON.parse(settings.description)
          : null,
        thankYouMessage: settings.thankYouMessage || null,
      },
      update: {
        minAmount: settings.minAmount,
        description: settings.description
          ? JSON.parse(settings.description)
          : null,
        thankYouMessage: settings.thankYouMessage || null,
      },
    });

    // Revalidate the event page
    revalidatePath(`/events/${event.slug}`);

    return serializePrisma(updatedModule);
  } catch (error) {
    log.error('Failed to update donation module:', { error, eventId });
    throw new Error(
      `Could not update donation module: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
