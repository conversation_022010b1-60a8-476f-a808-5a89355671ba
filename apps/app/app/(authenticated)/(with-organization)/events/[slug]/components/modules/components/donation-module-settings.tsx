import type { SerializedEvent } from '@/types';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { DonationModuleDialog } from './donation-module-dialog';

type DonationModuleSettingsProps = {
  event: SerializedEvent;
};

export const DonationModuleSettings = ({
  event,
}: DonationModuleSettingsProps) => {
  const modules = event.eventModule;

  return (
    <div className="flex items-start justify-between pt-2">
      <div className="w-full space-y-4">
        {modules?.donationModule?.minAmount && (
          <div className="flex flex-col items-start gap-2">
            <span className="text-muted-foreground text-xs">Min Amount</span>
            <span className="text-sm">{modules.donationModule.minAmount}</span>
          </div>
        )}
        {modules?.donationModule?.description && (
          <div className="flex flex-col gap-2">
            <span className="text-muted-foreground text-xs">Description</span>
            <div className="rounded-md border text-muted-foreground text-sm">
              <ReadOnlyEditor
                editorSerializedState={parseEditorContent(
                  typeof modules.donationModule.description === 'string'
                    ? modules.donationModule.description
                    : JSON.stringify(modules.donationModule.description)
                )}
              />
            </div>
          </div>
        )}
        {modules?.donationModule?.thankYouMessage && (
          <div className="flex flex-col gap-2">
            <span className="text-muted-foreground text-xs">
              Appreciation Message
            </span>
            <div className="rounded-md border p-4 text-muted-foreground text-sm">
              {modules.donationModule.thankYouMessage}
            </div>
          </div>
        )}
      </div>
      <DonationModuleDialog event={event} />
    </div>
  );
};
