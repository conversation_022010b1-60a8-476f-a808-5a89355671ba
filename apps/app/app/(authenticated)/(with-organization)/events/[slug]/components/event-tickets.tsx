'use client';

import { DeleteConfirmationDialog } from '@/app/(authenticated)/(with-organization)/components/delete-confirmation-dialog';
import { SponsorBannerDialog } from '@/app/(authenticated)/(with-organization)/events/components/sponsor-banner-dialog';
import { TicketPosterImageDialog } from '@/app/(authenticated)/(with-organization)/events/components/ticket-poster-image-dialog';
import { useIsPastEvent } from '@/app/hooks/use-is-past-event';
import type { SerializedEvent } from '@/types';
import {
  ImageIcon,
  Info,
  TicketIcon,
  TicketsIcon,
} from '@repo/design-system/components/icons';
import { Separator } from '@repo/design-system/components/ui/separator';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { formatDateTime } from '@repo/design-system/lib/format';
import { formatCurrency } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import Image from 'next/image';
import { InventoryDialog } from './inventory/components/inventory-dialog';
import { deleteTicketType } from './ticket-type/action';
import { TicketTypeDialog } from './ticket-type/components/ticket-type-dialog';
import { TicketTypeVisibility } from '@/app/(authenticated)/(with-organization)/events/[slug]/components/ticket-type/components/ticket-type-visibility';

interface EventTicketsProps {
  event: SerializedEvent;
}

export function EventTickets({ event }: EventTicketsProps) {
  return (
    <div className="space-y-6">
      <div className="rounded-lg border p-4">
        <div className="flex items-center justify-between gap-2">
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <TicketsIcon className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-semibold">Ticket Types</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Manage ticket types and view orders for this event.
            </p>
          </div>

          <TicketTypeDialog event={event} />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 2xl:grid-cols-3">
        {event.ticketTypes.map((ticketType) => (
          <TicketTypeView
            key={ticketType.id}
            event={event}
            ticketType={ticketType}
          />
        ))}
      </div>

      <div className="space-y-4 rounded-lg border p-4">
        <div className="flex items-center gap-2">
          <TicketIcon className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold">Customize Ticket Appearance</h3>
        </div>

        <div className="grid gap-y-4 lg:grid-cols-2">
          <div className="lg:pr-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-medium">Ticket Poster Image</h4>
              </div>
              <TicketPosterImageDialog event={event} />
            </div>
            <h4 className="text-left font-medium text-muted-foreground text-sm">
              Upload a poster image (A4 size — approx. 2480x3508 px, 5:7 aspect
              ratio) to appear on the ticket.
            </h4>
            <div className="mt-4 flex justify-center">
              {event.heroImageUrl ? (
                <div className="relative aspect-[5/7] w-[40%] overflow-hidden rounded-md">
                  <Image
                    src={event.heroImageUrl}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="flex aspect-[5/7] w-[40%] items-center justify-center rounded-md border border-dashed">
                  <p className="text-muted-foreground text-sm text-center px-4">
                    No ticket poster available
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="lg:border-l lg:pl-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-medium">Ticket Sponsor Banner</h4>
              </div>
              <SponsorBannerDialog event={event} />
            </div>
            <h4 className="text-left font-medium text-muted-foreground text-sm">
              Upload a banner image (1600x400 px, 4:1 aspect ratio) to showcase
              your event sponsors.
            </h4>
            <div className="mt-4">
              {event.sponsorBannerUrl ? (
                <div className="relative h-60 w-full overflow-hidden rounded-md">
                  <Image
                    src={event.sponsorBannerUrl}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                </div>
              ) : (
                <div className="flex h-60 w-full items-center justify-center rounded-md border border-dashed">
                  <p className="text-muted-foreground text-sm">
                    No ticket sponsor banner available
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function TicketTypeView({
  event,
  ticketType,
}: {
  event: SerializedEvent;
  ticketType: SerializedEvent['ticketTypes'][number];
}) {
  const isPastEvent = useIsPastEvent(event.startTime);

  const totalQuantity = ticketType.inventory.reduce(
    (acc, inv) => acc + inv.quantity,
    0
  );

  const handleDelete = async () => {
    if (ticketType._count.tickets > 0) {
      return;
    }

    try {
      await deleteTicketType(ticketType.id, event.id, event.slug);
      toast.success(`Ticket type "${ticketType.name}" deleted successfully`);
    } catch (error) {
      console.error('Error deleting ticket type:', error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Something went wrong, please try again.';
      toast.error(errorMessage);

      throw error;
    }
  };

  return (
    <div key={ticketType.id} className="space-y-3 rounded-lg border p-4">
      <div className="flex items-start justify-between">
        <h3 className="font-semibold text-md">{ticketType.name}</h3>
        {/* to edit ticket type infos */}
        {!isPastEvent && (
          <TicketTypeDialog event={event} ticketType={ticketType} mode="edit" />
        )}
      </div>
      {ticketType.description && (
        <p className="text-muted-foreground text-sm">
          {ticketType.description}
        </p>
      )}
      <div className="grid grid-cols-2 gap-2">
        <div className="flex items-center">
          <span className="mr-2 text-muted-foreground text-sm">Price:</span>
          <span className="rounded-full bg-primary/10 px-2 py-1 font-medium text-primary text-sm">
            {formatCurrency(ticketType.price)}
          </span>
        </div>
        <div className="flex items-center">
          <span className="mr-2 text-muted-foreground text-sm">
            Visibility:
          </span>
          <TicketTypeVisibility
            ticketType={ticketType}
            eventSlug={event.slug}
          />
        </div>
      </div>
      <div className="border-t pt-2">
        <div className="grid grid-cols-2 gap-2">
          <div className="flex flex-col">
            <div className="flex flex-row">
              <span className="text-muted-foreground text-xs">
                Tickets Sold
              </span>
              <Tooltip>
                <TooltipTrigger className="flex items-center">
                  <Info className="ml-1 h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Amount of tickets sold across all time slots</p>
                </TooltipContent>
              </Tooltip>
            </div>

            <span className="font-medium">{ticketType._count.tickets}</span>
          </div>
          <div className="flex flex-col">
            <div className="flex flex-row">
              <span className="text-muted-foreground text-xs">
                Tickets Remaining
              </span>
              <Tooltip>
                <TooltipTrigger className="flex items-center">
                  <Info className="ml-1 h-4 w-4" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Amount of tickets remaining across all time slots</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <span className="font-medium">{totalQuantity}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-muted-foreground text-xs">Min Per Order</span>
            <span className="font-medium">{ticketType.minPerOrder}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-muted-foreground text-xs">Max Per Order</span>
            <span className="font-medium">{ticketType.maxPerOrder}</span>
          </div>
        </div>
      </div>
      <div className="border-t pt-2 text-xs">
        <div className="flex flex-col gap-1">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Sale Start:</span>
            <span>{formatDateTime(ticketType.saleStartTime)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Sale End:</span>
            <span>{formatDateTime(ticketType.saleEndTime)}</span>
          </div>
        </div>
      </div>

      {/* to manage ticket type inventory */}
      {!isPastEvent && (
        <InventoryDialog event={event} ticketType={ticketType} />
      )}
      {/* delete ticket type */}
      {!isPastEvent && (
        <Tooltip>
          <TooltipTrigger asChild>
            <DeleteConfirmationDialog
              title="Delete Ticket Type"
              description={`Are you sure you want to delete "${ticketType.name}"? This action cannot be undone. Any existing inventory will also be deleted.`}
              deleteButtonText="Delete Ticket Type"
              onDelete={handleDelete}
              size="sm"
              variant="destructive"
              // cannot use disabled prop here because it will break tooltip
              className={cn(
                'w-full',
                ticketType._count.tickets > 0 && 'cursor-not-allowed opacity-50'
              )}
            />
          </TooltipTrigger>
          {ticketType._count.tickets > 0 && (
            <TooltipContent side="bottom">
              <p className="max-w-xs">
                This ticket type cannot be deleted because{' '}
                {ticketType._count.tickets}{' '}
                {ticketType._count.tickets === 1
                  ? 'ticket has'
                  : 'tickets have'}{' '}
                already been sold. You can only delete ticket types with no
                ticket sales.
              </p>
            </TooltipContent>
          )}
        </Tooltip>
      )}
    </div>
  );
}
