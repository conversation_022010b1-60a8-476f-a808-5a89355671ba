'use client';

import type { SerializedEvent } from '@/types';
import { Package, Plus } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { useParams } from 'next/navigation';
import React, { useState } from 'react';
import useSWR from 'swr';
import { TimeSlotMultiSelect } from '../../time-slot/components/time-slot-multiselect';
import { createInventory } from '../action';
import { InventoryCard } from './inventory-card';

interface InventoryDialogProps {
  event: SerializedEvent;
  ticketType: SerializedEvent['ticketTypes'][number];
}

export function InventoryDialog({ event, ticketType }: InventoryDialogProps) {
  const params = useParams();
  const slug = params.slug as string;
  const [open, setOpen] = React.useState(false);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [selectedTimeSlots, setSelectedTimeSlots] = useState<string[]>([]);
  const [quantity, setQuantity] = useState(event.maxTicketsPerEvent ?? 0);
  const [isCreating, setIsCreating] = useState(false);

  // Fetch inventories using SWR when the dialog is open
  const {
    data: { data: inventories } = {},
    isLoading,
    mutate,
  } = useSWR<{ data: SerializedEvent['ticketTypes'][number]['inventory'] }>(
    open
      ? urlSerialize('/api/inventory', { ticketTypeId: ticketType.id })
      : null
  );

  // Reset form
  function resetForm() {
    setSelectedTimeSlots([]);
    setQuantity(event.maxTicketsPerEvent ?? 0);
    setIsAddingNew(false);
  }

  const handleCreateInventory = async () => {
    if (selectedTimeSlots.length === 0) {
      toast.error('Please select at least one time slot');
      return;
    }

    if (quantity <= 0) {
      toast.error('Please enter a quantity greater than 0');
      return;
    }

    setIsCreating(true);

    try {
      const result = await createInventory(
        slug as string,
        ticketType.id,
        selectedTimeSlots,
        quantity
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to create inventory');
      }

      // Show success toast
      toast.success(result.message || 'Inventory created successfully');
      resetForm();

      // Refresh inventory data
      mutate();
    } catch (error) {
      console.error('Error creating inventory:', error);
      const message =
        error instanceof Error
          ? error.message
          : 'An error occurred while creating inventory';
      toast.error(message);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(value) => {
        setOpen(value);
        resetForm();
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="w-full">
          <Package />
          Manage Inventory ({ticketType.inventory.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Manage Inventory</DialogTitle>
          <DialogDescription>
            Manage inventories for {ticketType.name} ticket type.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isAddingNew ? (
            <div className="space-y-4 rounded-md border p-4">
              <h3 className="font-medium">Add New Inventory</h3>
              <div className="space-y-2">
                <span className="font-medium text-sm">Select Time Slots</span>
                <TimeSlotMultiSelect
                  eventId={event.id}
                  values={selectedTimeSlots}
                  onChange={setSelectedTimeSlots}
                  placeholder="Select time slots for inventory"
                  excludedIds={ticketType.inventory.map(
                    (inv) => inv.timeSlot.id
                  )}
                />
              </div>
              <div className="space-y-2">
                <span className="font-medium text-sm">
                  Number of tickets per time slot.
                </span>
                <Input
                  type="number"
                  min="1"
                  max={event.maxTicketsPerEvent}
                  value={quantity}
                  onChange={(e) => setQuantity(Number(e.target.value))}
                  placeholder="Enter quantity"
                />
                <span className="font-medium text-muted-foreground text-sm ">
                  Current event tier allows up to {event.maxTicketsPerEvent}{' '}
                  tickets per time slot.
                </span>
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={handleCreateInventory}
                  disabled={
                    isCreating ||
                    selectedTimeSlots.length === 0 ||
                    quantity <= 0
                  }
                >
                  {isCreating ? 'Creating...' : 'Create Inventory'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsAddingNew(false);
                    setSelectedTimeSlots([]);
                    setQuantity(0);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => setIsAddingNew(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add New Inventory
            </Button>
          )}

          {(() => {
            if (isLoading) {
              return (
                <div className="py-4 text-center text-muted-foreground">
                  Loading inventory data...
                </div>
              );
            }

            if (!inventories || inventories.length === 0) {
              return (
                <div className="py-4 text-center text-muted-foreground">
                  No inventory items found for this ticket type.
                </div>
              );
            }

            return inventories?.map((inventory) => (
              <InventoryCard
                key={inventory.id}
                inventory={inventory}
                maxTickets={event.maxTicketsPerEvent}
                mutate={mutate}
              />
            ));
          })()}
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
