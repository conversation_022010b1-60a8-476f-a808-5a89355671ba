'use client';

import type { SerializedEvent } from '@/types';
import { Visibility } from '@prisma/client';
import { Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateEventVisibility } from '../actions';
import { Button } from '@repo/design-system/components/ui/button';
import { useIsDesktop } from '@/app/hooks/use-is-desktop';

interface MobileVisibilityDialogProps {
  event: SerializedEvent;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function MobileVisibilityDialog({
  event,
  open,
  onOpenChange,
}: MobileVisibilityDialogProps) {
  const isDesktop = useIsDesktop();
  const [selectedVisibility, setSelectedVisibility] = useState<Visibility>(
    event.visibility
  );
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleVisibilityChange = (newVisibility: Visibility) => {
    setSelectedVisibility(newVisibility);
    onOpenChange(false);
    setConfirmOpen(true);
  };

  const handleConfirm = async () => {
    setIsLoading(true);

    try {
      if (event.slug) {
        await updateEventVisibility(event.slug, selectedVisibility);

        const visibilityText = {
          public: 'marked as public',
          private: 'marked as private',
          unlisted: 'marked as unlisted',
        }[selectedVisibility];

        toast.success(`Event ${visibilityText} successfully`);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to update event visibility. Please try again.';

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      setConfirmOpen(false);
    }
  };

  return (
    <>
      {isDesktop ? (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change Event Visibility</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Select the visibility level for your event.
              </p>
              <Select
                value={selectedVisibility}
                onValueChange={handleVisibilityChange}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={Visibility.public}>Public</SelectItem>
                  <SelectItem value={Visibility.private}>Private</SelectItem>
                  <SelectItem value={Visibility.unlisted}>Unlisted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent>
            <DrawerHeader className="text-left">
              <DialogTitle>Change Event Visibility</DialogTitle>
            </DrawerHeader>

            <div className="my-4 space-y-2 px-4">
              <p className="text-sm text-muted-foreground">
                Select the visibility level for your event.
              </p>
              <Select
                value={selectedVisibility}
                onValueChange={handleVisibilityChange}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={Visibility.public}>Public</SelectItem>
                  <SelectItem value={Visibility.private}>Private</SelectItem>
                  <SelectItem value={Visibility.unlisted}>Unlisted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <DrawerFooter className="pt-2">
              <DrawerClose asChild>
                <Button variant="outline">Cancel</Button>
              </DrawerClose>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      )}

      <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Event Visibility</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the event visibility to{' '}
              {selectedVisibility}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
