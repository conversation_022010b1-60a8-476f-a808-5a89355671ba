'use client';

import { EventDescriptionDialog } from '@/app/(authenticated)/(with-organization)/events/components/event-description-dialog';
import { EventTermsDialog } from '@/app/(authenticated)/(with-organization)/events/components/event-terms-dialog';
import type { SerializedEvent } from '@/types';
import {
  Activity,
  CalendarDays,
  ExternalLinkIcon,
  EyeIcon,
  Images,
  LinkIcon,
  MapPin,
  Tag,
  TicketIcon,
} from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import { formatDateTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import Image from 'next/image';
import { title } from 'radash';
import { CarouselImagesDialog } from '../../../components/carousel-images-dialog';
import { updateEvent } from '../../actions';
import { ShareButtons } from './share-buttons';

interface EventSummaryProps {
  event: SerializedEvent;
}

export function EventSummary({ event }: EventSummaryProps) {
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'public':
      case 'published':
        return {
          badge:
            'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
          dot: 'bg-green-600 dark:bg-green-400',
        };
      case 'unlisted':
      case 'draft':
        return {
          badge:
            'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
          dot: 'bg-gray-600 dark:bg-gray-400',
        };
      default:
        return {
          badge:
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
          dot: 'bg-yellow-600 dark:bg-yellow-400',
        };
    }
  };

  const statusStyles = getStatusStyles(event.status);
  const visibilityStyles = getStatusStyles(event.visibility);

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border p-4">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Venue</h3>
          </div>
          <p className="mt-2 text-muted-foreground text-sm">
            {/* {event.venue.name} */}
            {event.venueName}
          </p>
          {event.venueAddress && (
            <p className="mt-2 text-muted-foreground text-sm">
              {event.venueAddress}
            </p>
          )}
        </div>
        <div className="space-y-3 rounded-lg border p-4">
          <div>
            <div className="flex items-center gap-2">
              <CalendarDays className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-semibold">Date & Time</h3>
            </div>
            <p className="mt-2 text-muted-foreground text-sm">
              {formatDateTime(event.startTime)}
            </p>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <TicketIcon className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-semibold">Ticket Capacity</h3>
            </div>
            <p className="mt-2 text-muted-foreground text-sm">
              {event.maxTicketsPerEvent} tickets
              {!event.isPremiumEvent && (
                <span className="ml-1 text-yellow-600">(Free tier limit)</span>
              )}
            </p>
          </div>
        </div>
        <div className="space-y-3 rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <h3 className="font-semibold">Status</h3>
              </div>
              <div
                className={cn(
                  'inline-flex items-center gap-1.5 rounded-full px-2.5 py-1.5 font-medium text-xs',
                  statusStyles.badge
                )}
              >
                <div
                  className={cn('h-1.5 w-1.5 rounded-full', statusStyles.dot)}
                />
                {title(event.status)}
              </div>
            </div>
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-2">
                <EyeIcon className="h-4 w-4 text-muted-foreground" />
                <h3 className="font-semibold">Visibility</h3>
              </div>
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    'inline-flex items-center gap-1.5 rounded-full px-2.5 py-1.5 font-medium text-xs',
                    visibilityStyles.badge
                  )}
                >
                  <div
                    className={cn(
                      'h-1.5 w-1.5 rounded-full',
                      visibilityStyles.dot
                    )}
                  />
                  {title(event.visibility)}
                </div>
              </div>
            </div>
          </div>
          {event.status === 'published' && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <LinkIcon className="h-4 w-4 text-muted-foreground" />
                <h3 className="font-semibold">Public URL:</h3>
              </div>
              <a
                href={`${
                  process.env.NEXT_PUBLIC_WEB_URL || 'https://ticketcare.app'
                }/events/${event.slug}`}
                target="_blank"
                rel="noreferrer"
                className="group flex items-center gap-2 rounded-md bg-muted/50 p-2 text-xs transition-colors hover:bg-muted"
              >
                <span className="flex-1 truncate font-mono">
                  {process.env.NEXT_PUBLIC_WEB_URL || 'https://ticketcare.app'}
                  /events/{event.slug}
                </span>
                <ExternalLinkIcon className="h-3 w-3 text-muted-foreground transition-colors group-hover:text-foreground" />
              </a>
            </div>
          )}
        </div>
        <div className="rounded-lg border p-4 space-y-2">
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Category & Tags</h3>
          </div>
          <div className="space-y-2">
            <div>
              <p className="mb-1 text-muted-foreground text-xs">Event Type:</p>
              <Badge>
                {event.eventType === 'ngo'
                  ? 'Donation-Based Registration'
                  : 'Standard Ticketed Event'}
              </Badge>
            </div>
            {event.category && (
              <div>
                <p className="mb-1 text-muted-foreground text-xs">Category:</p>
                {event.category.map((category, index) => (
                  <Badge key={index} variant="secondary">
                    {category}
                  </Badge>
                ))}
              </div>
            )}
            {event.tags && event.tags.length > 0 && (
              <div>
                <p className="mb-2 text-muted-foreground text-xs">Tags:</p>
                <div className="flex flex-wrap gap-1">
                  {event.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="rounded-lg border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Images className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Carousel Images</h3>
          </div>
          <CarouselImagesDialog
            images={event.carouselImageUrls}
            onSubmit={async (values) => {
              await updateEvent(event.id, {
                carouselImageUrls: values.images,
              });
            }}
          />
        </div>
        <div className="mt-4">
          {event.carouselImageUrls && event.carouselImageUrls.length > 0 ? (
            <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
              {event.carouselImageUrls.map((imageUrl, index) => (
                <div
                  key={index}
                  className="relative h-40 overflow-hidden rounded-md"
                >
                  <Image
                    src={imageUrl}
                    alt={`${event.title} carousel image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
              ))}
            </div>
          ) : (
            <div className="flex h-40 w-full items-center justify-center rounded-md border border-dashed">
              <p className="text-muted-foreground text-sm">
                No carousel images available
              </p>
            </div>
          )}
        </div>
      </div>

      <div className="col-span-4">
        <EventDescriptionDialog event={event} />
      </div>

      <div className="col-span-4">
        <EventTermsDialog event={event} />
      </div>

      <ShareButtons event={event} />
    </div>
  );
}
