'use client';

import { updateEventModule } from '@/app/(authenticated)/(with-organization)/events/[slug]/components/modules/action';
import { CustomPaymentModuleSettings } from '@/app/(authenticated)/(with-organization)/events/[slug]/components/modules/components/custom-payment-module-settings';
import { DonationModuleSettings } from '@/app/(authenticated)/(with-organization)/events/[slug]/components/modules/components/donation-module-settings';
import type { SerializedEvent, SerializedOrganizer } from '@/types';
import { ExternalLink, InfoIcon } from '@repo/design-system/components/icons';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/design-system/components/ui/accordion';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Switch } from '@repo/design-system/components/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import Link from 'next/link';
import { type ComponentType, useState } from 'react';

type Module = {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  moduleSettingsComponent: ComponentType<{
    event: SerializedEvent;
    eventOrganizer?: SerializedOrganizer;
  }>;
  linkButtonText?: string;
  moduleRoute?: string;
};

type EventModulesProps = {
  event: SerializedEvent;
  eventOrganizer: SerializedOrganizer;
};

export const EventModules = ({ event, eventOrganizer }: EventModulesProps) => {
  const modules = event.eventModule;
  const [isUpdating, setIsUpdating] = useState(false);

  const availableModules: Module[] = [
    {
      // NOTE: id must tally with how EventModule schema defines the module name
      // NOTE: handleModuleToggle will append 'Enabled' to the id to match the schema
      // EXAMPLE: EventModule schema defines donationModule so id must be donation
      id: 'donation',
      name: 'Donations',
      description: 'Allow attendees to make donations for this event',
      enabled: modules?.donationEnabled || false,
      moduleSettingsComponent: DonationModuleSettings,
      linkButtonText: 'View Donations',
      moduleRoute: `/events/${event.slug}/donations`,
    },
    {
      id: 'customPayment',
      name: 'Custom Payment',
      description:
        'Process payments with your own payment gateway account instead of the default TicketCARE payment gateway.',
      enabled: modules?.customPaymentEnabled || false,
      moduleSettingsComponent: CustomPaymentModuleSettings,
    },
    // Add more modules here as they become available
  ];

  const handleModuleToggle = async (moduleId: string, enabled: boolean) => {
    setIsUpdating(true);
    try {
      await updateEventModule(event.id, {
        [`${moduleId}Enabled`]: enabled, // moduleId represents the name of the module
      });

      toast.success(
        `${moduleId.charAt(0).toUpperCase() + moduleId.slice(1)} module ${enabled ? 'enabled' : 'disabled'}`
      );
    } catch (error) {
      toast.error(
        `${error instanceof Error ? error.message : 'Failed to update module settings, please try again'}`
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="rounded-lg border">
      <div className="flex flex-col gap-1 p-4">
        <div className="flex items-center gap-2">
          <h3 className="font-semibold text-lg">Event Modules</h3>
          {!event.isPremiumEvent && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="reserved">
                  <InfoIcon className="h-3 w-3" />
                  Premium Required
                </Badge>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>
                  Customizing modules is available with Premium events — upgrade
                  to get started!
                </p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        <p className="text-muted-foreground text-sm">
          Enable or disable additional features for this event
        </p>
      </div>
      <div className="space-y-8 p-4 pt-0">
        {availableModules.map((module) => (
          <div
            key={module.id}
            className="flex flex-col gap-2 border-muted border-b-2 border-l-2 pb-4 pl-4" // Sort CSS classes
          >
            <div className="flex items-start justify-between space-x-2">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Label
                    htmlFor={`${module.id}-toggle`}
                    className="font-medium text-base"
                  >
                    {module.name}
                  </Label>
                </div>
                <p className="text-muted-foreground text-sm">
                  {module.description}
                </p>
              </div>

              <Switch
                id={`${module.id}-toggle`}
                checked={module.enabled}
                disabled={!event.isPremiumEvent || isUpdating}
                onCheckedChange={(checked) =>
                  handleModuleToggle(module.id, checked)
                }
              />
            </div>

            {/* Show module settings when enabled */}
            {event.isPremiumEvent && module.enabled && (
              <Accordion type="single" collapsible className="mt-2">
                <AccordionItem value="settings" className="">
                  <AccordionTrigger className="p-2 hover:bg-input hover:no-underline">
                    <h4 className="text-left font-medium text-sm">
                      {module.name} Settings
                    </h4>
                  </AccordionTrigger>
                  <AccordionContent className="p-2">
                    {module.id === 'customPayment' ? (
                      <module.moduleSettingsComponent
                        event={event}
                        eventOrganizer={eventOrganizer}
                      />
                    ) : (
                      <module.moduleSettingsComponent event={event} />
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}

            {event.isPremiumEvent && module.enabled && module.moduleRoute && (
              <Link href={module.moduleRoute} className="self-end">
                <Button variant="outline" size="sm">
                  <span className="mr-1">{module.linkButtonText}</span>
                  <ExternalLink size={14} />
                </Button>
              </Link>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
