'use client';
import type { SerializedEvent } from '@/types';
import { Visibility } from '@prisma/client';
import { Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateEventVisibility } from '../actions';

interface EventVisibilityDialogProps {
  event: SerializedEvent;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  mobileMode?: boolean;
}

export function EventVisibilityDialog({
  event,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  mobileMode = false,
}: EventVisibilityDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<Visibility | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [mobileSelectOpen, setMobileSelectOpen] = useState(false);

  // Use external state for mobile mode, internal for desktop
  const confirmOpen = mobileMode ? (externalOpen ?? false) : isOpen;
  const setConfirmOpen = mobileMode
    ? (externalOnOpenChange ?? (() => {}))
    : setIsOpen;

  const handleStatusChange = (newStatus: Visibility) => {
    setSelectedStatus(newStatus);
    if (mobileMode) {
      setMobileSelectOpen(false);
      setConfirmOpen(true);
    } else {
      setIsOpen(true);
    }
  };

  const handleConfirm = async () => {
    if (selectedStatus) {
      setIsLoading(true);

      try {
        if (event.slug) {
          await updateEventVisibility(event.slug, selectedStatus);

          // Show success message with appropriate visibility text
          const visibilityText = {
            public: 'marked as public',
            private: 'marked as private',
            unlisted: 'marked as unlisted',
          }[selectedStatus];

          toast.success(`Event ${visibilityText} successfully`);
        }
      } catch (error) {
        // Show error message
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to update event visibility. Please try again.';

        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
        if (mobileMode) {
          setConfirmOpen(false);
        } else {
          setIsOpen(false);
        }
      }
    }
  };

  // Mobile mode: Show a dialog with select
  if (mobileMode) {
    return (
      <>
        <AlertDialog open={mobileSelectOpen} onOpenChange={setMobileSelectOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Change Event Visibility</AlertDialogTitle>
              <AlertDialogDescription>
                Select the visibility level for your event.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="py-4">
              <Select
                value={selectedStatus ?? event.visibility}
                onValueChange={handleStatusChange}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select visibility" />
                  {isLoading && (
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  )}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={Visibility.public}>Public</SelectItem>
                  <SelectItem value={Visibility.private}>Private</SelectItem>
                  <SelectItem value={Visibility.unlisted}>Unlisted</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <AlertDialog open={confirmOpen} onOpenChange={setConfirmOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Change Event Visibility</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to change the event visibility to{' '}
                {selectedStatus}?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Continue'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }

  // Desktop mode: Show the original select + confirmation dialog
  return (
    <>
      <Select
        value={event.visibility}
        onValueChange={handleStatusChange}
        disabled={isLoading}
      >
        <SelectTrigger className="w-[120px]">
          <SelectValue placeholder="Select visibility" />
          {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={Visibility.public}>Public</SelectItem>
          <SelectItem value={Visibility.private}>Private</SelectItem>
          <SelectItem value={Visibility.unlisted}>Unlisted</SelectItem>
        </SelectContent>
      </Select>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Event Visibility</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the event visibility to{' '}
              {selectedStatus}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

// Export function to trigger mobile select dialog
export function useMobileVisibilityDialog(event: SerializedEvent) {
  const [selectOpen, setSelectOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);

  const openDialog = () => setSelectOpen(true);

  const component = (
    <EventVisibilityDialog
      event={event}
      mobileMode
      open={confirmOpen}
      onOpenChange={setConfirmOpen}
    />
  );

  return { openDialog, component };
}
