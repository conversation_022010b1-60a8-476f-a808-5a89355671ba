'use client';

import { DeleteConfirmationDialog } from '@/app/(authenticated)/(with-organization)/components/delete-confirmation-dialog';
import type { SerializedEvent } from '@/types';
import { Edit } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { formatDate, formatTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { deleteInventory, updateInventory } from '../action';

export interface InventoryCardProps {
  inventory: SerializedEvent['ticketTypes'][number]['inventory'][number];
  maxTickets: number;
  mutate?: () => void;
}

export function InventoryCard({
  inventory,
  maxTickets,
  mutate,
}: InventoryCardProps) {
  const params = useParams();
  const slug = params.slug as string;

  const [isEditing, setIsEditing] = useState(false);
  const [newQuantity, setNewQuantity] = useState(0);
  const [status, setStatus] = useState<
    'idle' | 'saving' | 'success' | 'error' | 'deleting'
  >('idle');

  const timeSlot = inventory.timeSlot;
  const soldTickets = timeSlot?._count?.tickets ?? 0;
  const totalTickets = inventory.quantity + soldTickets;

  // Calculate the effective maximum tickets that can be set
  // This is the tier limit minus already sold tickets
  const effectiveMaxTickets = Math.max(maxTickets - soldTickets, 0);

  // Start editing this inventory item
  const handleStartEdit = () => {
    setIsEditing(true);
    setNewQuantity(inventory.quantity);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setIsEditing(false);
    setNewQuantity(inventory.quantity);
  };

  // Handle change in the new quantity
  const handleQuantityChange = (quantity: number) => {
    setNewQuantity(quantity);
  };

  // Validate if the new quantity is valid
  const isQuantityValid = () => {
    // Must be at least equal to sold tickets
    if (newQuantity < soldTickets) {
      return false;
    }

    // Must not exceed max tickets (accounting for sold tickets)
    if (newQuantity > effectiveMaxTickets) {
      return false;
    }

    // Must be different from current quantity to save
    if (newQuantity === inventory.quantity) {
      return false;
    }

    return true;
  };

  // Save the inventory update
  const handleSaveInventory = async () => {
    if (!isQuantityValid()) {
      if (newQuantity < soldTickets) {
        toast.error(`Cannot set quantity below tickets sold (${soldTickets})`);
      } else if (newQuantity > effectiveMaxTickets) {
        toast.error(
          `Quantity exceeds available tier limit (${effectiveMaxTickets})`
        );
      } else {
        toast.error('Please change the quantity to save');
      }
      return;
    }

    try {
      // Set status to saving
      setStatus('saving');

      // Call the updateInventory function
      const result = await updateInventory(slug, inventory.id, newQuantity);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update inventory');
      }

      // Update status to success
      setStatus('success');

      // Reset editing state
      setIsEditing(false);

      // Call mutate if provided
      mutate?.();

      // Show success toast
      toast.success(`Updated inventory to ${newQuantity} tickets`);
    } catch (error) {
      // Handle error
      console.error('Error updating inventory:', error);
      const message =
        error instanceof Error
          ? error.message
          : 'An error occurred while updating inventory';

      // Show error toast
      toast.error(message);
      setStatus('error');
    }
  };

  // Delete the inventory item
  const handleDeleteInventory = async () => {
    try {
      // Set status to deleting
      setStatus('deleting');

      // Call the API to delete inventory
      const result = await deleteInventory(slug, inventory.id);

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete inventory');
      }

      // Update status to success
      setStatus('success');

      // Call mutate if provided
      mutate?.();

      // Show success toast
      toast.success('Inventory deleted successfully');
    } catch (error) {
      console.error('Error deleting inventory:', error);

      // Update error state
      const message =
        error instanceof Error
          ? error.message
          : 'An error occurred while deleting inventory';

      // Show error toast
      toast.error(message);
      setStatus('error');

      throw error;
    }
  };

  return (
    <div className="flex flex-col border-b pb-3">
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <p className="font-medium">{formatDate(timeSlot?.startTime)}</p>
          <p className="text-muted-foreground text-sm">
            {formatTime(timeSlot?.startTime, false)} -{' '}
            {formatTime(timeSlot?.endTime)}
          </p>
          <div className="mt-1 flex gap-4">
            <div className="flex flex-col">
              <span className="text-muted-foreground text-xs">Sold</span>
              <span className="font-medium">{soldTickets}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-muted-foreground text-xs">Available</span>
              <span className="font-medium">{inventory.quantity}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-muted-foreground text-xs">Total</span>
              <span className="font-medium">{totalTickets}</span>
            </div>
          </div>
        </div>

        {isEditing ? (
          <div className="flex flex-col items-end space-y-1">
            <span className="font-medium text-sm">Set available tickets:</span>
            <div className="flex flex-col items-end space-y-1">
              <Input
                type="number"
                min={soldTickets}
                max={effectiveMaxTickets}
                value={newQuantity}
                onChange={(e) =>
                  handleQuantityChange(Number.parseInt(e.target.value || '0'))
                }
                className="w-20"
                autoFocus
              />
              {newQuantity < soldTickets && (
                <p className="text-destructive text-xs">
                  Cannot be less than {soldTickets} (tickets sold)
                </p>
              )}
              {newQuantity > effectiveMaxTickets && (
                <p className="text-destructive text-xs">
                  Cannot exceed {effectiveMaxTickets}
                </p>
              )}
              <p className="text-muted-foreground text-xs">
                Min: {soldTickets} (tickets sold) | Available:{' '}
                {effectiveMaxTickets}
              </p>
              <p className="text-muted-foreground text-xs">
                Tier Limit: {maxTickets}
              </p>
            </div>
            <div className="mt-3 space-y-3">
              <Button
                variant="default"
                size="sm"
                onClick={handleSaveInventory}
                disabled={status === 'saving' || !isQuantityValid()}
              >
                {status === 'saving' ? 'Saving...' : 'Save'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelEdit}
                disabled={status === 'saving'}
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleStartEdit}
              disabled={status === 'saving'}
            >
              <Edit className="mr-1 h-4 w-4" /> Edit
            </Button>

            {/* only allow delete before sales occur */}
            <Tooltip>
              <TooltipTrigger asChild>
                <DeleteConfirmationDialog
                  title="Delete Inventory"
                  description={`Are you sure you want to delete this inventory for ${formatDate(timeSlot?.startTime)} ${formatTime(timeSlot?.startTime)} - ${formatTime(timeSlot?.endTime)}? This action cannot be undone.`}
                  onDelete={handleDeleteInventory}
                  size="sm"
                  variant="destructive"
                  // cannot use disabled prop here because it will break tooltip
                  className={cn(
                    'w-full',
                    (status === 'deleting' || soldTickets > 0) &&
                      'cursor-not-allowed opacity-50'
                  )}
                />
              </TooltipTrigger>
              {soldTickets > 0 && (
                <TooltipContent side="bottom">
                  <p className="max-w-xs">
                    This inventory cannot be deleted because {soldTickets}{' '}
                    {soldTickets === 1 ? 'ticket has' : 'tickets have'} already
                    been sold. You can only delete inventory with no ticket
                    sales.
                  </p>
                </TooltipContent>
              )}
            </Tooltip>
          </div>
        )}
      </div>
    </div>
  );
}
