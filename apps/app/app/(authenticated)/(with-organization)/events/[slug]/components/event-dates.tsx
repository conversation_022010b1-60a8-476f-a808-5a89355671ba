import { useIsPastEvent } from '@/app/hooks/use-is-past-event';
import type { SerializedEvent } from '@/types';
import {
  CalendarIcon,
  HourglassIcon,
} from '@repo/design-system/components/icons';
import { formatDate } from '@repo/design-system/lib/format';
import { TimeSlotCard } from './time-slot/components/time-slot-card';
import { TimeSlotDialog } from './time-slot/components/time-slot-dialog';

interface EventDatesProps {
  event: SerializedEvent;
}

export function EventDates({ event }: EventDatesProps) {
  const isPastEvent = useIsPastEvent(event.startTime);

  return (
    <div className="space-y-6">
      <div className="rounded-lg border p-4">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <HourglassIcon className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Event Dates</h3>
          </div>
          <p className="text-muted-foreground text-sm">
            Manage dates and time slots for this event.
          </p>
        </div>
      </div>

      {event.eventDates.length === 0 ? (
        <div className="rounded-lg border border-dashed p-8 text-center">
          <h3 className="font-medium text-muted-foreground">
            No dates added yet
          </h3>
          <p className="mt-1 text-muted-foreground text-sm">
            Event dates are automatically synced with the event start time and
            end time.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {event.eventDates.map((eventDate) => (
            <EventDateView
              key={eventDate.id}
              eventDate={eventDate}
              isPastEvent={isPastEvent}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function EventDateView({
  eventDate,
  isPastEvent = false,
}: {
  eventDate: SerializedEvent['eventDates'][number];
  isPastEvent?: boolean;
}) {
  return (
    <div key={eventDate.id} className="rounded-lg border p-4">
      <div className="mb-3 flex items-center gap-2">
        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
        <h3 className="font-semibold text-md">
          {formatDate(eventDate.date, 'EEEE, MMMM d, yyyy')}
        </h3>
      </div>

      {eventDate.timeSlots.length === 0 ? (
        <p className="text-muted-foreground text-sm">
          No time slots added for this date.
        </p>
      ) : (
        <div className="space-y-2">
          {eventDate.timeSlots.map((timeSlot) => (
            <TimeSlotCard
              key={timeSlot.id}
              timeSlot={timeSlot}
              isPastEvent={isPastEvent}
            />
          ))}
        </div>
      )}

      {!isPastEvent && (
        <div className="mt-3 flex justify-end">
          <TimeSlotDialog eventDate={eventDate} />
        </div>
      )}
    </div>
  );
}
