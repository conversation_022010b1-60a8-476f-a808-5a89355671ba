'use server';

import type { Visibility } from '@prisma/client';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';

export async function createTicketType(
  eventId: string,
  slug: string,
  ticketTypeData: {
    name: string;
    description?: string;
    price: number;
    quantity: number;
    maxPerOrder: number;
    minPerOrder: number;
    saleStartTime: Date;
    saleEndTime: Date;
    timeSlotIds: string[];
  }
) {
  try {
    // Use a transaction to ensure both operations succeed or fail together
    const result = await database.$transaction(async (tx) => {
      // Create ticket type
      const ticketType = await tx.ticketType.create({
        data: {
          eventId: eventId,
          name: ticketTypeData.name,
          description: ticketTypeData.description,
          price: ticketTypeData.price,
          maxPerOrder: ticketTypeData.maxPerOrder,
          minPerOrder: ticketTypeData.minPerOrder,
          saleStartTime: ticketTypeData.saleStartTime,
          saleEndTime: ticketTypeData.saleEndTime,
        },
      });

      // Create inventories using time slot ids
      await tx.inventory.createMany({
        data: ticketTypeData.timeSlotIds.map((timeSlotId) => ({
          ticketTypeId: ticketType.id,
          quantity: ticketTypeData.quantity,
          timeSlotId: timeSlotId,
        })),
      });

      return ticketType;
    });

    // Optionally revalidate paths or perform additional actions
    revalidatePath(`/events/${decodeURIComponent(slug)}`);

    return {
      success: true,
      ticketType: serializePrisma(result),
    };
  } catch (error) {
    log.error('Failed to create ticket type or inventory:', { error });

    throw new Error(
      `Could not create ticket type: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

// Update an existing ticket type without modifying inventory
export async function updateTicketType(
  ticketTypeId: string,
  eventId: string,
  slug: string,
  ticketTypeData: {
    name: string;
    description?: string;
    price: number;
    maxPerOrder: number;
    minPerOrder: number;
    saleStartTime: Date;
    saleEndTime: Date;
  }
) {
  try {
    // Validate that the ticket type belongs to the event
    const existingTicketType = await database.ticketType.findFirst({
      where: {
        id: ticketTypeId,
        eventId: eventId,
      },
    });

    if (!existingTicketType) {
      throw new Error('Ticket type not found or does not belong to this event');
    }

    // Update the ticket type
    const updatedTicketType = await database.ticketType.update({
      where: {
        id: ticketTypeId,
      },
      data: {
        name: ticketTypeData.name,
        description: ticketTypeData.description,
        price: ticketTypeData.price,
        maxPerOrder: ticketTypeData.maxPerOrder,
        minPerOrder: ticketTypeData.minPerOrder,
        saleStartTime: ticketTypeData.saleStartTime,
        saleEndTime: ticketTypeData.saleEndTime,
      },
    });

    // Revalidate the path to reflect changes
    revalidatePath(`/events/${decodeURIComponent(slug)}`);

    return {
      success: true,
      ticketType: serializePrisma(updatedTicketType),
    };
  } catch (error) {
    log.error('Failed to update ticket type:', { error });

    throw new Error(
      `Could not update ticket type: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

export async function deleteTicketType(
  ticketTypeId: string,
  eventId: string,
  slug: string
) {
  try {
    // Validate that the ticket type belongs to the event
    const existingTicketType = await database.ticketType.findFirst({
      where: {
        id: ticketTypeId,
        eventId: eventId,
      },
      include: {
        _count: {
          select: {
            tickets: true,
          },
        },
      },
    });

    if (!existingTicketType) {
      throw new Error('Ticket type not found or does not belong to this event');
    }

    // Check if there are any tickets sold for this ticket type
    if (existingTicketType._count.tickets > 0) {
      throw new Error('Cannot delete ticket type with existing ticket sales');
    }

    // Use a transaction to delete inventory items and the ticket type
    await database.$transaction(async (tx) => {
      // Delete all inventory items for this ticket type
      await tx.inventory.deleteMany({
        where: {
          ticketTypeId: ticketTypeId,
        },
      });

      // Delete the ticket type
      await tx.ticketType.delete({
        where: {
          id: ticketTypeId,
        },
      });
    });

    // Revalidate the path to reflect changes
    revalidatePath(`/events/${decodeURIComponent(slug)}`);

    return {
      success: true,
    };
  } catch (error) {
    log.error('Failed to delete ticket type:', { error });

    throw new Error(
      `Could not delete ticket type: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

export async function updateTicketTypeVisibility(
  ticketTypeId: string,
  slug: string,
  visibility: Visibility
) {
  const updatedTicketType = await database.ticketType.update({
    where: {
      id: ticketTypeId,
    },
    data: { visibility },
  });

  revalidatePath(`/events/${decodeURIComponent(slug)}`);

  return {
    success: true,
    data: serializePrisma(updatedTicketType),
  };
}
