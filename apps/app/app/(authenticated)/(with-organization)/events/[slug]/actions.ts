'use server';

import { EventStatus, TicketStatus, type Visibility } from '@prisma/client';
import { canAccessOrganizer, toAuthResult } from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { chip } from '@repo/payments';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

export async function getEvent(slug: string) {
  const event = await database.event.findUnique({
    where: {
      // Decode the slug in case it contains URL-encoded characters (for chinese slugs)
      slug: decodeURIComponent(slug),
    },
    select: {
      id: true,
      slug: true,
      title: true,
      description: true,
      terms: true,
      updates: true,
      startTime: true,
      endTime: true,
      doorsOpen: true,
      status: true,
      visibility: true,
      requiresApproval: true,
      waitingListEnabled: true,
      refundPolicy: true,
      category: true,
      tags: true,
      eventType: true,
      ticketSalesMode: true,
      venueId: true,
      heroImageUrl: true,
      carouselImageUrls: true,
      sponsorBannerUrl: true,
      isPremiumEvent: true,
      maxTicketsPerEvent: true,
      ticketsSold: true,
      premiumTierId: true,
      premiumTier: {
        select: {
          id: true,
          name: true,
          maxTicketsPerEvent: true,
        },
      },
      venueName: true,
      venueAddress: true,
      // venue: { select: { name: true } },
      eventModule: {
        select: {
          id: true,
          donationEnabled: true,
          customPaymentEnabled: true,
          donationModule: {
            select: {
              id: true,
              minAmount: true,
              description: true,
              thankYouMessage: true,
            },
          },
          customPaymentModule: {
            select: {
              id: true,
              chipsEnabled: true,
              stripeEnabled: true,
            },
          },
        },
      },
      ticketTypes: {
        select: {
          id: true,
          visibility: true,
          name: true,
          description: true,
          price: true,
          inventory: {
            select: {
              id: true,
              quantity: true,
              timeSlot: {
                select: {
                  id: true,
                  startTime: true,
                  endTime: true,
                  doorsOpen: true,
                  _count: {
                    select: {
                      tickets: true,
                    },
                  },
                },
              },
            },
          },
          maxPerOrder: true,
          minPerOrder: true,
          saleStartTime: true,
          saleEndTime: true,
          _count: {
            select: {
              tickets: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      },
      eventDates: {
        select: {
          id: true,
          date: true,
          timeSlots: {
            select: {
              id: true,
              startTime: true,
              endTime: true,
              doorsOpen: true,
            },
            orderBy: {
              startTime: 'asc',
            },
          },
        },
      },
      checkoutFormQuestion: true,
    },
  });

  if (!event) {
    return notFound();
  }

  return serializePrisma(event);
}

export async function updateEventStatus(slug: string, status: EventStatus) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  const event = await database.event.findUnique({
    where: { slug: decodeURIComponent(slug) },
    select: {
      id: true,
      organizerId: true,
    },
  });

  if (!event) {
    throw new Error('Event not found');
  }

  // Super admins can update any event status, regular organizers can only update their own
  if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
    throw new Error('Unauthorized: You can only update your own events');
  }

  // If trying to publish, check if there's at least one inventory available
  if (status === EventStatus.published) {
    // Get the event with its ticket types and inventory
    const event = await database.event.findUnique({
      where: { slug: decodeURIComponent(slug) },
      include: {
        ticketTypes: {
          include: {
            inventory: {
              include: {
                timeSlot: true,
              },
            },
          },
        },
      },
    });

    if (!event) {
      throw new Error('Event not found');
    }

    // Check if there's at least one ticket type with inventory
    const hasInventory = event.ticketTypes.some((ticketType) =>
      ticketType.inventory.some((inv) => inv.quantity > 0)
    );

    if (!hasInventory) {
      throw new Error(
        'Please add tickets for sale before publishing your event.'
      );
    }
  }

  const updatedEvent = await database.event.update({
    where: { slug: decodeURIComponent(slug) },
    data: { status },
  });

  // Create a promise that resolves after revalidation
  await Promise.all([
    new Promise((resolve) => {
      revalidatePath('/events');
      revalidatePath(`/events/${updatedEvent.slug}`);
      resolve(true);
    }),
  ]);

  return {
    success: true,
    data: serializePrisma(updatedEvent),
  };
}

export async function updateEventVisibility(
  slug: string,
  visibility: Visibility
) {
  const updatedEvent = await database.event.update({
    where: { slug: decodeURIComponent(slug) },
    data: { visibility },
  });

  // Create a promise that resolves after revalidation
  await Promise.all([
    new Promise((resolve) => {
      revalidatePath('/events');
      revalidatePath(`/events/${updatedEvent.slug}`);
      resolve(true);
    }),
  ]);

  return {
    success: true,
    data: serializePrisma(updatedEvent),
  };
}

export async function createTicket(
  eventId: string,
  values: {
    ownerName: string;
    ownerEmail: string;
    ownerPhone?: string;
    ticketType: string;
  }
) {
  // TODO: Implement ticket creation functionality
  // This is a placeholder function that will be implemented later
  // Using both parameters to satisfy the linter
  const ticketInfo = `Event: ${eventId}, Owner: ${values.ownerName}, Email: ${values.ownerEmail}, Type: ${values.ticketType}`;
  await Promise.resolve(ticketInfo); // Using await to satisfy linter and prevent unused variable warning

  // When implemented, this will create a ticket in the database
  // Example implementation:
  // const ticket = await database.ticket.create({
  //   data: {
  //     eventId,
  //     ticketTypeId: values.ticketType, // Use the ticket type from values
  //     orderId: 1, // This should be linked to an actual order
  //     status: 'reserved',
  //     purchaseDate: new Date(),
  //     ownerName: values.ownerName,
  //     ownerEmail: values.ownerEmail,
  //     ownerPhone: values.ownerPhone,
  //     qrCode: nanoid(), // Generate a unique QR code
  //   },
  // })

  await Promise.resolve(); // Adding await to fix lint error
  revalidatePath('/events');
  revalidatePath(`/events/${eventId}`);

  return {
    success: true,
    data: {},
    // data: serializePrisma(ticket),
  };
}

// Get all available premium tiers
export async function getPremiumTiers() {
  const tiers = await database.premiumTier.findMany({
    where: {
      isActive: true,
    },
    orderBy: {
      price: 'asc',
    },
  });

  return serializePrisma(tiers);
}

// Helper function to create event premium upgrade and handle errors
async function createEventPremiumUpgrade(
  eventId: string,
  slug: string,
  premiumTierId: string,
  paymentMethod: 'chip'
) {
  try {
    let eventPremiumUpgrade: { url: string };

    if (paymentMethod === 'chip') {
      // Use Chip-In Asia payment gateway
      eventPremiumUpgrade = await createChipPremiumUpgradePayment(
        eventId,
        slug,
        premiumTierId
      );
    } else {
      throw new Error('Invalid payment method');
    }

    return { redirectUrl: eventPremiumUpgrade.url };
  } catch (error) {
    log.error('Payment creation failed:', { error });
    throw new Error(
      `Failed to create payment: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

// Create a payment for premium tier upgrade using Chip-In Asia
export async function createChipPremiumUpgradePayment(
  eventId: string,
  slug: string,
  premiumTierId: string
) {
  // Decode the slug for URL usage
  const decodedSlug = decodeURIComponent(slug);
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  // Get the event to check if it belongs to the organizer or if user is super admin
  const event = await database.event.findUnique({
    where: { id: eventId },
    select: {
      id: true,
      title: true,
      organizerId: true,
      isPremiumEvent: true,
    },
  });

  if (!event) {
    throw new Error('Event not found');
  }

  // Super admins can upgrade any event, regular organizers can only upgrade their own
  if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
    throw new Error('Unauthorized: You can only upgrade your own events');
  }

  // Get the premium tier to check max tickets and price
  const premiumTier = await database.premiumTier.findUnique({
    where: { id: premiumTierId },
    select: {
      id: true,
      name: true,
      maxTicketsPerEvent: true,
      price: true,
    },
  });

  if (!premiumTier) {
    throw new Error('Premium tier not found');
  }

  if (!session.session.organizerId) {
    throw new Error('Organizer not found');
  }

  // Get the organizer to check premium status
  const organizer = await database.organizer.findUnique({
    where: { id: session.session.organizerId },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
    },
  });

  if (!organizer) {
    throw new Error('Organizer not found');
  }

  try {
    // Create a Chip payment
    const chipPayment = await chip.createPayment({
      amount: premiumTier.price.toNumber(),
      currency: 'MYR', // Malaysian Ringgit
      email: organizer.email,
      phone: organizer.phone || '', // Required by Chip
      fullName: organizer.name,
      products: [
        {
          name: `Premium Tier: ${premiumTier.name}`,
          quantity: String(1),
          price: premiumTier.price.toNumber(),
          category: 'event_premium_tier',
        },
      ],
      notes: `Upgrade event "${event.title}" to ${premiumTier.name} tier (${premiumTier.maxTicketsPerEvent} tickets)`,
      successUrl: `${process.env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`,
      failureUrl: `${process.env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`,
      successRedirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/redirect?slug=${decodedSlug}&eventId=${eventId}&status=success`,
      failureRedirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/redirect?slug=${decodedSlug}&eventId=${eventId}&status=failure`,
      cancelRedirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/redirect?slug=${decodedSlug}&eventId=${eventId}&status=cancel`,
      reference: `${organizer.name} - Premium Upgrade for Event #${eventId}`,
    });

    // fetch or create eventPremiumUpgrade (failed or pending)
    const existingUpgrade = await database.eventPremiumUpgrade.findFirst({
      where: {
        eventId,
        premiumTierId,
        organizerId: organizer.id,
        OR: [{ status: 'pending' }, { status: 'failed' }],
      },
      orderBy: { createdAt: 'desc' },
    });

    if (existingUpgrade) {
      // if record exists update the transaction id
      await database.eventPremiumUpgrade.update({
        where: { id: existingUpgrade.id },
        data: {
          premiumTierId,
          amount: premiumTier.price,
          status: 'pending',
          paymentStatus: 'pending',
          transactionId: chipPayment.id, // update Chip payment ID with latest one
        },
      });
    } else {
      // Store the payment in the database for verification later
      await database.eventPremiumUpgrade.create({
        data: {
          id: chipPayment.id,
          eventId,
          organizerId: organizer.id,
          premiumTierId,
          amount: premiumTier.price,
          status: 'pending',
          transactionId: chipPayment.id, // Use the Chip payment ID as the transaction ID
          metadata: {
            upgradeType: 'event_premium_tier',
            paymentMethod: 'chip',
          },
        },
      });
    }

    log.info('Chip payment created', {
      eventId,
      premiumTierId,
      organizerId: organizer.id,
      upgradeType: 'event_premium_tier',
      transactionId: chipPayment.id,
      paymentMethod: 'chip',
    });

    return { url: chipPayment.checkout_url };
  } catch (error) {
    log.error('Failed to create Chip payment:', { error });
    throw new Error(
      `Failed to create payment: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

// Upgrade event to premium tier after successful payment
// Initiate the premium upgrade process for an event
export async function initiateEventPremiumUpgrade(
  eventId: string,
  slug: string,
  premiumTierId: string,
  paymentMethod: 'chip' = 'chip' // Default to Chip payment
): Promise<{ redirectUrl: string }> {
  // Decode the slug for URL usage
  const decodedSlug = decodeURIComponent(slug);
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.session?.organizerId) {
    throw new Error('Unauthorized: Only organizers can upgrade events');
  }

  // Get the event to check if it belongs to the organizer
  const event = await database.event.findUnique({
    where: { id: eventId },
    select: {
      organizerId: true,
      isPremiumEvent: true,
      maxTicketsPerEvent: true,
    },
  });

  if (!event) {
    throw new Error('Event not found');
  }

  if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
    throw new Error('Unauthorized: You can only upgrade your own events');
  }

  // Get the premium tier to check max tickets
  const premiumTier = await database.premiumTier.findUnique({
    where: { id: premiumTierId },
    select: {
      maxTicketsPerEvent: true,
      price: true,
    },
  });

  if (!premiumTier) {
    throw new Error('Premium tier not found');
  }

  // Block user from downgrading tier
  if (premiumTier.maxTicketsPerEvent <= event.maxTicketsPerEvent) {
    throw new Error(
      'Selected tier level must not be lower than or equal to existing tier level'
    );
  }

  // Get the organizer
  const organizer = await database.organizer.findUnique({
    where: { id: session.session.organizerId },
  });

  if (!organizer) {
    throw new Error('Organizer not found');
  }

  // If the premium tier has a price, redirect to payment
  if (premiumTier.price.gt(0)) {
    // Redirect to payment
    return await createEventPremiumUpgrade(
      eventId,
      decodedSlug,
      premiumTierId,
      paymentMethod
    );
  }

  // If the premium tier is free, upgrade directly
  await database.event.update({
    where: { id: eventId },
    data: {
      isPremiumEvent: true,
      premiumTierId,
      maxTicketsPerEvent: premiumTier.maxTicketsPerEvent,
    },
  });

  revalidatePath('/events');
  revalidatePath(`/events/${decodedSlug}`);

  // Return empty redirectUrl to indicate no payment needed
  return {
    redirectUrl: '',
  };
}

/**
 * Delete an event and all its related data
 *
 * This function checks if:
 * 1. The user is authorized (must be the event organizer)
 * 2. The event has no tickets sold (cannot delete events with sold tickets)
 *
 * Then it deletes all related data in a transaction:
 * - Event premium upgrades
 * - Inventory items
 * - Ticket types
 * - Time slots
 * - Event dates
 * - The event itself
 */
export async function deleteEvent(eventId: string) {
  try {
    // Get the current session
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    // Get the event to check if it belongs to the organizer
    const event = await database.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        title: true,
        organizerId: true,
        _count: {
          select: {
            tickets: {
              where: {
                status: {
                  in: [
                    TicketStatus.purchased,
                    TicketStatus.reserved,
                    TicketStatus.pending,
                  ],
                },
              },
            },
          },
        },
      },
    });

    if (!event) {
      throw new Error('Event not found');
    }

    // Super admins can delete any event, regular organizers can only delete their own
    if (!canAccessOrganizer(toAuthResult(session), event.organizerId)) {
      throw new Error('Unauthorized: You can only delete your own events');
    }

    // Check if there are any tickets sold for this event
    if (event._count.tickets > 0) {
      throw new Error(
        'Cannot delete event with existing ticket sales. Cancel the event instead.'
      );
    }

    // Use a transaction to delete all related data
    await database.$transaction(async (tx) => {
      // Delete event premium upgrades
      await tx.eventPremiumUpgrade.deleteMany({
        where: { eventId },
      });

      // Delete all inventory items for all ticket types
      await tx.inventory.deleteMany({
        where: {
          ticketType: {
            eventId,
          },
        },
      });

      // Delete all ticket types
      await tx.ticketType.deleteMany({
        where: { eventId },
      });

      // Delete all time slots for all event dates
      await tx.timeSlot.deleteMany({
        where: {
          eventDate: {
            eventId,
          },
        },
      });

      // Delete all event dates
      await tx.eventDate.deleteMany({
        where: { eventId },
      });

      // Finally, delete the event
      await tx.event.delete({
        where: { id: eventId },
      });
    });

    // Revalidate paths
    revalidatePath('/events');

    log.info('Event deleted successfully', {
      eventId,
      eventTitle: event.title,
      organizerId: session.session.organizerId,
    });

    return {
      success: true,
      message: 'Event deleted successfully',
    };
  } catch (error) {
    log.error('Failed to delete event:', { error });

    throw new Error(
      `Could not delete event: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
