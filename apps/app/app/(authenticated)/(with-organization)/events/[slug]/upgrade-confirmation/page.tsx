import { getUpgradeInfoFromSession } from './action';
import { UpgradeConfirmationClient } from './client';

export default async function UpgradeConfirmationPage({
  params,
  searchParams,
}: {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Get the slug from params
  const { slug } = await params;

  // Pre-fetch upgrade information on the server side
  const upgradeInfo = await getUpgradeInfoFromSession(slug, await searchParams);

  // Pass the necessary props to the client component
  return (
    <UpgradeConfirmationClient
      eventId={upgradeInfo.eventId}
      slug={slug}
      sessionId={upgradeInfo.sessionId}
      initialStatus={upgradeInfo.status}
      initialUpgradeState={upgradeInfo.initialUpgradeState}
    />
  );
}
