'use client';

import {
  <PERSON>ert<PERSON><PERSON>cle,
  CheckCircle,
  Star,
  XCircle,
} from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  <PERSON>Header,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useInterval } from 'usehooks-ts';
import { checkUpgradeStatus, markInvalidUpgradeAsCancelled } from './action';

type UpgradeStatus = 'success' | 'failure' | 'cancel' | 'pending';

// Success component
function SuccessContent({
  eventId,
  slug,
  sessionId,
  initialUpgradeState,
}: {
  eventId: string;
  slug: string;
  sessionId: string;
  initialUpgradeState: string;
}) {
  // Start with the server-provided upgrade state
  const [upgradeStatus, setUpgradeStatus] =
    useState<string>(initialUpgradeState);
  const [pollingCount, setPollingCount] = useState(0);

  // Determine if we should poll based on upgrade state
  const shouldPoll =
    sessionId &&
    initialUpgradeState !== 'confirmed' &&
    initialUpgradeState !== 'failed' &&
    pollingCount < 10;

  // Function to check upgrade status
  const checkStatus = async () => {
    if (!sessionId) {
      return;
    }

    try {
      // Check the current upgrade status
      const result = await checkUpgradeStatus(sessionId);

      // If the upgrade is confirmed (completed)
      if (result.status === 'completed') {
        // Upgrade is confirmed - update UI
        setUpgradeStatus('confirmed');

        // Update invalid event premium upgrade rows
        await markInvalidUpgradeAsCancelled(eventId, slug);
      }
      // If the upgrade has failed or been cancelled
      else if (result.status === 'cancelled' || result.status === 'failed') {
        // Upgrade failed - update UI
        setUpgradeStatus('failed');
      }

      // Increment polling count
      setPollingCount((prev: number) => prev + 1);
    } catch (error) {
      console.error('Error polling for upgrade status:', error);
      // Continue polling on error
    }
  };

  // Use the useInterval hook for polling
  useInterval(checkStatus, shouldPoll ? 3000 : null);

  // Show different UI based on upgrade status
  if (upgradeStatus === 'processing') {
    return (
      <>
        <CardHeader className="flex flex-col items-center space-y-2 text-center">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <CardTitle className="text-2xl">Processing Upgrade</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground">
            Your premium tier upgrade is being processed. This may take a
            moment. Please do not close this page.
          </p>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <p className="text-center text-muted-foreground text-sm">
            You'll receive a confirmation once your upgrade is processed.
          </p>
        </CardFooter>
      </>
    );
  }

  if (upgradeStatus === 'failed') {
    return (
      <>
        <CardHeader className="flex flex-col items-center space-y-2 text-center">
          <XCircle className="h-16 w-16 text-red-500" />
          <CardTitle className="text-2xl">Upgrade Failed</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground">
            We couldn't process your premium tier upgrade. Please try again.
          </p>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button asChild className="w-full">
            <Link href={`/events/${slug}`}>Return to Event</Link>
          </Button>
        </CardFooter>
      </>
    );
  }

  // Default: confirmed upgrade
  return (
    <>
      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <CheckCircle className="h-16 w-16 text-green-500" />
        <CardTitle className="text-2xl">Upgrade Successful</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          Your event has been successfully upgraded to premium status. You now
          have increased ticket sales capacity and premium features.
        </p>
        <div className="mt-4 flex items-center justify-center">
          <Star className="h-8 w-8 text-amber-500" />
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/events/${slug}`}>Return to Event</Link>
        </Button>
      </CardFooter>
    </>
  );
}

// Failure component
function FailureContent({ slug }: { slug: string }) {
  return (
    <>
      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <XCircle className="h-16 w-16 text-red-500" />
        <CardTitle className="text-2xl">Upgrade Failed</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          We couldn't process your premium tier upgrade. Please try again or
          contact support.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/events/${slug}`}>Return to Event</Link>
        </Button>
      </CardFooter>
    </>
  );
}

// Cancel component
function CancelContent({ slug }: { slug: string }) {
  return (
    <>
      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <AlertCircle className="h-16 w-16 text-amber-500" />
        <CardTitle className="text-2xl">Upgrade Cancelled</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          Your premium tier upgrade was cancelled. You can try upgrading again
          from the event dashboard.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/events/${slug}`}>Return to Event</Link>
        </Button>
      </CardFooter>
    </>
  );
}

interface UpgradeConfirmationClientProps {
  eventId: string;
  slug: string;
  sessionId: string;
  initialStatus: string;
  initialUpgradeState: string;
}

export function UpgradeConfirmationClient({
  eventId,
  slug,
  sessionId,
  initialStatus,
  initialUpgradeState,
}: UpgradeConfirmationClientProps) {
  const searchParams = useSearchParams();
  // Use the server-provided status as fallback if client-side params are not available
  const status = (searchParams.get('status') as UpgradeStatus) || initialStatus;

  return (
    <div className="container flex w-full justify-center py-12">
      <Card className="max-w-md">
        {status === 'success' && (
          <SuccessContent
            eventId={eventId}
            slug={slug}
            sessionId={sessionId}
            initialUpgradeState={initialUpgradeState}
          />
        )}
        {status === 'failure' && <FailureContent slug={slug} />}
        {status === 'cancel' && <CancelContent slug={slug} />}
      </Card>
    </div>
  );
}
