'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedEvent } from '@/types';
import { Pencil, Upload } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTrigger,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import Image from 'next/image';
import { useState } from 'react';
import type { ChangeEvent } from 'react';
import { updateEvent } from '../actions';

interface SponsorBannerFormValues {
  sponsorBannerUrl?: string;
}

interface SponsorBannerDialogProps {
  event: SerializedEvent;
}

export function SponsorBannerDialog({ event }: SponsorBannerDialogProps) {
  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();

  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    event?.sponsorBannerUrl || null
  );

  const form = useForm<SponsorBannerFormValues>({
    defaultValues: {
      sponsorBannerUrl: event.sponsorBannerUrl || undefined,
    },
  });

  // Function to handle Ticket Sponsor Banner upload
  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('access', 'public');

      // Create a new XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/upload', true);

      // Set the appropriate headers
      xhr.setRequestHeader('Accept', 'application/json');

      // Track upload progress
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(percentComplete);
        }
      };

      // Handle response
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
              setPreviewUrl(response.url);
              form.setValue('sponsorBannerUrl', response.url);
              toast.success('Image uploaded successfully');
            } else {
              toast.error(response.error || 'Failed to upload image');
            }
          } catch (_) {
            toast.error('Failed to process server response');
          }
        } else {
          toast.error(
            `Failed to upload image: ${xhr.statusText || 'Server error'}`
          );
        }
        setIsUploading(false);
      };

      // Handle errors
      xhr.onerror = () => {
        toast.error('Network error during upload.');
        setIsUploading(false);
      };

      xhr.send(formData);
    } catch (_) {
      toast.error('Failed to upload image');
      setIsUploading(false);
    }
  };

  // Function to handle image URL input
  const handleImageUrlChange = (url: string) => {
    if (url) {
      setPreviewUrl(url);
      form.setValue('sponsorBannerUrl', url);
    }
  };

  async function onSubmit(values: SponsorBannerFormValues) {
    try {
      setIsSaving(true);
      if (!event?.id) {
        throw new Error('Event ID is required');
      }

      await updateEvent(event.id, {
        ...values,
      });

      toast.success('Ticket Sponsor Banner updated successfully');
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const FileUploadTab = () => (
    <div className="flex flex-col gap-4">
      {previewUrl && (
        <div className="relative h-40 w-full overflow-hidden rounded-md">
          <Image
            src={previewUrl}
            alt="Ticket Sponsor Banner preview"
            fill
            className="object-cover"
          />
        </div>
      )}

      <FormField
        control={form.control}
        name="sponsorBannerUrl"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center gap-4">
              <Label
                htmlFor="image-upload"
                className={`flex h-10 cursor-pointer items-center gap-2 rounded-md border border-input bg-background px-4 py-2 font-medium text-sm ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${
                  isUploading ? 'opacity-50' : ''
                }`}
              >
                <Upload className="h-4 w-4" />
                {isUploading
                  ? `Uploading... ${uploadProgress}%`
                  : 'Pick a file'}
              </Label>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
                disabled={isUploading}
              />
              <input type="hidden" {...field} />
            </div>
            <FormDescription>
              Upload a sponsor banner for your event. Recommended size:
              1200x200px.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );

  const UrlInputTab = () => (
    <div className="flex flex-col gap-4">
      {previewUrl && (
        <div className="relative h-40 w-full overflow-hidden rounded-md">
          <Image
            src={previewUrl}
            alt="Ticket Sponsor Banner preview"
            fill
            className="object-cover"
          />
        </div>
      )}

      <FormField
        control={form.control}
        name="sponsorBannerUrl"
        render={({ field }) => (
          <FormItem>
            <Label htmlFor="image-url">Image URL</Label>
            <div className="flex flex-col gap-2">
              <Input
                id="image-url"
                placeholder="https://example.com/image.jpg"
                {...field}
                disabled={isUploading}
              />
              <Button
                type="button"
                variant="secondary"
                onClick={() => handleImageUrlChange(field.value || '')}
                disabled={!field.value || isUploading}
                className="mt-2 w-full sm:w-auto"
              >
                Preview Image
              </Button>
            </div>
            <FormDescription>
              Enter a URL for your Ticket Sponsor Banner. Recommended size:
              1200x200px.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );

  const SponsorBannerForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-3xl space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="sponsorBannerUrl"
          render={({ field }) => (
            <FormItem>
              <Tabs defaultValue="url" className="w-full">
                <TabsList className="mb-4 w-full">
                  <TabsTrigger value="url" className="w-full">
                    Image URL
                  </TabsTrigger>
                  <TabsTrigger value="file" className="w-full">
                    Upload File
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="url">
                  <UrlInputTab />
                </TabsContent>
                <TabsContent value="file">
                  <FileUploadTab />
                </TabsContent>
              </Tabs>
              <input type="hidden" {...field} />
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="max-md:w-full"
          disabled={isSaving || isUploading}
        >
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </form>
    </Form>
  );

  if (isDesktop) {
    return (
      <Dialog
        open={open}
        onOpenChange={() => {
          setOpen(!open);
          setPreviewUrl(event?.sponsorBannerUrl || null);
          form.reset();
        }}
      >
        <DialogTrigger asChild>
          <Button size="sm" variant="outline">
            <Pencil className="mr-2 h-4 w-4" />
            Edit
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Ticket Sponsor Banner</DialogTitle>
          </DialogHeader>
          <SponsorBannerForm />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button size="sm" variant="outline">
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DialogTitle>Edit Ticket Sponsor Banner</DialogTitle>
        </DrawerHeader>
        <SponsorBannerForm />
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">Cancel</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
