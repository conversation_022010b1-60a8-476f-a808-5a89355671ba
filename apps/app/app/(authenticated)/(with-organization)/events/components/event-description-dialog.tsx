'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedEvent } from '@/types';
import { Editor } from '@repo/design-system/components/blocks/editor-00/editor';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { Info, Pencil } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateEvent } from '../actions';

interface DescriptionFormValues {
  description?: string;
}

interface EventDescriptionDialogProps {
  event: SerializedEvent;
}

export function EventDescriptionDialog({ event }: EventDescriptionDialogProps) {
  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<DescriptionFormValues>({
    defaultValues: {
      description: event.description || undefined,
    },
  });

  async function onSubmit(values: DescriptionFormValues) {
    try {
      setIsSaving(true);
      if (!event?.id) {
        throw new Error('Event ID is required');
      }

      await updateEvent(event.id, {
        ...values,
      });

      toast.success('Event description updated successfully');
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const DescriptionForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-5xl space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <Editor
                editorSerializedState={parseEditorContent(field.value)}
                onSerializedChange={(value) =>
                  field.onChange(JSON.stringify(value))
                }
              />
              <FormDescription>
                Provide a detailed description of your event. You can add
                formatting, links, and images.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="max-md:w-full" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </form>
    </Form>
  );

  const DialogTriggerContent = () => (
    <div className="rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Info className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold">Event Details</h3>
        </div>
        <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </Button>
      </div>
      <div className="mt-2 text-muted-foreground">
        <ReadOnlyEditor
          editorSerializedState={parseEditorContent(
            event.description ?? 'No description provided.'
          )}
        />
      </div>
    </div>
  );

  if (isDesktop) {
    return (
      <>
        <DialogTriggerContent />
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="sm:max-w-2xl lg:max-w-5xl">
            <DialogHeader>
              <DialogTitle>Edit Event Description</DialogTitle>
            </DialogHeader>
            <DescriptionForm />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <DialogTriggerContent />
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent>
          <DrawerHeader className="text-left">
            <DialogTitle>Edit Event Description</DialogTitle>
          </DrawerHeader>
          <DescriptionForm />
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}
