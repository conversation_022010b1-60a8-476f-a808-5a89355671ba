'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedEvent } from '@/types';
import { FileText, Pencil } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateEvent } from '../actions';

interface CheckoutQuestionFormValues {
  checkoutFormQuestion?: string;
}

interface EventCheckoutQuestionDialogProps {
  event: SerializedEvent;
}

export function EventCheckoutQuestionDialog({
  event,
}: EventCheckoutQuestionDialogProps) {
  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<CheckoutQuestionFormValues>({
    defaultValues: {
      checkoutFormQuestion: event.checkoutFormQuestion || undefined,
    },
  });

  async function onSubmit(values: CheckoutQuestionFormValues) {
    try {
      setIsSaving(true);
      if (!event?.id) {
        throw new Error('Event ID is required');
      }

      await updateEvent(event.id, {
        ...values,
      });

      toast.success('Checkout question updated successfully');
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const CheckoutQuestionForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-5xl space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="checkoutFormQuestion"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Checkout Question</FormLabel>
              <Input
                placeholder="e.g., What's your t-shirt size?"
                {...field}
                value={field.value || ''}
              />
              <FormDescription>
                Add a question that attendees will answer during checkout. This
                could be for t-shirt sizes, dietary preferences, or any other
                information you need.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="max-md:w-full" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </form>
    </Form>
  );

  const DialogTriggerContent = () => (
    <div className="rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold">Checkout Question (Optional)</h3>
        </div>
        <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </Button>
      </div>
      <div className="mt-2 text-muted-foreground ">
        {event.checkoutFormQuestion ? (
          <p>{event.checkoutFormQuestion}</p>
        ) : (
          <p className="text-muted-foreground">
            No checkout question set. Attendees won't be asked for additional
            information.
          </p>
        )}
      </div>
    </div>
  );

  if (isDesktop) {
    return (
      <>
        <DialogTriggerContent />
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="sm:max-w-2xl lg:max-w-xl">
            <DialogHeader>
              <DialogTitle>Edit Checkout Question</DialogTitle>
            </DialogHeader>
            <CheckoutQuestionForm />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <DialogTriggerContent />
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent>
          <DrawerHeader className="text-left">
            <DialogTitle>Edit Checkout Question</DialogTitle>
          </DrawerHeader>
          <CheckoutQuestionForm />
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}
