'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedEvent } from '@/types';
import type { EventUpdate, JsonInput } from '@/types/event-update';
import { Editor } from '@repo/design-system/components/blocks/editor-00/editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { Pencil } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import { nanoid } from 'nanoid';
import { useState } from 'react';
import { updateEvent } from '../actions';

interface UpdateFormValues {
  title: string;
  content?: string;
}

interface EventUpdateDialogProps {
  event: SerializedEvent;
  update?: EventUpdate;
  mode?: 'create' | 'edit';
}

export function EventUpdateDialog({
  event,
  update,
  mode = 'create',
}: EventUpdateDialogProps) {
  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<UpdateFormValues>({
    defaultValues: {
      title: update?.title || '',
      content: update?.content || undefined,
    },
  });

  // Helper function to get button text based on mode
  const getButtonText = () => {
    return mode === 'create' ? 'Add Update' : 'Save Changes';
  };

  async function onSubmit(values: UpdateFormValues) {
    try {
      setIsSaving(true);
      if (!event?.id) {
        throw new Error('Event ID is required');
      }

      // Get current updates or initialize empty array
      const currentUpdates: EventUpdate[] = Array.isArray(event.updates)
        ? (event.updates as EventUpdate[])
        : [];

      let newUpdates: EventUpdate[];

      if (mode === 'create') {
        // Create a new update
        const newUpdate: EventUpdate = {
          id: nanoid(),
          title: values.title,
          content: values.content || '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        newUpdates = [...currentUpdates, newUpdate];
      } else if (mode === 'edit' && update) {
        // Edit existing update
        newUpdates = currentUpdates.map((item) => {
          if (item.id === update.id) {
            return {
              ...item,
              title: values.title,
              content: values.content || item.content,
              updatedAt: new Date().toISOString(),
            };
          }
          return item;
        });
      } else {
        throw new Error('Invalid operation');
      }

      // Convert updates array to JSON for Prisma
      await updateEvent(event.id, {
        updates: newUpdates as unknown as JsonInput[],
      });

      toast.success(
        mode === 'create'
          ? 'Update added successfully'
          : 'Update edited successfully'
      );
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const UpdateForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-5xl space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <Input placeholder="Update title" {...field} />
              <FormDescription>
                Enter a title for this update (e.g., "Seating Information",
                "Parking Details", etc.)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Content</FormLabel>
              <Editor
                editorSerializedState={parseEditorContent(field.value)}
                onSerializedChange={(value) =>
                  field.onChange(JSON.stringify(value))
                }
              />
              <FormDescription>
                Provide the details for this update.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="max-md:w-full" disabled={isSaving}>
          {isSaving ? 'Saving...' : getButtonText()}
        </Button>
      </form>
    </Form>
  );

  const DialogTriggerContent = () => {
    if (mode === 'create') {
      return <Button onClick={() => setOpen(true)}>Create Update</Button>;
    }

    return (
      <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
        <Pencil className="mr-2 h-4 w-4" />
        Edit
      </Button>
    );
  };

  if (isDesktop) {
    return (
      <>
        <DialogTriggerContent />
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="sm:max-w-2xl lg:max-w-5xl">
            <DialogHeader>
              <DialogTitle>
                {mode === 'create' ? 'Add New Update' : 'Edit Update'}
              </DialogTitle>
            </DialogHeader>
            <UpdateForm />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <DialogTriggerContent />
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent>
          <DrawerHeader className="text-left">
            <DialogTitle>
              {mode === 'create' ? 'Add New Update' : 'Edit Update'}
            </DialogTitle>
          </DrawerHeader>
          <UpdateForm />
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}
