'use client';

import { EventCheckoutQuestionDialog } from '@/app/(authenticated)/(with-organization)/events/components/event-checkout-question-dialog';
import type { SerializedEvent } from '@/types';
import type { EventUpdate } from '@/types/event-update';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { EditIcon, FileText } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import { Card } from '@repo/design-system/components/ui/card';
import { formatDistanceToNow } from 'date-fns';
import { EventUpdateDeleteDialog } from './event-update-delete-dialog';
import { EventUpdateDialog } from './event-update-dialog';

interface EventEditorialsManagerProps {
  event: SerializedEvent;
}

export function EventEditorialsManager({ event }: EventEditorialsManagerProps) {
  // Get updates from event or initialize empty array
  const updates: EventUpdate[] = Array.isArray(event.updates)
    ? (event.updates as EventUpdate[])
    : [];

  return (
    <div className="space-y-6">
      <div className="space-y-4 rounded-lg border p-4">
        <div className="flex items-center justify-between gap-2">
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <EditIcon className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-semibold">Event Updates</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Manage updates to keep your attendees informed about important
              event information.
            </p>
          </div>

          <EventUpdateDialog event={event} mode="create" />
        </div>

        {updates.length <= 0 ? (
          <div className="space-y-4 rounded-lg p-8 text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
              <FileText className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="font-semibold text-lg">No updates yet</h3>
            <p className="text-muted-foreground text-sm">
              Add updates to keep your attendees informed about important event
              information.
            </p>
            <Button variant="outline" asChild>
              <EventUpdateDialog event={event} mode="create" />
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Display dynamic updates */}
            {updates.map((update) => (
              <UpdateCard key={update.id} event={event} update={update} />
            ))}
          </div>
        )}
      </div>

      <EventCheckoutQuestionDialog event={event} />
    </div>
  );
}

interface UpdateCardProps {
  event: SerializedEvent;
  update: EventUpdate;
}

function UpdateCard({ event, update }: UpdateCardProps) {
  return (
    <Card className="rounded-none border-0 border-b-2 border-l-2 p-4">
      <div className="mb-2 flex items-center justify-between">
        <div>
          <h4 className="font-semibold">{update.title}</h4>
          <p className="text-muted-foreground text-xs">
            Updated{' '}
            {formatDistanceToNow(new Date(update.updatedAt), {
              addSuffix: true,
            })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <EventUpdateDialog event={event} update={update} mode="edit" />
          <EventUpdateDeleteDialog event={event} update={update} />
        </div>
      </div>
      <div className="mt-2">
        <ReadOnlyEditor
          editorSerializedState={parseEditorContent(update.content)}
          innerClassName="p-0"
        />
      </div>
    </Card>
  );
}
