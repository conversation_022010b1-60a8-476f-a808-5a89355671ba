'use client';

import type { SerializedEvent } from '@/types';
import { Trash } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useRouter } from 'next/navigation';
import { type ReactNode, useState } from 'react';
import { deleteEvent } from '../[slug]/actions';

interface EventDeleteDialogProps {
  event: SerializedEvent;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  trigger?: ReactNode;
}

export function EventDeleteDialog({
  event,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  trigger,
}: EventDeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [internalOpen, setInternalOpen] = useState(false);
  const router = useRouter();

  // Use external state if provided, otherwise use internal state
  const open = externalOpen ?? internalOpen;
  const setOpen = externalOnOpenChange ?? setInternalOpen;

  async function handleDelete() {
    try {
      setIsDeleting(true);

      // Call the server action to delete the event
      const result = await deleteEvent(event.id);

      if (result.success) {
        toast.success('Event deleted successfully');
        // Redirect to events list
        router.push('/events');
      } else {
        throw new Error(result.message || 'Failed to delete event');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  }

  const defaultTrigger = (
    <Button variant="destructive" size="icon">
      <Trash className="h-4 w-4" />
    </Button>
  );

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      {!externalOpen && (
        <AlertDialogTrigger asChild>
          {trigger ?? defaultTrigger}
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Event</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete <strong>{event.title}</strong>?
            <br />
            <br />
            This action <strong>cannot be undone</strong>. This will permanently
            delete the event and all associated data including ticket types,
            time slots, and inventory.
            <br />
            <br />
            <strong>Note:</strong> Events with sold tickets cannot be deleted.
            You should cancel the event instead.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            variant="destructive"
            onClick={(e) => {
              e.preventDefault();
              handleDelete();
            }}
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete Event'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
