'use client';

import * as React from 'react';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedEvent } from '@/types';
import { PencilIcon } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTrigger,
} from '@repo/design-system/components/ui/drawer';
import { title } from 'radash';
import { EventForm } from '../form';

interface EventDialogProps {
  mode?: 'create' | 'edit' | 'disabled';
  event?: SerializedEvent;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  trigger?: React.ReactNode;
}

export function EventDialog({
  mode = 'create',
  event,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  trigger,
}: EventDialogProps) {
  const isDesktop = useIsDesktop();
  const [internalOpen, setInternalOpen] = React.useState(false);

  // Use external state if provided, otherwise use internal state
  const open = externalOpen ?? internalOpen;
  const setOpen = externalOnOpenChange ?? setInternalOpen;

  // User hasn't complete onboarding
  if (mode === 'disabled') {
    return <Button disabled>Create Event</Button>;
  }

  const defaultTrigger = (
    <Button>
      {mode === 'create' ? 'Create Event' : <PencilIcon className="h-4 w-4" />}
    </Button>
  );

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        {!externalOpen && (
          <DialogTrigger asChild>{trigger ?? defaultTrigger}</DialogTrigger>
        )}
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>{title(mode)} an event</DialogTitle>
          </DialogHeader>
          <EventForm {...{ setOpen, mode, event }} />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      {!externalOpen && (
        <DrawerTrigger asChild>
          {trigger ?? <Button>{title(mode)} Event</Button>}
        </DrawerTrigger>
      )}
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DialogTitle>{title(mode)} an event</DialogTitle>
        </DrawerHeader>
        <EventForm {...{ setOpen, mode, event }} />
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">Cancel</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
