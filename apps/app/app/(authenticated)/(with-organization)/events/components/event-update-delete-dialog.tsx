'use client';

import type { SerializedEvent } from '@/types';
import type { EventUpdate, JsonInput } from '@/types/event-update';
import { Trash2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateEvent } from '../actions';

interface EventUpdateDeleteDialogProps {
  event: SerializedEvent;
  update: EventUpdate;
}

export function EventUpdateDeleteDialog({
  event,
  update,
}: EventUpdateDeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  async function handleDelete() {
    try {
      setIsDeleting(true);
      if (!event?.id) {
        throw new Error('Event ID is required');
      }

      // Get current updates
      const currentUpdates: EventUpdate[] = Array.isArray(event.updates)
        ? (event.updates as EventUpdate[])
        : [];

      // Filter out the update to delete
      const newUpdates = currentUpdates.filter(
        (item: EventUpdate) => item.id !== update.id
      );

      // Convert updates array to JSON for Prisma
      await updateEvent(event.id, {
        updates: newUpdates as unknown as JsonInput[],
      });

      toast.success('Update deleted successfully');
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button size="sm" variant="outline" className="text-destructive">
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Update</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the update "{update.title}"? This
            action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-white hover:bg-destructive/90"
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
