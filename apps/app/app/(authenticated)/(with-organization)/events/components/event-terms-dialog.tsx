'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedEvent } from '@/types';
import { Editor } from '@repo/design-system/components/blocks/editor-00/editor';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { FileText, Pencil } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateEvent } from '../actions';

interface TermsFormValues {
  terms?: string;
}

interface EventTermsDialogProps {
  event: SerializedEvent;
}

export function EventTermsDialog({ event }: EventTermsDialogProps) {
  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<TermsFormValues>({
    defaultValues: {
      terms: event.terms || undefined,
    },
  });

  async function onSubmit(values: TermsFormValues) {
    try {
      setIsSaving(true);
      if (!event?.id) {
        throw new Error('Event ID is required');
      }

      await updateEvent(event.id, {
        ...values,
      });

      toast.success('Event terms updated successfully');
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const TermsForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-5xl space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="terms"
          render={({ field }) => (
            <FormItem>
              <Editor
                editorSerializedState={parseEditorContent(field.value)}
                onSerializedChange={(value) =>
                  field.onChange(JSON.stringify(value))
                }
              />
              <FormDescription>
                Specify the terms and conditions for your event, including
                refund policies, age restrictions, and other important
                information.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="max-md:w-full" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </form>
    </Form>
  );

  const DialogTriggerContent = () => (
    <div className="rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-semibold">Terms & Conditions</h3>
        </div>
        <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </Button>
      </div>
      <div className="mt-2 text-muted-foreground">
        <ReadOnlyEditor
          editorSerializedState={parseEditorContent(
            event.terms ?? 'No terms provided.'
          )}
          innerClassName="p-0"
        />
      </div>
    </div>
  );

  if (isDesktop) {
    return (
      <>
        <DialogTriggerContent />
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="sm:max-w-2xl lg:max-w-5xl">
            <DialogHeader>
              <DialogTitle>Edit Event Terms & Conditions</DialogTitle>
            </DialogHeader>
            <TermsForm />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <DialogTriggerContent />
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent>
          <DrawerHeader className="text-left">
            <DialogTitle>Edit Event Terms & Conditions</DialogTitle>
          </DrawerHeader>
          <TermsForm />
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}
