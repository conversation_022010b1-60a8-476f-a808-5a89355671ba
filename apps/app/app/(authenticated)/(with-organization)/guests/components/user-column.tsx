'use client';

import type { SerializedUser } from '@/types';
import { ArrowUpDown } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import type { ColumnDef } from '@tanstack/react-table';

export const columns: ColumnDef<SerializedUser>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const id = (getValue() as string) ?? '';
      const short =
        id && id.length > 12 ? `${id.slice(0, 4)}…${id.slice(-4)}` : id;

      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="font-mono cursor-help">{short}</span>
          </TooltipTrigger>
          <TooltipContent>{id}</TooltipContent>
        </Tooltip>
      );
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const user = row.original;
      return user.name || `${user.firstName} ${user.lastName}`;
    },
  },
  // {
  //   accessorKey: 'lastName',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="transparent"
  //         size="table"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Last Name
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: 'email',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  // {
  //   accessorKey: 'type',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="transparent"
  //         size="table"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Type
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  // {
  //   accessorKey: 'tickets',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="transparent"
  //         size="table"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Tickets
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     return (
  //       <div className="flex flex-col">
  //         <span className="font-semibold">
  //           {row.original.orders?.at(0)?.tickets?.at(0)?.event?.title}
  //         </span>
  //         <span>
  //           {row.original.orders?.at(0)?.tickets?.at(0)?.ticketType?.name}
  //         </span>
  //       </div>
  //     );
  //   },
  // },
  {
    accessorKey: '_count.orders',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Orders
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return <span>{row.original._count?.orders ?? 0}</span>;
    },
  },
  // {
  //   accessorKey: 'balance',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="transparent"
  //         size="table"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Balance
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  // {
  //   accessorKey: 'lastLogin',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="transparent"
  //         size="table"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Last Login
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
];
