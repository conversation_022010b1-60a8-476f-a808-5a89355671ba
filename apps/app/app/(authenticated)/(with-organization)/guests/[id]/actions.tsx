import { getOrganizerFilter, toAuthResult } from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import {} from '@repo/design-system/components/ui/breadcrumb';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

export async function getUser(id: string) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user || !session?.session.organizerId) {
    return notFound();
  }

  const organizerId = getOrganizerFilter(toAuthResult(session));

  // If organizerId is null, user has no access to user data
  if (organizerId === null) {
    return notFound();
  }

  const user = await database.user.findUnique({
    where: {
      id,
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      phone: true,
      dob: true,
      role: true,
      orders: {
        where: {
          tickets: {
            some: {
              event: {
                organizerId: organizerId ?? undefined,
              },
            },
          },
        },
        include: {
          tickets: {
            include: {
              event: true,
              ticketType: true,
            },
          },
        },
      },
    },
  });

  if (!user) {
    return notFound();
  }

  return serializePrisma(user);
}
