import { OrderTable } from '@/app/(authenticated)/(with-organization)/orders/components/order-table';
import { getUser } from '@/app/(authenticated)/(with-organization)/guests/[id]/actions';
import {} from '@repo/design-system/components/ui/breadcrumb';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const user = await getUser(id);

  const title = 'TicketCARE - User';
  const description = `Details for ${user.firstName} ${user.lastName}`;

  return {
    title,
    description,
  };
}

export default async function GuestDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const user = await getUser(id);

  return (
    <>
      <Header pages={['Users']} page={user.id} />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-3xl tracking-tight">{`${user.firstName} ${user.lastName}`}</h2>
        </div>
        <Tabs defaultValue="summary">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="orders">Orders</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2" />
          </div>
          <TabsContent value="summary" className="mt-6">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name</span>
                    <span>{`${user.firstName} ${user.lastName}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email</span>
                    <span>{user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone</span>
                    <span>{user.phone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Date of Birth</span>
                    <span>
                      {user.dob
                        ? new Date(user.dob).toLocaleDateString()
                        : 'N/A'}
                    </span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Account Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Account Type</span>
                    <span className="capitalize">{user.role}</span>
                  </div>
                </CardContent>
              </Card>
              {/* <Card>
                <CardHeader>
                  <CardTitle>Notifications</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Email Notifications
                    </span>
                    <span>
                      {user.emailNotifications ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      SMS Notifications
                    </span>
                    <span>
                      {user.smsNotifications ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Push Notifications
                    </span>
                    <span>
                      {user.pushNotifications ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </CardContent>
              </Card> */}
            </div>
          </TabsContent>
          <TabsContent value="orders" className="mt-6">
            <OrderTable userId={user.id} />
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
