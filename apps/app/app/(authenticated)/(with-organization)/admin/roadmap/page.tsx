import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { Button } from '@repo/design-system/components/ui/button';
import { headers } from 'next/headers';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Header } from '../../components/header';
import { getRoadmapTasks } from './actions';
import { RoadmapKanban } from './components/roadmap-kanban';
import { RoadmapTaskDialog } from './components/roadmap-task-dialog';

export default async function AdminRoadmapPage() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  // Check if user is a super admin
  if (!isSuperAdmin(toAuthResult(session))) {
    redirect('/');
  }

  const tasks = await getRoadmapTasks();

  return (
    <>
      <Header page="Roadmap" />

      <div className="flex items-center justify-between gap-4 px-4">
        <div>
          <h1 className="font-bold text-3xl">Roadmap Management</h1>
          <p className="text-muted-foreground">
            Manage development tasks and public roadmap visibility
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild variant="outline">
            <Link href="/admin/roadmap/requests">View Requests</Link>
          </Button>
          <RoadmapTaskDialog />
        </div>
      </div>

      <div className="p-4">
        <RoadmapKanban initialTasks={tasks} />
      </div>
    </>
  );
}
