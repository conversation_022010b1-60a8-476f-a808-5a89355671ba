'use client';

import {
  type PublicRequestWithRelations,
  requestColumns,
} from '@/app/(authenticated)/(with-organization)/admin/roadmap/requests/components/request-columns';
import { MAX_ROWS, type Pagination } from '@repo/database/types';
import { Button } from '@repo/design-system/components/ui/button';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import { useDebounce } from '@repo/design-system/hooks/use-debounce';
import { urlSerialize } from '@repo/design-system/lib/utils';
import {
  type PaginationState,
  type SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import React from 'react';
import useSWR, { mutate } from 'swr';
import { RequestBulkActions } from './request-bulk-actions';
import { RequestReviewDialog } from './request-review-dialog';

interface RequestTableProps {
  initialData: PublicRequestWithRelations[];
}

export function RequestTable({ initialData }: RequestTableProps) {
  const router = useRouter();
  const [search, setSearch] = React.useState('');
  const debouncedSearch = useDebounce(search, 300);
  const [selectedRequests, setSelectedRequests] = React.useState<string[]>([]);

  const [statusFilter, setStatusFilter] = React.useState<string>('all');

  // Memoize columnFilters to prevent infinite re-renders
  const columnFilters = React.useMemo(() => {
    return statusFilter !== 'all'
      ? [{ id: 'status', value: statusFilter }]
      : [];
  }, [statusFilter]);
  const [sorting, setSorting] = React.useState<SortingState>([
    { id: 'createdAt', desc: true },
  ]);
  const [pagination, _setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: MAX_ROWS,
  });

  const [dialogState, setDialogState] = React.useState<{
    open: boolean;
    request: PublicRequestWithRelations | null;
  }>({
    open: false,
    request: null,
  });

  const handleDialogClose = React.useCallback((open: boolean) => {
    if (!open) {
      setDialogState({ open: false, request: null });
    }
  }, []);

  // Create the SWR key for consistent cache management
  const swrKey = React.useMemo(
    () =>
      urlSerialize('/api/public-requests', {
        query: debouncedSearch,
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
      }),
    [debouncedSearch, pagination.pageIndex, pagination.pageSize]
  );

  const handleDialogUpdate = React.useCallback(async () => {
    try {
      // Mutate the current SWR cache to trigger a re-fetch
      await mutate(swrKey);

      // Also mutate any other potential cache keys for public requests
      // This ensures data consistency across different views/filters
      await mutate(
        (key) => typeof key === 'string' && key.includes('/api/public-requests')
      );
    } catch (error) {
      console.error('Error refreshing data:', error);
      // Fallback to route refresh (no full reload) if SWR mutation fails
      router.refresh();
    }
  }, [swrKey]);

  const { data } = useSWR<{
    data: PublicRequestWithRelations[];
    pagination: Pagination;
  }>(swrKey);

  const currentData = data?.data ?? initialData;

  // Handle selection
  const handleSelectRequest = (requestId: string, selected: boolean) => {
    setSelectedRequests((prev) =>
      selected ? [...prev, requestId] : prev.filter((id) => id !== requestId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedRequests(
      selected ? currentData.map((request) => request.id) : []
    );
  };

  const clearSelection = () => {
    setSelectedRequests([]);
  };

  const table = useReactTable({
    data: currentData,
    columns: requestColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    globalFilterFn: (row, _columnId, filterValue) => {
      const title = row.original.title?.toLowerCase() || '';
      const description = row.original.description?.toLowerCase() || '';
      const name = row.original.name?.toLowerCase() || '';
      const email = row.original.email?.toLowerCase() || '';
      const searchValue = filterValue.toLowerCase();

      return (
        title.includes(searchValue) ||
        description.includes(searchValue) ||
        name.includes(searchValue) ||
        email.includes(searchValue)
      );
    },
    state: {
      sorting,
      globalFilter: debouncedSearch,
      columnFilters,
    },
  });

  return (
    <>
      <RequestBulkActions
        selectedRequests={selectedRequests}
        requests={currentData}
        onClearSelection={clearSelection}
        onUpdate={handleDialogUpdate}
      />

      <div className="flex items-center gap-4 py-4">
        <div className="relative max-w-sm flex-1">
          <Input
            placeholder="Search requests (title, description, name, email)..."
            value={search}
            onChange={(event) => setSearch(event.target.value)}
            className="pl-4"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="reviewed">Reviewed</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="converted">Converted</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                <TableHead className="w-12">
                  <Checkbox
                    checked={
                      selectedRequests.length === currentData.length &&
                      currentData.length > 0
                    }
                    onCheckedChange={handleSelectAll}
                    indeterminate={
                      selectedRequests.length > 0 &&
                      selectedRequests.length < currentData.length
                    }
                  />
                </TableHead>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={(e) => {
                    // Don't open dialog if clicking on checkbox
                    if (
                      (e.target as HTMLElement).closest('[role="checkbox"]')
                    ) {
                      return;
                    }
                    setDialogState({ open: true, request: row.original });
                  }}
                >
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={selectedRequests.includes(row.original.id)}
                      onCheckedChange={(checked) =>
                        handleSelectRequest(row.original.id, checked as boolean)
                      }
                    />
                  </TableCell>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={requestColumns.length + 1}
                  className="h-24 text-center"
                >
                  No requests found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-muted-foreground text-sm">
          Showing{' '}
          {table.getFilteredRowModel().rows.length > 0
            ? `${table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}-${Math.min(
                (table.getState().pagination.pageIndex + 1) *
                  table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}`
            : '0'}{' '}
          of {table.getFilteredRowModel().rows.length} entries
        </div>

        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <p className="font-medium text-sm">Rows per page</p>
            <select
              className="h-8 w-16 rounded-md border border-input bg-background px-2 py-1 text-sm"
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Previous
            </Button>
            <span className="flex items-center gap-1 text-sm">
              <span>Page</span>
              <strong>
                {table.getState().pagination.pageIndex + 1} of{' '}
                {table.getPageCount() || 1}
              </strong>
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              Last
            </Button>
          </div>
        </div>
      </div>

      <RequestReviewDialog
        request={dialogState.request}
        open={dialogState.open}
        onOpenChange={handleDialogClose}
        onUpdate={handleDialogUpdate}
      />
    </>
  );
}
