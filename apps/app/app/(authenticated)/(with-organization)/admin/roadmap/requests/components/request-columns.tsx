'use client';

import type { PublicRequest, User } from '@repo/database/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import type { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { title } from 'radash';

export type PublicRequestWithRelations = PublicRequest & {
  reviewer?: Pick<User, 'id' | 'name' | 'email'> | null;
  convertedToTask?: {
    id: string;
    title: string;
    status: string;
  } | null;
};

export const requestColumns: ColumnDef<PublicRequestWithRelations>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => {
      const title = row.original.title;
      return (
        <div className="max-w-[300px]">
          <div className="truncate font-medium">{title}</div>
          {row.original.description && (
            <div className="mt-1 truncate text-muted-foreground text-sm">
              {row.original.description.substring(0, 100)}
              {row.original.description.length > 100 && '...'}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      let variant:
        | 'outline'
        | 'pending'
        | 'success'
        | 'destructive'
        | 'secondary' = 'outline';

      switch (status) {
        case 'pending':
          variant = 'pending';
          break;
        case 'reviewed':
          variant = 'secondary';
          break;
        case 'approved':
          variant = 'success';
          break;
        case 'rejected':
          variant = 'destructive';
          break;
        case 'converted':
          variant = 'success';
          break;
        default:
          variant = 'pending';
          break;
      }

      return <Badge variant={variant}>{title(status)}</Badge>;
    },
    filterFn: (row, id, value) => {
      if (value === 'all') {
        return true;
      }
      return row.getValue(id) === value;
    },
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      const priority = row.original.priority;
      if (!priority) {
        return <span className="text-muted-foreground">-</span>;
      }

      let variant: 'outline' | 'destructive' | 'pending' | 'secondary' =
        'outline';

      switch (priority) {
        case 'high':
          variant = 'destructive';
          break;
        case 'medium':
          variant = 'pending';
          break;
        case 'low':
          variant = 'secondary';
          break;
        default:
          variant = 'secondary';
          break;
      }

      return <Badge variant={variant}>{title(priority)}</Badge>;
    },
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: ({ row }) => {
      const category = row.original.category;
      if (!category) {
        return <span className="text-muted-foreground">-</span>;
      }
      return (
        <Badge variant="outline">{title(category.replace('-', ' '))}</Badge>
      );
    },
  },
  {
    accessorKey: 'name',
    header: 'Submitter',
    cell: ({ row }) => {
      const name = row.original.name;
      const email = row.original.email;

      if (!name && !email) {
        return <span className="text-muted-foreground">Anonymous</span>;
      }

      return (
        <div>
          {name && <div className="font-medium">{name}</div>}
          {email && (
            <div className="text-muted-foreground text-sm">{email}</div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'convertedToTask',
    header: 'Converted Task',
    cell: ({ row }) => {
      const task = row.original.convertedToTask;
      if (!task) {
        return <span className="text-muted-foreground">-</span>;
      }

      return (
        <div className="flex items-center space-x-1">
          <div className="max-w-[150px] truncate font-medium text-sm">
            {task.title}
          </div>
          <Badge variant="outline" className="text-xs">
            {title(task.status.replace('-', ' '))}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created',
    cell: ({ row }) => {
      return format(new Date(row.original.createdAt), 'MMM d, yyyy');
    },
  },
];
