'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import type { PublicRequest, RequestStatus, User } from '@prisma/client';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/design-system/components/ui/form';
import { useForm } from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { Separator } from '@repo/design-system/components/ui/separator';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { format } from 'date-fns';
import { title } from 'radash';
import React from 'react';
import { z } from 'zod';
import { convertRequestToTask, updateRequestStatus } from '../../actions';

type PublicRequestWithRelations = PublicRequest & {
  reviewer?: Pick<User, 'id' | 'name' | 'email'> | null;
  convertedToTask?: {
    id: string;
    title: string;
    status: string;
  } | null;
};

interface RequestReviewDialogProps {
  request: PublicRequestWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: () => Promise<void> | void;
}

const updateStatusSchema = z.object({
  status: z.enum(['pending', 'reviewed', 'approved', 'rejected', 'converted'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
  priority: z
    .enum(['high', 'medium', 'low'], {
      errorMap: () => ({ message: 'Please select a valid priority level' }),
    })
    .optional(),
  category: z
    .enum(['feature', 'bug-fix', 'enhancement', 'infrastructure'], {
      errorMap: () => ({ message: 'Please select a valid category' }),
    })
    .optional(),
  adminNotes: z
    .string()
    .max(1000, 'Admin notes must be less than 1000 characters')
    .optional(),
});

const convertToTaskSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  description: z
    .string()
    .max(2000, 'Description must be less than 2000 characters')
    .optional(),
  status: z
    .enum(['planned', 'in-progress', 'in-review', 'completed'], {
      errorMap: () => ({ message: 'Please select a valid status' }),
    })
    .default('planned'),
  priority: z.enum(['high', 'medium', 'low'], {
    errorMap: () => ({ message: 'Please select a valid priority level' }),
  }),
  category: z.enum(['feature', 'bug-fix', 'enhancement', 'infrastructure'], {
    errorMap: () => ({ message: 'Please select a valid category' }),
  }),
  estimatedTimeline: z
    .string()
    .max(100, 'Timeline must be less than 100 characters')
    .optional(),
});

type UpdateStatusFormData = z.infer<typeof updateStatusSchema>;
type ConvertToTaskFormData = z.infer<typeof convertToTaskSchema>;

// Create a separate component for the dialog content to avoid performance issues
function DialogContent_({
  request,
  onUpdate,
  onOpenChange,
}: {
  request: PublicRequestWithRelations;
  onUpdate: () => void;
  onOpenChange: (open: boolean) => void;
}) {
  const [isConverting, setIsConverting] = React.useState(false);
  const [isUpdating, setIsUpdating] = React.useState(false);

  const updateForm = useForm<UpdateStatusFormData>({
    resolver: zodResolver(updateStatusSchema),
    defaultValues: {
      status: request.status,
      priority: request.priority as 'high' | 'medium' | 'low' | undefined,
      category: request.category as
        | 'feature'
        | 'bug-fix'
        | 'enhancement'
        | 'infrastructure'
        | undefined,
      adminNotes: request.adminNotes || '',
    },
  });

  const convertForm = useForm<ConvertToTaskFormData>({
    resolver: zodResolver(convertToTaskSchema),
    defaultValues: {
      title: request.title,
      description: request.description || '',
      status: 'planned',
      priority: 'medium',
      category: 'feature',
      estimatedTimeline: '',
    },
  });

  const handleUpdateStatus = async (data: UpdateStatusFormData) => {
    setIsUpdating(true);
    try {
      const result = await updateRequestStatus(request.id, {
        status: data.status,
        priority: data.priority,
        category: data.category,
        adminNotes: data.adminNotes,
      });

      if (result.success) {
        toast.success('Request status updated successfully');
        // Trigger data refresh first, then close dialog
        await onUpdate();
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error updating request status:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to update request status'
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleConvertToTask = async (data: ConvertToTaskFormData) => {
    setIsConverting(true);
    try {
      const result = await convertRequestToTask(request.id, {
        title: data.title,
        description: data.description,
        status: data.status,
        priority: data.priority,
        category: data.category,
        estimatedTimeline: data.estimatedTimeline,
      });

      if (result.success) {
        toast.success('Request converted to task successfully');
        // Trigger data refresh first, then close dialog
        await onUpdate();
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error converting request to task:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to convert request to task'
      );
    } finally {
      setIsConverting(false);
    }
  };

  const getStatusBadgeVariant = (status: RequestStatus) => {
    switch (status) {
      case 'pending':
        return 'pending';
      case 'reviewed':
        return 'secondary';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'destructive';
      case 'converted':
        return 'success';
      default:
        return 'outline';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'pending';
      case 'low':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>Review Request</DialogTitle>
        <DialogDescription>
          Review and manage this feature request. You can update its status, add
          notes, or convert it to a roadmap task.
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-6">
        {/* Request Details */}
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg">{request.title}</h3>
            <p className="mt-2 text-muted-foreground">{request.description}</p>
          </div>

          <div className="flex flex-wrap gap-2">
            <Badge variant={getStatusBadgeVariant(request.status)}>
              {title(request.status)}
            </Badge>
            {request.priority && (
              <Badge variant={getPriorityBadgeVariant(request.priority)}>
                {title(request.priority)} Priority
              </Badge>
            )}
            {request.category && (
              <Badge variant="outline">
                {title(request.category.replace('-', ' '))}
              </Badge>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Submitted:</span>{' '}
              {format(new Date(request.createdAt), 'MMM d, yyyy')}
            </div>
            <div>
              <span className="font-medium">Last Updated:</span>{' '}
              {format(new Date(request.updatedAt), 'MMM d, yyyy')}
            </div>
            {request.name && (
              <div>
                <span className="font-medium">Submitter:</span> {request.name}
              </div>
            )}
            {request.email && (
              <div>
                <span className="font-medium">Email:</span> {request.email}
              </div>
            )}
            {request.reviewer && (
              <div>
                <span className="font-medium">Reviewed by:</span>{' '}
                {request.reviewer.name || request.reviewer.email}
              </div>
            )}
            {request.convertedToTask && (
              <div>
                <span className="font-medium">Converted to:</span>{' '}
                <Badge variant="outline" className="ml-1">
                  {request.convertedToTask.title}
                </Badge>
              </div>
            )}
          </div>

          {request.adminNotes && (
            <div>
              <span className="font-medium">Admin Notes:</span>
              <p className="mt-1 text-muted-foreground">{request.adminNotes}</p>
            </div>
          )}
        </div>

        <Separator />

        {/* Action Forms */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Update Status Form */}
          <div className="flex flex-col space-y-4">
            <h4 className="font-semibold">Update Status</h4>
            <Form {...updateForm}>
              <form
                onSubmit={updateForm.handleSubmit(handleUpdateStatus)}
                className="flex flex-1 flex-col space-y-4"
              >
                <div className="flex-1 space-y-4">
                  <FormField
                    control={updateForm.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="reviewed">Reviewed</SelectItem>
                            <SelectItem value="approved">Approved</SelectItem>
                            <SelectItem value="rejected">Rejected</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={updateForm.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={updateForm.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="feature">Feature</SelectItem>
                            <SelectItem value="bug-fix">Bug Fix</SelectItem>
                            <SelectItem value="enhancement">
                              Enhancement
                            </SelectItem>
                            <SelectItem value="infrastructure">
                              Infrastructure
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={updateForm.control}
                    name="adminNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Admin Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Add internal notes about this request..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isUpdating}
                  className="mt-auto w-full"
                >
                  {isUpdating ? 'Updating...' : 'Update Status'}
                </Button>
              </form>
            </Form>
          </div>

          {/* Convert to Task Form */}
          <div className="flex flex-col space-y-4">
            <h4 className="font-semibold">Convert to Task</h4>
            <Form {...convertForm}>
              <form
                onSubmit={convertForm.handleSubmit(handleConvertToTask)}
                className="flex flex-1 flex-col space-y-4"
              >
                <div className="flex-1 space-y-4">
                  <FormField
                    control={convertForm.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Task Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter task title..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={convertForm.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter task description..."
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={convertForm.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priority</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select priority" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="low">Low</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={convertForm.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="feature">Feature</SelectItem>
                              <SelectItem value="bug-fix">Bug Fix</SelectItem>
                              <SelectItem value="enhancement">
                                Enhancement
                              </SelectItem>
                              <SelectItem value="infrastructure">
                                Infrastructure
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={convertForm.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Initial Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="planned">Planned</SelectItem>
                              <SelectItem value="in-progress">
                                In Progress
                              </SelectItem>
                              <SelectItem value="in-review">
                                In Review
                              </SelectItem>
                              <SelectItem value="completed">
                                Completed
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={convertForm.control}
                      name="estimatedTimeline"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Timeline (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Q1 2024" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="mt-auto">
                  <Button
                    type="submit"
                    disabled={isConverting || request.status === 'converted'}
                    className="w-full"
                  >
                    {isConverting ? 'Converting...' : 'Convert to Task'}
                  </Button>
                  {request.status === 'converted' && (
                    <p className="mt-2 text-center text-muted-foreground text-sm">
                      This request has already been converted to a task.
                    </p>
                  )}
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={() => onOpenChange(false)}>
          Close
        </Button>
      </DialogFooter>
    </>
  );
}

export function RequestReviewDialog({
  request,
  open,
  onOpenChange,
  onUpdate,
}: RequestReviewDialogProps) {
  // Early return after all hooks are called
  if (!request || !open) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] min-w-2xl max-w-4xl overflow-y-auto">
        <DialogContent_
          request={request}
          onUpdate={onUpdate}
          onOpenChange={onOpenChange}
        />
      </DialogContent>
    </Dialog>
  );
}
