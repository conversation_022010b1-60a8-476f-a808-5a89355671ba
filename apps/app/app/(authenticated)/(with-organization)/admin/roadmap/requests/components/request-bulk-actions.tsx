'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { CheckSquare, Trash2, X } from 'lucide-react';
import * as React from 'react';
import { updateRequestStatus } from '../../actions';
import type { PublicRequestWithRelations } from './request-columns';

interface RequestBulkActionsProps {
  selectedRequests: string[];
  requests: PublicRequestWithRelations[];
  onClearSelection: () => void;
  onUpdate: () => void;
}

const STATUS_OPTIONS = [
  { value: 'pending', label: 'Pending' },
  { value: 'reviewed', label: 'Reviewed' },
  { value: 'approved', label: 'Approved' },
  { value: 'rejected', label: 'Rejected' },
];

export function RequestBulkActions({
  selectedRequests,
  requests,
  onClearSelection,
  onUpdate,
}: RequestBulkActionsProps) {
  const [bulkActionDialog, setBulkActionDialog] = React.useState<{
    open: boolean;
    action: 'status-change' | 'delete' | null;
    status?: string;
    count: number;
  }>({
    open: false,
    action: null,
    count: 0,
  });

  const handleBulkStatusChange = (status: string) => {
    setBulkActionDialog({
      open: true,
      action: 'status-change',
      status,
      count: selectedRequests.length,
    });
  };

  const handleBulkDelete = () => {
    setBulkActionDialog({
      open: true,
      action: 'delete',
      count: selectedRequests.length,
    });
  };

  const confirmBulkAction = async () => {
    const { action, status } = bulkActionDialog;

    try {
      if (action === 'status-change' && status) {
        // Update status for all selected requests
        await Promise.all(
          selectedRequests.map((requestId) =>
            updateRequestStatus(requestId, { status: status as any })
          )
        );

        toast.success(
          `${selectedRequests.length} request${selectedRequests.length === 1 ? '' : 's'} updated to ${status}`
        );
      } else if (action === 'delete') {
        // For now, we'll mark as rejected instead of actual deletion
        await Promise.all(
          selectedRequests.map((requestId) =>
            updateRequestStatus(requestId, {
              status: 'rejected',
              adminNotes: 'Bulk rejected by admin',
            })
          )
        );

        toast.success(
          `${selectedRequests.length} request${selectedRequests.length === 1 ? '' : 's'} rejected`
        );
      }

      onUpdate();
      onClearSelection();
    } catch (error) {
      toast.error('Failed to update requests');
      console.error('Bulk action error:', error);
    }

    setBulkActionDialog({ open: false, action: null, count: 0 });
  };

  if (selectedRequests.length === 0) {
    return null;
  }

  return (
    <>
      <div className="flex items-center justify-between rounded-lg border bg-muted/50 p-3 mb-4">
        <div className="flex items-center gap-2">
          <CheckSquare className="h-4 w-4" />
          <span className="text-sm font-medium">
            {selectedRequests.length} request
            {selectedRequests.length === 1 ? '' : 's'} selected
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Select onValueChange={handleBulkStatusChange}>
            <SelectTrigger className="w-[140px] h-8">
              <SelectValue placeholder="Change status" />
            </SelectTrigger>
            <SelectContent>
              {STATUS_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkDelete}
            className="gap-1 text-destructive hover:text-destructive"
          >
            <Trash2 className="h-3 w-3" />
            Reject
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="gap-1"
          >
            <X className="h-3 w-3" />
            Clear
          </Button>
        </div>
      </div>

      <AlertDialog
        open={bulkActionDialog.open}
        onOpenChange={(open) =>
          setBulkActionDialog({ ...bulkActionDialog, open })
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {bulkActionDialog.action === 'status-change'
                ? 'Update Request Status'
                : 'Reject Requests'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {bulkActionDialog.action === 'status-change'
                ? `Are you sure you want to update ${bulkActionDialog.count} request${bulkActionDialog.count === 1 ? '' : 's'} to "${bulkActionDialog.status}"?`
                : `Are you sure you want to reject ${bulkActionDialog.count} request${bulkActionDialog.count === 1 ? '' : 's'}?`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBulkAction}>
              {bulkActionDialog.action === 'status-change'
                ? 'Update'
                : 'Reject'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
