'use server';

import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import {
  sendRequestConversionNotification,
  sendRequestStatusNotification,
} from '@repo/email';
import { log } from '@repo/observability/log';
import { revalidatePath, revalidateTag } from 'next/cache';
import { headers } from 'next/headers';
import { z } from 'zod';

// Validation schemas
const roadmapTaskSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  description: z
    .string()
    .max(2000, 'Description must be less than 2000 characters')
    .optional(),
  status: z.enum(['planned', 'in-progress', 'in-review', 'completed']),
  priority: z.enum(['high', 'medium', 'low']),
  category: z.enum(['feature', 'bug-fix', 'enhancement', 'infrastructure']),
  estimatedTimeline: z
    .string()
    .max(100, 'Timeline must be less than 100 characters')
    .optional(),
  order: z.number().int().min(0).optional(),
});

const reorderTasksSchema = z.object({
  taskId: z.string(),
  newStatus: z.enum(['planned', 'in-progress', 'in-review', 'completed']),
  newOrder: z.number().int().min(0),
});

const updateRequestStatusSchema = z.object({
  status: z.enum(['pending', 'reviewed', 'approved', 'rejected', 'converted']),
  priority: z.enum(['high', 'medium', 'low']).optional(),
  category: z
    .enum(['feature', 'bug-fix', 'enhancement', 'infrastructure'])
    .optional(),
  adminNotes: z
    .string()
    .max(1000, 'Admin notes must be less than 1000 characters')
    .optional(),
});

const convertRequestSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  description: z
    .string()
    .max(2000, 'Description must be less than 2000 characters')
    .optional(),
  status: z
    .enum(['planned', 'in-progress', 'in-review', 'completed'])
    .default('planned'),
  priority: z.enum(['high', 'medium', 'low']),
  category: z.enum(['feature', 'bug-fix', 'enhancement', 'infrastructure']),
  estimatedTimeline: z
    .string()
    .max(100, 'Timeline must be less than 100 characters')
    .optional(),
});

export type RoadmapTaskInput = z.infer<typeof roadmapTaskSchema>;
export type ReorderTasksInput = z.infer<typeof reorderTasksSchema>;
export type UpdateRequestStatusInput = z.infer<
  typeof updateRequestStatusSchema
>;
export type ConvertRequestInput = z.infer<typeof convertRequestSchema>;

// Helper function to check admin authorization
async function checkAdminAuth() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!isSuperAdmin(toAuthResult(session))) {
    throw new Error('Unauthorized: Only super admins can manage roadmap');
  }

  return session;
}

/**
 * Get all roadmap tasks for admin view
 */
export async function getRoadmapTasks() {
  log.debug('getRoadmapTasks called');

  try {
    await checkAdminAuth();

    const tasks = await database.roadmapTask.findMany({
      orderBy: [{ status: 'asc' }, { order: 'asc' }, { createdAt: 'desc' }],
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    log.info('Roadmap tasks fetched successfully', {
      count: tasks.length,
    });

    return tasks;
  } catch (error) {
    log.error('Error fetching roadmap tasks', { error });
    throw error;
  }
}

/**
 * Create a new roadmap task
 */
export async function createRoadmapTask(input: RoadmapTaskInput) {
  log.debug('createRoadmapTask called', { title: input.title });

  try {
    const session = await checkAdminAuth();
    const validatedInput = roadmapTaskSchema.parse(input);

    // Get the highest order for the specified status to append new task at the end
    const maxOrderTask = await database.roadmapTask.findFirst({
      where: { status: validatedInput.status },
      orderBy: { order: 'desc' },
      select: { order: true },
    });

    const newOrder = validatedInput.order ?? (maxOrderTask?.order ?? 0) + 1;

    const task = await database.roadmapTask.create({
      data: {
        title: validatedInput.title,
        description: validatedInput.description || null,
        status: validatedInput.status,
        priority: validatedInput.priority,
        category: validatedInput.category,
        estimatedTimeline: validatedInput.estimatedTimeline || null,
        order: newOrder,
        createdBy: session.user.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    log.info('Roadmap task created successfully', {
      taskId: task.id,
      title: task.title,
    });

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidateTag('roadmap-tasks');

    return {
      success: true,
      data: task,
    };
  } catch (error) {
    log.error('Error creating roadmap task', { error });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      throw new Error(`Validation error: ${errorMessages}`);
    }

    throw error;
  }
}

/**
 * Update an existing roadmap task
 */
export async function updateRoadmapTask(
  taskId: string,
  input: Partial<RoadmapTaskInput>
) {
  log.debug('updateRoadmapTask called', { taskId, input });

  try {
    await checkAdminAuth();
    const validatedInput = roadmapTaskSchema.partial().parse(input);

    const task = await database.roadmapTask.update({
      where: { id: taskId },
      data: {
        ...(validatedInput.title && { title: validatedInput.title }),
        ...(validatedInput.description !== undefined && {
          description: validatedInput.description || null,
        }),
        ...(validatedInput.status && { status: validatedInput.status }),
        ...(validatedInput.priority && { priority: validatedInput.priority }),
        ...(validatedInput.category && { category: validatedInput.category }),
        ...(validatedInput.estimatedTimeline !== undefined && {
          estimatedTimeline: validatedInput.estimatedTimeline || null,
        }),
        ...(validatedInput.order !== undefined && {
          order: validatedInput.order,
        }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    log.info('Roadmap task updated successfully', { taskId });

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidateTag('roadmap-tasks');

    return {
      success: true,
      data: task,
    };
  } catch (error) {
    log.error('Error updating roadmap task', { error, taskId });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      throw new Error(`Validation error: ${errorMessages}`);
    }

    throw error;
  }
}

/**
 * Delete a roadmap task
 */
export async function deleteRoadmapTask(taskId: string) {
  log.debug('deleteRoadmapTask called', { taskId });

  try {
    await checkAdminAuth();

    // Use a transaction to handle the deletion and any related cleanup
    await database.$transaction(async (tx) => {
      // First, check if this task was converted from a request
      const convertedRequest = await tx.publicRequest.findFirst({
        where: { convertedToTaskId: taskId },
      });

      // If there's a converted request, update its status back to reviewed
      if (convertedRequest) {
        await tx.publicRequest.update({
          where: { id: convertedRequest.id },
          data: {
            status: 'reviewed',
            convertedToTaskId: null,
          },
        });
      }

      // Delete the task
      await tx.roadmapTask.delete({
        where: { id: taskId },
      });
    });

    log.info('Roadmap task deleted successfully', { taskId });

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidateTag('roadmap-tasks');
    revalidateTag('public-requests');

    return {
      success: true,
    };
  } catch (error) {
    log.error('Error deleting roadmap task', { error, taskId });
    throw error;
  }
}

/**
 * Reorder roadmap tasks (for drag-and-drop functionality)
 */
export async function reorderRoadmapTasks(updates: ReorderTasksInput[]) {
  log.debug('reorderRoadmapTasks called', { updates });

  try {
    await checkAdminAuth();
    const validatedUpdates = updates.map((update) =>
      reorderTasksSchema.parse(update)
    );

    // Use a transaction to update all tasks atomically
    await database.$transaction(async (tx) => {
      for (const update of validatedUpdates) {
        await tx.roadmapTask.update({
          where: { id: update.taskId },
          data: {
            status: update.newStatus,
            order: update.newOrder,
          },
        });
      }
    });

    log.info('Roadmap tasks reordered successfully', {
      count: validatedUpdates.length,
    });

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidateTag('roadmap-tasks');

    return {
      success: true,
    };
  } catch (error) {
    log.error('Error reordering roadmap tasks', { error });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      throw new Error(`Validation error: ${errorMessages}`);
    }

    throw error;
  }
}

/**
 * Get all public requests for admin review
 */
export async function getPublicRequests() {
  log.debug('getPublicRequests called');

  try {
    await checkAdminAuth();

    const requests = await database.publicRequest.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        convertedToTask: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
      },
    });

    log.info('Public requests fetched successfully', {
      count: requests.length,
    });

    return requests;
  } catch (error) {
    log.error('Error fetching public requests', { error });
    throw error;
  }
}

/**
 * Update the status of a public request
 */
export async function updateRequestStatus(
  requestId: string,
  input: UpdateRequestStatusInput
) {
  log.debug('updateRequestStatus called', { requestId, input });

  try {
    const session = await checkAdminAuth();
    const validatedInput = updateRequestStatusSchema.parse(input);

    const request = await database.publicRequest.update({
      where: { id: requestId },
      data: {
        status: validatedInput.status,
        priority: validatedInput.priority || null,
        category: validatedInput.category || null,
        adminNotes: validatedInput.adminNotes || null,
        reviewedBy: session.user.id,
      },
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        convertedToTask: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
      },
    });

    log.info('Request status updated successfully', {
      requestId,
      status: validatedInput.status,
    });

    // Send email notification if the request has an email and status changed
    if (request.email && validatedInput.status !== 'pending') {
      try {
        await sendRequestStatusNotification({
          to: request.email,
          requestTitle: request.title,
          status: validatedInput.status,
          adminNotes: validatedInput.adminNotes || undefined,
          submitterName: request.name || undefined,
        });

        log.info('Request status notification sent', {
          requestId,
          email: request.email,
          status: validatedInput.status,
        });
      } catch (emailError) {
        // Log email error but don't fail the request update
        log.error('Failed to send request status notification', {
          requestId,
          email: request.email,
          error: emailError,
        });
      }
    }

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap/requests');
    revalidateTag('public-requests');

    return {
      success: true,
      data: request,
    };
  } catch (error) {
    log.error('Error updating request status', { error, requestId });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      throw new Error(`Validation error: ${errorMessages}`);
    }

    throw error;
  }
}

/**
 * Bulk update roadmap tasks
 */
export async function bulkUpdateRoadmapTasks(
  taskIds: string[],
  updates: Partial<RoadmapTaskInput>
) {
  log.debug('bulkUpdateRoadmapTasks called', { taskIds, updates });

  try {
    await checkAdminAuth();
    const validatedUpdates = roadmapTaskSchema.partial().parse(updates);

    // Use a transaction to update all tasks atomically
    const updatedTasks = await database.$transaction(async (tx) => {
      const tasks = [];
      for (const taskId of taskIds) {
        const task = await tx.roadmapTask.update({
          where: { id: taskId },
          data: {
            ...(validatedUpdates.title && { title: validatedUpdates.title }),
            ...(validatedUpdates.description !== undefined && {
              description: validatedUpdates.description || null,
            }),
            ...(validatedUpdates.status && { status: validatedUpdates.status }),
            ...(validatedUpdates.priority && {
              priority: validatedUpdates.priority,
            }),
            ...(validatedUpdates.category && {
              category: validatedUpdates.category,
            }),
            ...(validatedUpdates.estimatedTimeline !== undefined && {
              estimatedTimeline: validatedUpdates.estimatedTimeline || null,
            }),
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });
        tasks.push(task);
      }
      return tasks;
    });

    log.info('Roadmap tasks bulk updated successfully', {
      count: taskIds.length,
    });

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidateTag('roadmap-tasks');

    return {
      success: true,
      data: updatedTasks,
    };
  } catch (error) {
    log.error('Error bulk updating roadmap tasks', { error, taskIds });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      throw new Error(`Validation error: ${errorMessages}`);
    }

    throw error;
  }
}

/**
 * Bulk delete roadmap tasks
 */
export async function bulkDeleteRoadmapTasks(taskIds: string[]) {
  log.debug('bulkDeleteRoadmapTasks called', { taskIds });

  try {
    await checkAdminAuth();

    // Use a transaction to handle the deletion and any related cleanup
    await database.$transaction(async (tx) => {
      // First, check if any of these tasks were converted from requests
      const convertedRequests = await tx.publicRequest.findMany({
        where: { convertedToTaskId: { in: taskIds } },
      });

      // Update converted requests back to reviewed status
      if (convertedRequests.length > 0) {
        await tx.publicRequest.updateMany({
          where: { convertedToTaskId: { in: taskIds } },
          data: {
            status: 'reviewed',
            convertedToTaskId: null,
          },
        });
      }

      // Delete the tasks
      await tx.roadmapTask.deleteMany({
        where: { id: { in: taskIds } },
      });
    });

    log.info('Roadmap tasks bulk deleted successfully', {
      count: taskIds.length,
    });

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidateTag('roadmap-tasks');
    revalidateTag('public-requests');

    return {
      success: true,
    };
  } catch (error) {
    log.error('Error bulk deleting roadmap tasks', { error, taskIds });
    throw error;
  }
}

/**
 * Convert a public request to a roadmap task
 */
export async function convertRequestToTask(
  requestId: string,
  input: ConvertRequestInput
) {
  log.debug('convertRequestToTask called', { requestId, input });

  try {
    const session = await checkAdminAuth();
    const validatedInput = convertRequestSchema.parse(input);

    // Use a transaction to create the task and update the request atomically
    const result = await database.$transaction(async (tx) => {
      // Get the highest order for the specified status
      const maxOrderTask = await tx.roadmapTask.findFirst({
        where: { status: validatedInput.status },
        orderBy: { order: 'desc' },
        select: { order: true },
      });

      const newOrder = (maxOrderTask?.order ?? 0) + 1;

      // Create the new roadmap task
      const task = await tx.roadmapTask.create({
        data: {
          title: validatedInput.title,
          description: validatedInput.description || null,
          status: validatedInput.status,
          priority: validatedInput.priority,
          category: validatedInput.category,
          estimatedTimeline: validatedInput.estimatedTimeline || null,
          order: newOrder,
          createdBy: session.user.id,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // Update the request to mark it as converted
      const updatedRequest = await tx.publicRequest.update({
        where: { id: requestId },
        data: {
          status: 'converted',
          convertedToTaskId: task.id,
          reviewedBy: session.user.id,
        },
        include: {
          reviewer: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return { task, request: updatedRequest };
    });

    log.info('Request converted to task successfully', {
      requestId,
      taskId: result.task.id,
      title: validatedInput.title,
    });

    // Send email notification if the request has an email
    if (result.request.email) {
      try {
        await sendRequestConversionNotification({
          to: result.request.email,
          requestTitle: result.request.title,
          taskTitle: validatedInput.title,
          submitterName: result.request.name || undefined,
        });

        log.info('Request conversion notification sent', {
          requestId,
          taskId: result.task.id,
          email: result.request.email,
        });
      } catch (emailError) {
        // Log email error but don't fail the conversion
        log.error('Failed to send request conversion notification', {
          requestId,
          taskId: result.task.id,
          email: result.request.email,
          error: emailError,
        });
      }
    }

    // Revalidate relevant paths and tags
    revalidatePath('/admin/roadmap');
    revalidatePath('/admin/roadmap/requests');
    revalidateTag('roadmap-tasks');
    revalidateTag('public-requests');

    return {
      success: true,
      data: {
        task: result.task,
        request: result.request,
      },
    };
  } catch (error) {
    log.error('Error converting request to task', { error, requestId });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      throw new Error(`Validation error: ${errorMessages}`);
    }

    throw error;
  }
}
