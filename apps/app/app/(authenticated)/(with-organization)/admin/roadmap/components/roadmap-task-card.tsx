'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@repo/design-system/components/ui/dropdown-menu';
import { KanbanCard } from '@repo/design-system/components/ui/kibo-ui/kanban';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  AlertCircle,
  Calendar,
  Edit,
  MoreHorizontal,
  Tag,
  Trash2,
} from 'lucide-react';
import * as React from 'react';
import { deleteRoadmapTask } from '../actions';
import type { RoadmapTask } from './roadmap-kanban';
import { RoadmapTaskDialog } from './roadmap-task-dialog';

interface RoadmapTaskCardProps {
  task: RoadmapTask;
  onUpdate: (task: RoadmapTask) => void;
  onDelete: (taskId: string) => void;
  disabled?: boolean;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
}

const PRIORITY_COLORS = {
  high: 'bg-red-100 text-red-800 border-red-200',
  medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  low: 'bg-green-100 text-green-800 border-green-200',
};

const CATEGORY_COLORS = {
  feature: 'bg-blue-100 text-blue-800 border-blue-200',
  'bug-fix': 'bg-red-100 text-red-800 border-red-200',
  enhancement: 'bg-purple-100 text-purple-800 border-purple-200',
  infrastructure: 'bg-gray-100 text-gray-800 border-gray-200',
};

export function RoadmapTaskCard({
  task,
  onUpdate,
  onDelete,
  disabled,
  selected,
  onSelect,
}: RoadmapTaskCardProps) {
  const [showEditDialog, setShowEditDialog] = React.useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleEdit = () => {
    setShowEditDialog(true);
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteRoadmapTask(task.id);
      onDelete(task.id);
      toast.success('Task deleted successfully');
      setShowDeleteDialog(false);
    } catch (error) {
      toast.error('Failed to delete task. Please try again.');
      console.error('Error deleting task:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleTaskUpdate = (updatedTask: RoadmapTask) => {
    onUpdate(updatedTask);
    setShowEditDialog(false);
  };

  return (
    <>
      <KanbanCard
        id={task.id}
        name={task.name}
        column={task.column}
        className={`group touch-manipulation ${disabled ? 'opacity-50' : ''} ${selected ? 'ring-2 ring-primary' : ''}`}
      >
        <div className="space-y-3 p-1 sm:p-0">
          {/* Header with selection, title and actions */}
          <div className="flex items-start justify-between gap-2">
            <div className="flex items-start gap-2 flex-1 min-w-0">
              {onSelect && (
                <Checkbox
                  checked={selected}
                  onCheckedChange={onSelect}
                  className="mt-0.5 flex-shrink-0"
                  onClick={(e) => e.stopPropagation()}
                />
              )}
              <h3 className="font-medium text-sm sm:text-sm leading-tight flex-1 break-words">
                {task.title}
              </h3>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100 sm:opacity-0 sm:group-hover:opacity-100 touch:opacity-100 flex-shrink-0"
                  disabled={disabled}
                  onPointerDown={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-40"
                onPointerDown={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onClick={(e) => e.stopPropagation()}
              >
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit();
                  }}
                  onPointerDown={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowDeleteDialog(true);
                  }}
                  onPointerDown={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Description */}
          {task.description && (
            <p className="overflow-hidden text-ellipsis text-muted-foreground text-xs leading-relaxed">
              {task.description && task.description.length > 80
                ? `${task.description.substring(0, 80)}...`
                : task.description}
            </p>
          )}

          {/* Priority and Category badges */}
          <div className="flex flex-col gap-2 sm:flex-row sm:flex-wrap sm:gap-1">
            <Badge
              variant="outline"
              className={`text-xs w-fit ${PRIORITY_COLORS[task.priority]}`}
            >
              <AlertCircle className="mr-1 h-3 w-3" />
              {task.priority}
            </Badge>
            <Badge
              variant="outline"
              className={`text-xs w-fit ${CATEGORY_COLORS[task.category]}`}
            >
              <Tag className="mr-1 h-3 w-3" />
              {task.category}
            </Badge>
          </div>

          {/* Timeline */}
          {task.estimatedTimeline && (
            <div className="flex items-center gap-1 text-muted-foreground text-xs">
              <Calendar className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">{task.estimatedTimeline}</span>
            </div>
          )}
        </div>
      </KanbanCard>

      {/* Edit Dialog */}
      <RoadmapTaskDialog
        mode="edit"
        task={task}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSave={handleTaskUpdate}
        onDelete={onDelete}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Task</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{task.title}"? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
