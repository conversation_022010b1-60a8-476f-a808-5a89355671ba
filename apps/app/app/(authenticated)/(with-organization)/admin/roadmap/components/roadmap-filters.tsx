'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { useDebounce } from '@repo/design-system/hooks/use-debounce';
import { Archive, CheckSquare, Filter, Search, Trash2, X } from 'lucide-react';
import * as React from 'react';
import type { RoadmapTask } from './roadmap-kanban';

export interface FilterState {
  search: string;
  status: string[];
  priority: string[];
  category: string[];
  createdBy: string[];
}

interface RoadmapFiltersProps {
  tasks: RoadmapTask[];
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  selectedTasks: string[];
  onBulkAction: (
    action: 'delete' | 'archive' | 'status-change',
    data?: any
  ) => void;
  onClearSelection: () => void;
}

const STATUS_OPTIONS = [
  { value: 'planned', label: 'Planned' },
  { value: 'in-progress', label: 'In Progress' },
  { value: 'in-review', label: 'In Review' },
  { value: 'completed', label: 'Completed' },
];

const PRIORITY_OPTIONS = [
  { value: 'high', label: 'High' },
  { value: 'medium', label: 'Medium' },
  { value: 'low', label: 'Low' },
];

const CATEGORY_OPTIONS = [
  { value: 'feature', label: 'Feature' },
  { value: 'bug-fix', label: 'Bug Fix' },
  { value: 'enhancement', label: 'Enhancement' },
  { value: 'infrastructure', label: 'Infrastructure' },
];

export function RoadmapFilters({
  tasks,
  filters,
  onFiltersChange,
  selectedTasks,
  onBulkAction,
  onClearSelection,
}: RoadmapFiltersProps) {
  const [search, setSearch] = React.useState(filters.search);
  const debouncedSearch = useDebounce(search, 300);

  // Update filters when debounced search changes
  React.useEffect(() => {
    if (debouncedSearch !== filters.search) {
      onFiltersChange({ ...filters, search: debouncedSearch });
    }
  }, [debouncedSearch, filters, onFiltersChange]);

  // Get unique creators from tasks
  const creators = React.useMemo(() => {
    const uniqueCreators = new Map();
    tasks.forEach((task) => {
      if (task.createdBy && task.user) {
        uniqueCreators.set(task.createdBy, {
          id: task.createdBy,
          name: task.user.name || task.user.email || 'Unknown',
        });
      }
    });
    return Array.from(uniqueCreators.values());
  }, [tasks]);

  const handleFilterChange = (key: keyof FilterState, value: string[]) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const toggleFilter = (key: keyof FilterState, value: string) => {
    const currentValues = filters[key] as string[];
    const newValues = currentValues.includes(value)
      ? currentValues.filter((v) => v !== value)
      : [...currentValues, value];
    handleFilterChange(key, newValues);
  };

  const clearAllFilters = () => {
    setSearch('');
    onFiltersChange({
      search: '',
      status: [],
      priority: [],
      category: [],
      createdBy: [],
    });
  };

  const hasActiveFilters =
    filters.search ||
    filters.status.length > 0 ||
    filters.priority.length > 0 ||
    filters.category.length > 0 ||
    filters.createdBy.length > 0;

  const activeFilterCount =
    (filters.search ? 1 : 0) +
    filters.status.length +
    filters.priority.length +
    filters.category.length +
    filters.createdBy.length;

  return (
    <div className="space-y-3 sm:space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
        <div className="relative flex-1 sm:max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search tasks..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-9 h-10"
          />
        </div>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="gap-2 w-full sm:w-auto">
              <Filter className="h-4 w-4" />
              <span className="sm:inline">Filters</span>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="start">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Filters</h4>
                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="h-auto p-1 text-xs"
                  >
                    Clear all
                  </Button>
                )}
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Status</Label>
                <div className="space-y-2">
                  {STATUS_OPTIONS.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`status-${option.value}`}
                        checked={filters.status.includes(option.value)}
                        onCheckedChange={() =>
                          toggleFilter('status', option.value)
                        }
                      />
                      <Label
                        htmlFor={`status-${option.value}`}
                        className="text-sm font-normal"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Priority Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Priority</Label>
                <div className="space-y-2">
                  {PRIORITY_OPTIONS.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`priority-${option.value}`}
                        checked={filters.priority.includes(option.value)}
                        onCheckedChange={() =>
                          toggleFilter('priority', option.value)
                        }
                      />
                      <Label
                        htmlFor={`priority-${option.value}`}
                        className="text-sm font-normal"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <div className="space-y-2">
                  {CATEGORY_OPTIONS.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`category-${option.value}`}
                        checked={filters.category.includes(option.value)}
                        onCheckedChange={() =>
                          toggleFilter('category', option.value)
                        }
                      />
                      <Label
                        htmlFor={`category-${option.value}`}
                        className="text-sm font-normal"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Creator Filter */}
              {creators.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Created By</Label>
                  <div className="space-y-2">
                    {creators.map((creator) => (
                      <div
                        key={creator.id}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          id={`creator-${creator.id}`}
                          checked={filters.createdBy.includes(creator.id)}
                          onCheckedChange={() =>
                            toggleFilter('createdBy', creator.id)
                          }
                        />
                        <Label
                          htmlFor={`creator-${creator.id}`}
                          className="text-sm font-normal"
                        >
                          {creator.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-sm text-muted-foreground hidden sm:inline">
            Active filters:
          </span>

          {filters.search && (
            <Badge variant="secondary" className="gap-1 text-xs">
              Search: "
              {filters.search.length > 15
                ? `${filters.search.substring(0, 15)}...`
                : filters.search}
              "
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => {
                  setSearch('');
                  onFiltersChange({ ...filters, search: '' });
                }}
              />
            </Badge>
          )}

          {filters.status.map((status) => (
            <Badge
              key={`status-${status}`}
              variant="secondary"
              className="gap-1 text-xs"
            >
              <span className="hidden sm:inline">Status: </span>
              {STATUS_OPTIONS.find((o) => o.value === status)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleFilter('status', status)}
              />
            </Badge>
          ))}

          {filters.priority.map((priority) => (
            <Badge
              key={`priority-${priority}`}
              variant="secondary"
              className="gap-1 text-xs"
            >
              <span className="hidden sm:inline">Priority: </span>
              {PRIORITY_OPTIONS.find((o) => o.value === priority)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleFilter('priority', priority)}
              />
            </Badge>
          ))}

          {filters.category.map((category) => (
            <Badge
              key={`category-${category}`}
              variant="secondary"
              className="gap-1 text-xs"
            >
              <span className="hidden sm:inline">Category: </span>
              {CATEGORY_OPTIONS.find((o) => o.value === category)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleFilter('category', category)}
              />
            </Badge>
          ))}

          {filters.createdBy.map((creatorId) => (
            <Badge
              key={`creator-${creatorId}`}
              variant="secondary"
              className="gap-1 text-xs"
            >
              <span className="hidden sm:inline">Creator: </span>
              {creators.find((c) => c.id === creatorId)?.name}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleFilter('createdBy', creatorId)}
              />
            </Badge>
          ))}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-6 px-2 text-xs"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedTasks.length > 0 && (
        <div className="flex flex-col gap-3 rounded-lg border bg-muted/50 p-3 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-2">
            <CheckSquare className="h-4 w-4" />
            <span className="text-sm font-medium">
              {selectedTasks.length} task{selectedTasks.length === 1 ? '' : 's'}{' '}
              selected
            </span>
          </div>

          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
            <Select
              onValueChange={(value) => onBulkAction('status-change', value)}
            >
              <SelectTrigger className="w-full sm:w-[140px] h-9">
                <SelectValue placeholder="Change status" />
              </SelectTrigger>
              <SelectContent>
                {STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('archive')}
                className="gap-1 flex-1 sm:flex-none"
              >
                <Archive className="h-3 w-3" />
                <span className="sm:inline">Archive</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction('delete')}
                className="gap-1 text-destructive hover:text-destructive flex-1 sm:flex-none"
              >
                <Trash2 className="h-3 w-3" />
                <span className="sm:inline">Delete</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
                className="gap-1 flex-1 sm:flex-none"
              >
                <X className="h-3 w-3" />
                <span className="sm:inline">Clear</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
