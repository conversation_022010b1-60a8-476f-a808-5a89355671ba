'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import {
  type DragEndEvent,
  KanbanBoard,
  KanbanCards,
  KanbanHeader,
  KanbanProvider,
} from '@repo/design-system/components/ui/kibo-ui/kanban';
import { toast } from '@repo/design-system/components/ui/sonner';
import { log } from '@repo/observability/log';
import * as React from 'react';
import {
  bulkDeleteRoadmapTasks,
  bulkUpdateRoadmapTasks,
  reorderRoadmapTasks,
} from '../actions';
import { type FilterState, RoadmapFilters } from './roadmap-filters';
import { RoadmapTaskCard } from './roadmap-task-card';

// Define the roadmap task type based on the schema
export interface RoadmapTask {
  id: string;
  title: string;
  description?: string;
  status: 'planned' | 'in-progress' | 'in-review' | 'completed';
  priority: 'high' | 'medium' | 'low';
  category: 'feature' | 'bug-fix' | 'enhancement' | 'infrastructure';
  estimatedTimeline?: string;
  order: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  column: string; // Required by kanban component
  name: string; // Required by kanban component
  [key: string]: unknown; // Index signature for KanbanItemProps compatibility
}

// Define kanban columns
const KANBAN_COLUMNS = [
  { id: 'planned', name: 'Planned' },
  { id: 'in-progress', name: 'In Progress' },
  { id: 'in-review', name: 'In Review' },
  { id: 'completed', name: 'Completed' },
];

interface RoadmapKanbanProps {
  initialTasks: any[];
}

export function RoadmapKanban({ initialTasks }: RoadmapKanbanProps) {
  // Transform tasks to match kanban component requirements
  const [tasks, setTasks] = React.useState<RoadmapTask[]>(() =>
    initialTasks.map((task) => ({
      ...task,
      column: task.status,
      name: task.title,
    }))
  );

  const [isReordering, setIsReordering] = React.useState(false);
  const [selectedTasks, setSelectedTasks] = React.useState<string[]>([]);
  const [filters, setFilters] = React.useState<FilterState>({
    search: '',
    status: [],
    priority: [],
    category: [],
    createdBy: [],
  });
  const [bulkActionDialog, setBulkActionDialog] = React.useState<{
    open: boolean;
    action: 'delete' | 'archive' | null;
    count: number;
  }>({
    open: false,
    action: null,
    count: 0,
  });

  const handleDragEnd = async (event: DragEndEvent) => {
    log.debug('handleDragEnd called', {
      active: event.active.id,
      over: event.over?.id,
    });

    const { active, over } = event;

    if (!over || active.id === over.id) {
      log.debug('Early return: no over or same id');
      return;
    }

    const activeTask = tasks.find((task) => task.id === active.id);
    if (!activeTask) {
      log.debug('Early return: no active task found');
      return;
    }

    // Determine if we're dropping on a column or on another task
    const isDroppedOnColumn = KANBAN_COLUMNS.some((col) => col.id === over.id);
    log.debug('isDroppedOnColumn:', { isDroppedOnColumn, overId: over.id });

    let newStatus: string;
    let newOrder: number;

    if (isDroppedOnColumn) {
      // Dropped on a column - append to the end of that column
      newStatus = over.id as string;
      const tasksInNewColumn = tasks.filter(
        (task) => task.column === newStatus && task.id !== active.id
      );
      newOrder = Math.max(...tasksInNewColumn.map((t) => t.order || 0), 0) + 1;
      log.debug('Dropped on column:', { newStatus, newOrder });
    } else {
      // Dropped on another task - get the target task's position
      const overTask = tasks.find((task) => task.id === over.id);
      if (!overTask) {
        log.debug('Early return: no over task found');
        return;
      }

      newStatus = overTask.column;
      newOrder = overTask.order || 0;
      log.debug('Dropped on task:', {
        overtaskId: overTask.id,
        newStatus,
        newOrder,
      });
    }

    log.debug('Current status:', { oldStatus: activeTask.status, newStatus });

    // Only proceed if status actually changed
    if (activeTask.status === newStatus) {
      log.debug('Early return: status unchanged');
      return;
    }

    // Optimistic update
    const previousTasks = [...tasks];
    setTasks((prevTasks) =>
      prevTasks.map((task) =>
        task.id === active.id
          ? {
              ...task,
              status: newStatus as any,
              column: newStatus,
              order: newOrder,
            }
          : task
      )
    );

    setIsReordering(true);

    try {
      log.debug('Calling reorderRoadmapTasks with:', {
        taskId: active.id as string,
        newStatus: newStatus as any,
        newOrder,
      });

      await reorderRoadmapTasks([
        {
          taskId: active.id as string,
          newStatus: newStatus as any,
          newOrder,
        },
      ]);

      toast.success('Task moved successfully');
      log.debug('Task moved successfully');
    } catch (error) {
      // Rollback on error
      setTasks(previousTasks);
      toast.error('Failed to move task. Please try again.');
      console.error('Error reordering tasks:', error);
    } finally {
      setIsReordering(false);
    }
  };

  const handleTaskUpdate = (updatedTask: RoadmapTask) => {
    setTasks((prevTasks) =>
      prevTasks.map((task) =>
        task.id === updatedTask.id
          ? {
              ...updatedTask,
              column: updatedTask.status,
              name: updatedTask.title,
            }
          : task
      )
    );
  };

  const handleTaskDelete = (taskId: string) => {
    setTasks((prevTasks) => prevTasks.filter((task) => task.id !== taskId));
  };

  const _handleTaskCreate = (newTask: RoadmapTask) => {
    setTasks((prevTasks) => [
      ...prevTasks,
      { ...newTask, column: newTask.status, name: newTask.title },
    ]);
  };

  // Filter tasks based on current filters
  const filteredTasks = React.useMemo(() => {
    return tasks.filter((task) => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        const matchesSearch =
          task.title.toLowerCase().includes(searchLower) ||
          task.description?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(task.status)) {
        return false;
      }

      // Priority filter
      if (
        filters.priority.length > 0 &&
        !filters.priority.includes(task.priority)
      ) {
        return false;
      }

      // Category filter
      if (
        filters.category.length > 0 &&
        !filters.category.includes(task.category)
      ) {
        return false;
      }

      // Creator filter
      if (
        filters.createdBy.length > 0 &&
        task.createdBy &&
        !filters.createdBy.includes(task.createdBy)
      ) {
        return false;
      }

      return true;
    });
  }, [tasks, filters]);

  // Handle task selection
  const handleTaskSelect = (taskId: string, selected: boolean) => {
    setSelectedTasks((prev) =>
      selected ? [...prev, taskId] : prev.filter((id) => id !== taskId)
    );
  };

  const _handleSelectAll = (selected: boolean) => {
    setSelectedTasks(selected ? filteredTasks.map((task) => task.id) : []);
  };

  const clearSelection = () => {
    setSelectedTasks([]);
  };

  // Handle bulk operations
  const handleBulkAction = async (
    action: 'delete' | 'archive' | 'status-change',
    data?: any
  ) => {
    if (action === 'status-change' && data) {
      // Handle bulk status change
      try {
        await bulkUpdateRoadmapTasks(selectedTasks, { status: data });

        // Update local state
        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            selectedTasks.includes(task.id)
              ? { ...task, status: data, column: data }
              : task
          )
        );

        toast.success(
          `${selectedTasks.length} task${selectedTasks.length === 1 ? '' : 's'} updated`
        );
        clearSelection();
      } catch (error) {
        toast.error('Failed to update tasks');
        console.error('Bulk status change error:', error);
      }
    } else if (action === 'delete' || action === 'archive') {
      setBulkActionDialog({
        open: true,
        action,
        count: selectedTasks.length,
      });
    }
  };

  const confirmBulkAction = async () => {
    const { action } = bulkActionDialog;

    if (action === 'delete') {
      try {
        // Use bulk delete API
        await bulkDeleteRoadmapTasks(selectedTasks);

        setTasks((prevTasks) =>
          prevTasks.filter((task) => !selectedTasks.includes(task.id))
        );

        toast.success(
          `${selectedTasks.length} task${selectedTasks.length === 1 ? '' : 's'} deleted`
        );
      } catch (error) {
        toast.error('Failed to delete tasks');
        console.error('Bulk delete error:', error);
      }
    } else if (action === 'archive') {
      try {
        // Archive by updating status to completed
        await bulkUpdateRoadmapTasks(selectedTasks, { status: 'completed' });

        setTasks((prevTasks) =>
          prevTasks.map((task) =>
            selectedTasks.includes(task.id)
              ? { ...task, status: 'completed', column: 'completed' }
              : task
          )
        );

        toast.success(
          `${selectedTasks.length} task${selectedTasks.length === 1 ? '' : 's'} archived`
        );
      } catch (error) {
        toast.error('Failed to archive tasks');
        console.error('Bulk archive error:', error);
      }
    }

    clearSelection();
    setBulkActionDialog({ open: false, action: null, count: 0 });
  };

  return (
    <div className="space-y-4">
      <RoadmapFilters
        tasks={tasks}
        filters={filters}
        onFiltersChange={setFilters}
        selectedTasks={selectedTasks}
        onBulkAction={handleBulkAction}
        onClearSelection={clearSelection}
      />

      <div className="h-[calc(100vh-300px)] sm:h-[calc(100vh-280px)]">
        <KanbanProvider
          columns={KANBAN_COLUMNS}
          data={filteredTasks}
          onDragEnd={handleDragEnd}
          className="h-full grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"
        >
          {(column) => (
            <KanbanBoard
              key={column.id}
              id={column.id}
              className="h-full min-h-[300px] sm:min-h-[400px]"
            >
              <KanbanHeader className="flex items-center justify-between p-3 sm:p-2">
                <span className="font-semibold text-base sm:text-sm">
                  {column.name}
                </span>
                <span className="rounded-full bg-muted px-2 py-1 text-sm sm:text-xs">
                  {
                    filteredTasks.filter((task) => task.column === column.id)
                      .length
                  }
                </span>
              </KanbanHeader>
              <KanbanCards id={column.id}>
                {(task) => (
                  <RoadmapTaskCard
                    key={task.id}
                    task={task as RoadmapTask}
                    onUpdate={handleTaskUpdate}
                    onDelete={handleTaskDelete}
                    disabled={isReordering}
                    selected={selectedTasks.includes(task.id)}
                    onSelect={(selected) => handleTaskSelect(task.id, selected)}
                  />
                )}
              </KanbanCards>
            </KanbanBoard>
          )}
        </KanbanProvider>
      </div>

      <AlertDialog
        open={bulkActionDialog.open}
        onOpenChange={(open) =>
          setBulkActionDialog({ ...bulkActionDialog, open })
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {bulkActionDialog.action === 'delete'
                ? 'Delete Tasks'
                : 'Archive Tasks'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to {bulkActionDialog.action}{' '}
              {bulkActionDialog.count} task
              {bulkActionDialog.count === 1 ? '' : 's'}?
              {bulkActionDialog.action === 'delete' &&
                ' This action cannot be undone.'}
              {bulkActionDialog.action === 'archive' &&
                ' This will move the tasks to the Completed column.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkAction}
              className={
                bulkActionDialog.action === 'delete'
                  ? 'bg-destructive hover:bg-destructive/90'
                  : ''
              }
            >
              {bulkActionDialog.action === 'delete' ? 'Delete' : 'Archive'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
