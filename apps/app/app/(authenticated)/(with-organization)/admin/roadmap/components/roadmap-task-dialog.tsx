'use client';

import { DeleteConfirmationDialog } from '@/app/(authenticated)/(with-organization)/components/delete-confirmation-dialog';
import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@repo/design-system/components/ui/drawer';
import { useForm } from '@repo/design-system/components/ui/form';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { Plus } from 'lucide-react';
import * as React from 'react';
import { z } from 'zod';
import {
  createRoadmapTask,
  deleteRoadmapTask,
  updateRoadmapTask,
} from '../actions';
import type { RoadmapTask } from './roadmap-kanban';

const taskFormSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  description: z
    .string()
    .max(2000, 'Description must be less than 2000 characters')
    .optional(),
  status: z.enum(['planned', 'in-progress', 'in-review', 'completed'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
  priority: z.enum(['high', 'medium', 'low'], {
    errorMap: () => ({ message: 'Please select a valid priority level' }),
  }),
  category: z.enum(['feature', 'bug-fix', 'enhancement', 'infrastructure'], {
    errorMap: () => ({ message: 'Please select a valid category' }),
  }),
  estimatedTimeline: z
    .string()
    .max(100, 'Timeline must be less than 100 characters')
    .optional(),
});

type TaskFormData = z.infer<typeof taskFormSchema>;

interface RoadmapTaskDialogProps {
  mode?: 'create' | 'edit';
  task?: RoadmapTask;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSave?: (task: RoadmapTask) => void;
  onDelete?: (taskId: string) => void;
}

const STATUS_OPTIONS = [
  { value: 'planned', label: 'Planned' },
  { value: 'in-progress', label: 'In Progress' },
  { value: 'in-review', label: 'In Review' },
  { value: 'completed', label: 'Completed' },
];

const PRIORITY_OPTIONS = [
  { value: 'high', label: 'High' },
  { value: 'medium', label: 'Medium' },
  { value: 'low', label: 'Low' },
];

const CATEGORY_OPTIONS = [
  { value: 'feature', label: 'Feature' },
  { value: 'bug-fix', label: 'Bug Fix' },
  { value: 'enhancement', label: 'Enhancement' },
  { value: 'infrastructure', label: 'Infrastructure' },
];

export function RoadmapTaskDialog({
  mode = 'create',
  task,
  open,
  onOpenChange,
  onSave,
  onDelete,
}: RoadmapTaskDialogProps) {
  const [internalOpen, setInternalOpen] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const isDesktop = useIsDesktop();

  const isOpen = open !== undefined ? open : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: task?.title || '',
      description: task?.description || '',
      status: task?.status || 'planned',
      priority: task?.priority || 'medium',
      category: task?.category || 'feature',
      estimatedTimeline: task?.estimatedTimeline || '',
    },
  });

  // Reset form when task changes or dialog opens
  React.useEffect(() => {
    if (isOpen) {
      form.reset({
        title: task?.title || '',
        description: task?.description || '',
        status: task?.status || 'planned',
        priority: task?.priority || 'medium',
        category: task?.category || 'feature',
        estimatedTimeline: task?.estimatedTimeline || '',
      });
    }
  }, [task, isOpen, form]);

  const onSubmit = async (data: TaskFormData) => {
    setIsSubmitting(true);
    try {
      if (mode === 'edit' && task) {
        await updateRoadmapTask(task.id, data);
        const updatedTask: RoadmapTask = {
          ...task,
          ...data,
          column: data.status,
          name: data.title,
        };
        onSave?.(updatedTask);
        toast.success('Task updated successfully');
      } else {
        const result = await createRoadmapTask(data);
        const newTask = {
          ...result.data,
          ...data,
          column: data.status,
          name: data.title,
          description: result.data.description || undefined,
          estimatedTimeline: result.data.estimatedTimeline || undefined,
          createdAt: result.data.createdAt.toISOString(),
          updatedAt: result.data.updatedAt.toISOString(),
        };
        onSave?.(newTask as RoadmapTask);
        toast.success('Task created successfully');
      }
      setOpen(false);
      form.reset();
    } catch (error) {
      toast.error(
        mode === 'edit'
          ? 'Failed to update task. Please try again.'
          : 'Failed to create task. Please try again.'
      );
      console.error('Error saving task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setOpen(false);
    form.reset();
  };

  const handleDelete = async () => {
    if (!task?.id) {
      return;
    }

    try {
      await deleteRoadmapTask(task.id);
      onDelete?.(task.id);
      setOpen(false);
      toast.success('Task deleted successfully');
    } catch (error) {
      toast.error('Failed to delete task. Please try again.');
      console.error('Error deleting task:', error);
    }
  };

  const DialogForm = () => (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Enter task title" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter task description (optional)"
                  className="min-h-[100px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Priority</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {PRIORITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {CATEGORY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="estimatedTimeline"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Timeline</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Q1 2024" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-between pt-4">
          {mode === 'edit' && task && (
            <DeleteConfirmationDialog
              title="Delete Task"
              description={`Are you sure you want to delete "${task.title}"? This action cannot be undone.`}
              deleteButtonText="Delete Task"
              deletingText="Deleting..."
              onDelete={handleDelete}
              variant="destructive"
              size="sm"
            />
          )}
          <div className="ml-auto flex gap-2">
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? mode === 'edit'
                  ? 'Updating...'
                  : 'Creating...'
                : mode === 'edit'
                  ? 'Update Task'
                  : 'Create Task'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );

  if (isDesktop) {
    return (
      <Dialog open={isOpen} onOpenChange={setOpen}>
        {mode === 'create' && (
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Task
            </Button>
          </DialogTrigger>
        )}
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {mode === 'edit' ? 'Edit Task' : 'Create New Task'}
            </DialogTitle>
          </DialogHeader>
          <DialogForm />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={isOpen} onOpenChange={setOpen}>
      {mode === 'create' && (
        <DrawerTrigger asChild>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Task
          </Button>
        </DrawerTrigger>
      )}
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DrawerTitle>
            {mode === 'edit' ? 'Edit Task' : 'Create New Task'}
          </DrawerTitle>
        </DrawerHeader>
        <div className="px-4">
          <DialogForm />
        </div>
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">Cancel</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
