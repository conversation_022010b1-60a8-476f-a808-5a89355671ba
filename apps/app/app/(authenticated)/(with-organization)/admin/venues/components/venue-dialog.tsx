'use client';

import * as React from 'react';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedVenue } from '@/types';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTrigger,
} from '@repo/design-system/components/ui/drawer';
import { title } from 'radash';
import { VenueForm } from '../form';

interface VenueDialogProps {
  mode?: 'create' | 'edit';
  venue?: SerializedVenue;
}

export function VenueDialog({ mode = 'create', venue }: VenueDialogProps) {
  const [open, setOpen] = React.useState(false);
  const isDesktop = useIsDesktop();

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>{title(mode)} Venue</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>{title(mode)} a venue</DialogTitle>
          </DialogHeader>
          <VenueForm {...{ setOpen, mode, venue }} />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button>{title(mode)} Venue</Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DialogTitle>{title(mode)} a venue</DialogTitle>
        </DrawerHeader>
        <VenueForm {...{ setOpen, mode, venue }} />
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">Cancel</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
