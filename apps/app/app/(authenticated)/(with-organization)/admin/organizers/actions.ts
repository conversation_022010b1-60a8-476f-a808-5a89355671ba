'use server';

import { randomUUID } from 'node:crypto';
import { isSuper<PERSON>d<PERSON>, toAuth<PERSON><PERSON><PERSON> } from '@repo/auth/permission-utils';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { MAX_ROWS, type Prisma } from '@repo/database/types';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

// Helper function to get or create user
async function getOrCreateUser(data: Prisma.OrganizerUncheckedCreateInput) {
  type UserData = Partial<
    Prisma.UserGetPayload<{
      select: {
        id: true;
        firstName: true;
        lastName: true;
        email: true;
        phone: true;
      };
    }>
  > | null;

  let user: UserData = null;

  if (data.userId === '-1') {
    // Creating a new user
    if (!data.name || !data.email) {
      throw new Error('Missing customer name/email');
    }

    const [firstName, ...lastNameParts] = data.name.split(' ');
    const lastName = lastNameParts.join(' ');

    // First, try to find an existing user by email
    user = await database.user.findFirst({
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
      },
      where: {
        email: data.email,
      },
    });

    // If no user exists, create one
    if (!user && data.email && data.name) {
      user = await database.user.create({
        data: {
          id: randomUUID(),
          firstName,
          lastName: lastName || firstName,
          name: data.name,
          email: data.email,
          emailVerified: false,
          phone: data.phone,
          dob: new Date('2000-01-01'),
        },
      });
    }

    if (!user) {
      throw new Error('Failed to create user');
    }
  } else {
    // Using existing user
    user = await database.user.findUnique({
      where: { id: data.userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
      },
    });

    if (!user) {
      throw new Error('User does not exist');
    }
  }

  return user;
}

// Get all organizers
export async function getOrganizers() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!isSuperAdmin(toAuthResult(session))) {
    throw new Error('Unauthorized: Only super admins can manage organizers');
  }

  const organizers = await database.organizer.findMany({
    include: {
      user: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: MAX_ROWS,
  });

  return serializePrisma(organizers);
}

// Create a new organizer
export async function createOrganizer(
  values: Prisma.OrganizerUncheckedCreateInput
) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!isSuperAdmin(toAuthResult(session))) {
    throw new Error('Unauthorized: Only super admins can manage organizers');
  }

  // create or get user
  const user = await getOrCreateUser(values);

  // create organizer with user id
  await database.organizer.create({
    data: {
      ...values,
      userId: user.id ?? '',
    },
  });

  revalidatePath('/admin/organizers');

  return {
    success: true,
  };
}

// Update an existing organizer
export async function updateOrganizer(
  id: string,
  values: Prisma.OrganizerUncheckedUpdateInput
) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const isSelf = session?.session.organizerId === id;

  if (!isSuperAdmin(toAuthResult(session)) && !isSelf) {
    throw new Error(
      'Unauthorized: Only super admins or the organizer themselves can modify this data'
    );
  }

  await database.organizer.update({
    where: { id },
    data: values,
  });

  revalidatePath('/admin/organizers');
  revalidatePath(`/admin/organizers/${id}`);

  return {
    success: true,
  };
}

// Delete an organizer
export async function deleteOrganizer(id: string) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!isSuperAdmin(toAuthResult(session))) {
    throw new Error('Unauthorized: Only super admins can manage organizers');
  }

  // Check if any events are associated with this organizer
  const eventsCount = await database.event.count({
    where: {
      organizerId: id,
    },
  });

  if (eventsCount > 0) {
    throw new Error(
      `Cannot delete organizer: ${eventsCount} events are associated with this organizer`
    );
  }

  await database.organizer.delete({
    where: { id },
  });

  revalidatePath('/admin/organizers');

  return {
    success: true,
  };
}

// Get a specific organizer by ID
export async function getOrganizer(id: string) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const isSelf = session?.session?.organizerId === id;

  if (!isSuperAdmin(toAuthResult(session)) && !isSelf) {
    return null;
  }

  const organizer = await database.organizer.findUnique({
    where: { id },
    include: {
      user: true,
      premiumUpgrades: true,
    },
  });

  if (!organizer) {
    return null;
  }

  return serializePrisma(organizer);
}
