import { auth } from '@repo/auth/server';
import {
  ArrowLeft,
  Bell,
  ClipboardCheck,
  Clock,
  Phone,
  StarIcon,
  User,
} from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDate } from '@repo/design-system/lib/format';
import { headers } from 'next/headers';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { title } from 'radash';
import { Header } from '../../../components/header';
import { getOrganizer } from '../actions';
import { OrganizerDialog } from '../components/organizer-dialog';
import { OrganizerNotification } from '../components/organizer-notification';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

// Helper function to check if user is a super admin
async function checkSuperAdminAccess() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  // Check if user is a super admin
  if (!isSuperAdmin(toAuthResult(session))) {
    redirect('/');
  }

  return session;
}

// Helper function to fetch and validate organizer
async function fetchOrganizer(organizerId: string) {
  const organizer = await getOrganizer(organizerId);

  if (!organizer) {
    notFound();
  }

  return organizer;
}

// Import the SerializedOrganizer type
import type { SerializedOrganizer } from '@/types';
import { isSuperAdmin, toAuthResult } from '@repo/auth/permission-utils';

// Use SerializedOrganizer as our Organizer type
type Organizer = SerializedOrganizer;

// Component for the organizer header section
function OrganizerHeader({
  organizer,
  isPremium,
}: { organizer: Organizer; isPremium: boolean }) {
  return (
    <div className="relative flex flex-col space-y-2 md:flex-row md:items-center md:space-y-0">
      <h2 className="flex-1 font-bold text-3xl tracking-tight">
        {title(organizer.name)}
      </h2>
      <div className="flex items-center gap-2">
        <Button variant="outline" asChild>
          <Link href="/admin/organizers">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Link>
        </Button>
        <OrganizerDialog mode="edit" organizer={organizer} />
      </div>

      {isPremium && (
        <div className="-top-6 absolute left-0">
          <Badge variant="premium">
            <StarIcon className="h-3 w-3" />
            Premium
          </Badge>
        </div>
      )}
    </div>
  );
}

// Component for basic information card
function BasicInfoCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Basic Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-2">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Name</dt>
            <dd>{organizer.name}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Slug</dt>
            <dd>{organizer.slug}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Description
            </dt>
            <dd>{organizer.description || 'No description'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Status
            </dt>
            <dd>
              <Badge
                variant={
                  organizer.verificationStatus.toLowerCase() as
                    | 'pending'
                    | 'verified'
                    | 'rejected'
                    | 'outline'
                }
              >
                {title(organizer.verificationStatus)}
              </Badge>
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Component for contact information card
function ContactInfoCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Phone className="h-5 w-5" />
          Contact Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-2">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Email</dt>
            <dd>{organizer.email || 'Not provided'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">Phone</dt>
            <dd>{organizer.phone || 'Not provided'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Website
            </dt>
            <dd>{organizer.website || 'Not provided'}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Address
            </dt>
            <dd>{organizer.address || 'Not provided'}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Component for verification and payment card
function VerificationPaymentCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <ClipboardCheck className="h-5 w-5" />
          Verification & Payment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-2">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Payout Frequency
            </dt>
            <dd>
              {title(
                organizer.payoutFrequency
                  ? organizer.payoutFrequency
                  : 'Not set'
              )}
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Commission Rate
            </dt>
            <dd>
              {organizer.commissionRate
                ? `${organizer.commissionRate}%`
                : 'Not set'}
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Component for notification preferences card
function NotificationPreferencesCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Preferences
        </CardTitle>
        <OrganizerNotification organizer={organizer} />
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Email Notifications
            </dt>
            <dd>
              <Badge
                variant={organizer.emailNotifications ? 'success' : 'outline'}
              >
                {organizer.emailNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              SMS Notifications
            </dt>
            <dd>
              <Badge
                variant={organizer.smsNotifications ? 'success' : 'outline'}
              >
                {organizer.smsNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Push Notifications
            </dt>
            <dd>
              <Badge
                variant={organizer.pushNotifications ? 'success' : 'outline'}
              >
                {organizer.pushNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Component for metadata card
function MetadataCard({ organizer }: { organizer: Organizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Metadata
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Created
            </dt>
            <dd>{formatDate(new Date(organizer.createdAt))}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Last Updated
            </dt>
            <dd>{formatDate(new Date(organizer.updatedAt))}</dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              User ID
            </dt>
            <dd>{organizer.userId || 'Not linked'}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}

// Main component for rendering the organizer detail page
function OrganizerDetailContent({ organizer }: { organizer: Organizer }) {
  const isPremium = organizer.premiumUpgrades.length > 0;

  return (
    <>
      <Header pages={['Organizers']} page={organizer.name} />

      <main className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <OrganizerHeader organizer={organizer} isPremium={isPremium} />

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <BasicInfoCard organizer={organizer} />
          <ContactInfoCard organizer={organizer} />
          <VerificationPaymentCard organizer={organizer} />
        </div>

        <NotificationPreferencesCard organizer={organizer} />
        <MetadataCard organizer={organizer} />
      </main>
    </>
  );
}

// Main page component with reduced complexity
export default async function OrganizerDetailPage({ params }: PageProps) {
  // Check access and fetch data
  await checkSuperAdminAccess();

  try {
    const organizer = await fetchOrganizer((await params).id);
    return <OrganizerDetailContent organizer={organizer} />;
  } catch (error) {
    console.error(error);
    return notFound();
  }
}
