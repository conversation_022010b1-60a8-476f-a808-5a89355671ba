'use client';

import type { SerializedOrganizer } from '@/types';
import { generateSlugPrefix, slugPattern } from '@repo/design-system/lib/utils';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  PayoutFrequency,
  type Prisma,
  VerificationStatus,
} from '@repo/database/types';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { slugify } from '@repo/design-system/lib/utils';
import { useMemo, useState } from 'react';
import { z } from 'zod';
import { UserAutocomplete } from '../../components/user-autocomplete';
import { createOrganizer, updateOrganizer } from './actions';

interface OrganizerFormProps {
  setOpen?: (open: boolean) => void;
  mode?: 'create' | 'edit';
  isSelf?: boolean;
  organizer?: SerializedOrganizer;
}

// Define the form schema with validation
const organizerFormSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  slug: z
    .string({ required_error: 'Slug is required' })
    .min(3, { message: 'Slug must be at least 3 characters' })
    .refine(
      (value) => {
        // Process the value through slugify and check if it's valid
        const processed = slugify(value);
        return processed.length >= 3 && slugPattern.test(processed);
      },
      {
        message:
          'Slug can only contain letters, numbers, hyphens, and Chinese characters',
      }
    )
    .transform((value) => {
      // Transform the value using slugify before submitting
      return slugify(value);
    }),
  description: z.string().optional().nullable(),
  website: z.string().optional().nullable(),
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().optional().nullable(),
  whatsapp: z.string().optional().nullable(),
  facebook: z.string().optional().nullable(),
  twitter: z.string().optional().nullable(),
  instagram: z.string().optional().nullable(),
  youtube: z.string().optional().nullable(),
  tiktok: z.string().optional().nullable(),
  rednote: z.string().optional().nullable(),
  picName: z.string().optional().nullable(),
  picTitle: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  verificationStatus: z
    .nativeEnum(VerificationStatus, {
      required_error: 'Verification status is required',
    })
    .default('pending'),
  payoutFrequency: z
    .nativeEnum(PayoutFrequency, {
      required_error: 'Payout frequency is required',
    })
    .default('monthly'),
  commissionRate: z.coerce
    .number({
      required_error: 'Commission rate is required',
      invalid_type_error: 'Commission rate must be a number',
    })
    .min(0)
    .max(100)
    .default(0),
  emailNotifications: z.boolean().default(true),
  smsNotifications: z.boolean().default(false),
  pushNotifications: z.boolean().default(false),
  userId: z.string().min(1, { message: 'User is required' }),
});

type OrganizerFormValues = z.infer<typeof organizerFormSchema>;

export function OrganizerForm({
  setOpen,
  mode = 'create',
  isSelf = false,
  organizer,
}: OrganizerFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate prefix once and memoize it
  const prefix = useMemo(() => {
    // If in edit mode and organizer has a slug, extract the prefix
    if (mode === 'edit' && organizer?.slug) {
      const parts = organizer.slug.split('-');
      if (parts.length > 0) {
        return parts[0];
      }
    }
    // Otherwise generate a new prefix
    return generateSlugPrefix('organizer');
  }, [mode, organizer?.slug]);

  const form = useForm<OrganizerFormValues>({
    resolver: zodResolver(organizerFormSchema),
    defaultValues: organizer
      ? {
          name: organizer.name,
          slug: organizer.slug
            ? organizer.slug.split('-').slice(1).join('-')
            : '',
          description: organizer.description || '',
          website: organizer.website || '',
          email: organizer.email,
          phone: organizer.phone || '',
          whatsapp: organizer.whatsapp || '',
          facebook: organizer.facebook || '',
          twitter: organizer.twitter || '',
          instagram: organizer.instagram || '',
          youtube: organizer.youtube || '',
          tiktok: organizer.tiktok || '',
          rednote: organizer.rednote || '',
          picName: organizer.picName || '',
          picTitle: organizer.picTitle || '',
          address: organizer.address || '',
          verificationStatus: organizer.verificationStatus,
          payoutFrequency: organizer.payoutFrequency,
          commissionRate: organizer.commissionRate,
          emailNotifications: organizer.emailNotifications,
          smsNotifications: organizer.smsNotifications,
          pushNotifications: organizer.pushNotifications,
          userId: organizer.userId,
        }
      : {
          // TODO: missing fields
          verificationStatus: VerificationStatus.pending,
          payoutFrequency: PayoutFrequency.monthly,
          commissionRate: 10,
          emailNotifications: true,
          smsNotifications: false,
          pushNotifications: false,
        },
  });

  async function onSubmit(values: OrganizerFormValues) {
    setIsSubmitting(true);

    try {
      // Convert form values to Prisma input
      const prismaValues =
        values as unknown as Prisma.OrganizerUncheckedCreateInput;

      // Combine slug before submit
      prismaValues.slug = `${prefix}-${values.slug}`;

      if (mode === 'edit' && organizer?.id) {
        await updateOrganizer(organizer.id, prismaValues);
        toast.success('Organizer updated successfully');
      } else {
        await createOrganizer(prismaValues);
        toast.success('Organizer created successfully');
      }

      setOpen?.(false);
      router.refresh();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-3xl space-y-4 px-4 md:px-0"
      >
        {mode === 'create' && (
          <FormField
            control={form.control}
            name="userId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>User</FormLabel>
                <FormControl>
                  <UserAutocomplete
                    value={field.value ?? ''}
                    onChange={field.onChange}
                  />
                </FormControl>
                <FormDescription>
                  User associated with this organizer
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Organizer Name" {...field} />
              </FormControl>
              <FormDescription>The name of the organizer</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Slug (Customize the final part of your URL.)
              </FormLabel>
              <FormControl>
                <div className="flex items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Input
                        value={prefix}
                        className="flex-1 cursor-not-allowed opacity-60"
                        autoFocus={false}
                        readOnly
                      />
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>This portion of the slug cannot be modified</p>
                    </TooltipContent>
                  </Tooltip>
                  -
                  <Input
                    placeholder="Slug"
                    type=""
                    className="flex-4"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormDescription className="mt-1 text-muted-foreground text-xs">
                <span className="font-medium">Preview: </span>
                <span className="font-mono">
                  {process.env.NEXT_PUBLIC_WEB_URL ||
                    '[https://ticketcare.app](https://ticketcare.app)'}
                  /organizers/{prefix}-
                  {field.value
                    ? slugify(field.value ?? '')
                    : 'my-organizer-name'}
                </span>
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Organizer Description"
                  className="resize-none"
                  maxLength={250}
                  {...field}
                  // Convert null to empty string
                  value={field.value ?? ''}
                />
              </FormControl>
              <FormDescription>
                Brief description of the organizer (maximum 250 characters)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                  />
                </FormControl>
                <FormDescription>Contact email address</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input
                    placeholder="+****************"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Contact phone number</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="whatsapp"
            render={({ field }) => (
              <FormItem>
                <FormLabel>WhatsApp</FormLabel>
                <FormControl>
                  <Input
                    placeholder="+****************"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>WhatsApp contact number</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://example.com"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Organizer's website URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Input
                    placeholder="123 Main St, City, State"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Physical address</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="picName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Person in-charge Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="John Doe"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Name of the person in-charge</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="picTitle"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Person in-charge Title</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Event Manager"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>
                  Title/Position of the person in-charge
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Social Media Section */}
        <div className="mt-6 mb-4">
          <h3 className="font-medium text-lg">Social Media</h3>
          <p className="text-muted-foreground text-sm">
            Add your social media profiles
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="facebook"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Facebook</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://facebook.com/yourpage"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Facebook page URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="instagram"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Instagram</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://instagram.com/youraccount"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Instagram profile URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="twitter"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Twitter</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://twitter.com/youraccount"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>Twitter profile URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="youtube"
            render={({ field }) => (
              <FormItem>
                <FormLabel>YouTube</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://youtube.com/c/yourchannel"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>YouTube channel URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="tiktok"
            render={({ field }) => (
              <FormItem>
                <FormLabel>TikTok</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://tiktok.com/@youraccount"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>TikTok profile URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="rednote"
            render={({ field }) => (
              <FormItem>
                <FormLabel>RedNote</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://rednote.com/youraccount"
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>RedNote profile URL</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <FormField
            control={form.control}
            name="verificationStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Verification Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select verification status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="VERIFIED">Verified</SelectItem>
                    <SelectItem value="REJECTED">Rejected</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>Current verification status</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="payoutFrequency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payout Frequency</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select payout frequency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="DAILY">Daily</SelectItem>
                    <SelectItem value="WEEKLY">Weekly</SelectItem>
                    <SelectItem value="BIWEEKLY">Biweekly</SelectItem>
                    <SelectItem value="MONTHLY">Monthly</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  How often payouts are processed
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="commissionRate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Commission Rate (%)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min={0}
                  max={100}
                  step="0.01"
                  {...field}
                  onChange={(e) =>
                    field.onChange(Number.parseFloat(e.target.value) || 0)
                  }
                  value={field.value}
                />
              </FormControl>
              <FormDescription>Platform commission percentage</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        /> */}

        <Button
          disabled={form.formState.isSubmitting || isSubmitting}
          type="submit"
          className="max-md:w-full"
        >
          {isSelf
            ? 'Edit Profile'
            : mode === 'create'
              ? 'Create Organizer'
              : 'Update Organizer'}
        </Button>
      </form>
    </Form>
  );
}
