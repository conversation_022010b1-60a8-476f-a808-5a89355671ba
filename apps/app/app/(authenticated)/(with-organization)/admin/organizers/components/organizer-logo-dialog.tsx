'use client';

import Image from 'next/image';

import { Upload } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { type ChangeEvent, useState } from 'react';
import { updateOrganizer } from '../actions';

interface LogoFormValues {
  logo?: string;
}

interface OrganizerLogoProps {
  organizer: {
    id: string;
    logo?: string | null;
  };
}

export function OrganizerLogoDialog({ organizer }: OrganizerLogoProps) {
  const [open, setOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    organizer?.logo || null
  );

  const form = useForm<LogoFormValues>({
    defaultValues: {
      logo: organizer.logo || undefined,
    },
  });

  // Function to handle image upload
  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('access', 'public');

      // Create a new XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/upload', true);

      // Set the appropriate headers
      xhr.setRequestHeader('Accept', 'application/json');

      // Track upload progress
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(percentComplete);
        }
      };

      // Handle response
      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
              setPreviewUrl(response.url);
              form.setValue('logo', response.url);
              toast.success('Logo uploaded successfully');
            } else {
              toast.error(response.error || 'Failed to upload logo');
            }
          } catch (_) {
            toast.error('Failed to process server response');
          }
        } else {
          toast.error(
            `Failed to upload logo: ${xhr.statusText || 'Server error'}`
          );
        }
        setIsUploading(false);
      };

      // Handle errors
      xhr.onerror = () => {
        toast.error('Network error during upload.');
        setIsUploading(false);
      };

      xhr.send(formData);
    } catch (_) {
      toast.error('Failed to upload logo');
      setIsUploading(false);
    }
  };

  // Function to handle image URL input
  const handleImageUrlChange = (url: string) => {
    if (url) {
      setPreviewUrl(url);
      form.setValue('logo', url);
    }
  };

  async function onSubmit(values: LogoFormValues) {
    try {
      setIsSaving(true);
      if (!organizer?.id) {
        throw new Error('Organizer ID is required');
      }

      await updateOrganizer(organizer.id, {
        ...values,
      });

      toast.success('Organizer logo successfully updated');
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const FileUploadTab = () => (
    <div className="flex flex-col gap-4">
      {previewUrl && (
        <div className="relative aspect-square w-[40%] self-center overflow-hidden rounded-md">
          <Image
            src={previewUrl}
            alt="Organizer logo preview"
            fill
            className="object-cover"
          />
        </div>
      )}

      <FormField
        control={form.control}
        name="logo"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center gap-4">
              <Label
                htmlFor="image-upload"
                className={`flex h-10 cursor-pointer items-center gap-2 rounded-md border border-input bg-background px-4 py-2 font-medium text-sm ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${
                  isUploading ? 'opacity-50' : ''
                }`}
              >
                <Upload className="h-4 w-4" />
                {isUploading
                  ? `Uploading... ${uploadProgress}%`
                  : 'Pick a file'}
              </Label>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
                disabled={isUploading}
              />
              <input type="hidden" {...field} />
            </div>
            <FormDescription>
              Upload a square logo image for your organization.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );

  const UrlInputTab = () => (
    <div className="flex flex-col gap-4">
      {previewUrl && (
        <div className="relative aspect-square w-[40%] self-center overflow-hidden rounded-md">
          <Image
            src={previewUrl}
            alt="Organizer logo preview"
            fill
            className="object-cover"
          />
        </div>
      )}

      <FormField
        control={form.control}
        name="logo"
        render={({ field }) => (
          <FormItem>
            <Label htmlFor="image-url">Logo URL</Label>
            <div className="flex flex-col gap-2">
              <Input
                id="image-url"
                placeholder="https://example.com/logo.jpg"
                {...field}
                disabled={isUploading}
              />
              <Button
                type="button"
                variant="secondary"
                onClick={() => handleImageUrlChange(field.value || '')}
                disabled={!field.value || isUploading}
                className="mt-2 w-full sm:w-auto"
              >
                Preview Image
              </Button>
            </div>
            <FormDescription>
              Enter a URL to an existing logo image.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" type="button">
          {organizer.logo ? 'Change Logo' : 'Add Logo'}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Organizer Logo</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <Tabs defaultValue="upload" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="upload">Upload</TabsTrigger>
                <TabsTrigger value="url">URL</TabsTrigger>
              </TabsList>
              <TabsContent value="upload" className="mt-4">
                <FileUploadTab />
              </TabsContent>
              <TabsContent value="url" className="mt-4">
                <UrlInputTab />
              </TabsContent>
            </Tabs>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Logo'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
