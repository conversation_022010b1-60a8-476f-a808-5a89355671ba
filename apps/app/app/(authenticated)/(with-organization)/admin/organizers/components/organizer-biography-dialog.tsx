'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { SerializedOrganizer } from '@/types';
import { Editor } from '@repo/design-system/components/blocks/editor-00/editor';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { FileText, Pencil } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { updateOrganizer } from '../actions';

interface BiographyFormValues {
  biography?: string;
}

interface OrganizerBiographyDialogProps {
  organizer: SerializedOrganizer;
}

export function OrganizerBiographyDialog({
  organizer,
}: OrganizerBiographyDialogProps) {
  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<BiographyFormValues>({
    defaultValues: {
      biography: organizer?.biography
        ? JSON.stringify(organizer?.biography)
        : undefined,
    },
  });

  async function onSubmit(values: BiographyFormValues) {
    try {
      setIsSaving(true);
      if (!organizer?.id) {
        throw new Error('Organizer ID is required');
      }

      await updateOrganizer(organizer.id, {
        ...values,
      });

      toast.success('Organizer biography updated successfully');
      setOpen(false);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  }

  const BiographyForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-5xl space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="biography"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Biography</FormLabel>
              <Editor
                editorSerializedState={parseEditorContent(field.value)}
                onSerializedChange={(value) =>
                  field.onChange(JSON.stringify(value))
                }
              />
              <FormDescription>
                Provide a detailed biography of the organizer. You can add
                formatting, links, and images.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="max-md:w-full" disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save'}
        </Button>
      </form>
    </Form>
  );

  const DialogTriggerContent = () => {
    return (
      <div className="rounded-lg border p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Organizer Biography</h3>
          </div>
          {organizer && (
            <Button size="sm" variant="outline" onClick={() => setOpen(true)}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit Biography
            </Button>
          )}
        </div>
        <div className="mt-2 text-muted-foreground">
          <ReadOnlyEditor
            editorSerializedState={parseEditorContent(
              (typeof organizer?.biography === 'string'
                ? organizer?.biography
                : JSON.stringify(organizer?.biography)) ?? 'N/A'
            )}
          />
        </div>
      </div>
    );
  };

  if (isDesktop) {
    return (
      <>
        <DialogTriggerContent />
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="sm:max-w-2xl lg:max-w-5xl">
            <DialogHeader>
              <DialogTitle>Edit Organizer Biography</DialogTitle>
            </DialogHeader>
            <BiographyForm />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <DialogTriggerContent />
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent>
          <DrawerHeader className="text-left">
            <DialogTitle>Edit Organizer Biography</DialogTitle>
          </DrawerHeader>
          <BiographyForm />
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}
