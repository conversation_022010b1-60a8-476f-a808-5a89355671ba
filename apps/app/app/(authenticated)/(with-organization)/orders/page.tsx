import { auth } from '@repo/auth/server';
import {} from '@repo/design-system/components/ui/breadcrumb';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { Header } from '../components/header';
import { OrderDialog } from './components/order-dialog';
import { OrderTable } from './components/order-table';

const title = 'TicketCARE - Orders';
const description = 'TicketCARE - Orders';

export const metadata: Metadata = {
  title,
  description,
};

const OrdersPage = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  return (
    <>
      <Header page="Orders" />

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div>
          <div className="flex items-center justify-end">
            <OrderDialog
              mode={
                session?.session.organizerId != null ? 'create' : 'disabled'
              }
            />
          </div>
        </div>
        <div className="min-h-[100vh] flex-1 rounded-xl md:min-h-min">
          <OrderTable />
        </div>
      </div>
    </>
  );
};

export default OrdersPage;
