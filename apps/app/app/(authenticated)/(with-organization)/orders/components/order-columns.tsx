'use client';

import type { SerializedOrder } from '@/types';
import type { OrderStatus } from '@repo/database/types';
import { ArrowUpDown } from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import {
  formatDateTime,
  formatDateTimeTable,
} from '@repo/design-system/lib/format';
import type { ColumnDef } from '@tanstack/react-table';
import Decimal from 'decimal.js';
import { title } from 'radash';

export const columns: ColumnDef<SerializedOrder>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Order ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const id = (getValue() as string) ?? '';
      const short =
        id && id.length > 12 ? `${id.slice(0, 4)}…${id.slice(-4)}` : id;

      return (
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="font-mono cursor-help">{short}</span>
          </TooltipTrigger>
          <TooltipContent>{id}</TooltipContent>
        </Tooltip>
      );
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const status = getValue() as OrderStatus;

      return <Badge variant={status}>{title(status)}</Badge>;
    },
  },
  {
    accessorKey: 'user',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const user = row.original.user;

      return (
        <div>
          <p>{user.name || `${user.firstName} ${user.lastName}`}</p>
          <p>{user.email}</p>
        </div>
      );
    },
  },
  {
    accessorKey: 'orderedAt',
    header: ({ column }) => {
      return (
        <Button
          variant="transparent"
          size="table"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ getValue }) => {
      const date = getValue() as Date;

      return (
        <span className="whitespace-pre-wrap">
          {formatDateTimeTable(date ?? '')}
        </span>
      );
    },
  },
  {
    accessorKey: 'event',
    header: 'Event',
    cell: (props) => {
      const event = props.row.original.event;

      return (
        <div className="flex flex-col items-start">
          <div className="font-semibold">{event?.title}</div>
          <div className="text-secondary-foreground text-xs">
            {event?.venueName}
          </div>
          {event?.startTime && (
            <div className="text-secondary-foreground text-xs">
              {formatDateTime(event?.startTime ?? '')}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'tickets',
    header: 'Tickets',
    cell: (props) => {
      const tickets = props.row.original.tickets || [];

      // Group tickets by time slot
      const timeSlots = tickets.reduce(
        (
          groups: {
            [key: string]: {
              id: string;
              time: string | null;
              tickets: typeof tickets;
            };
          },
          ticket: (typeof tickets)[number]
        ) => {
          const timeSlotId = ticket.timeSlot?.id || 'no-timeslot';

          // Create the time slot group if it doesn't exist
          if (!groups[timeSlotId]) {
            groups[timeSlotId] = {
              id: timeSlotId,
              time: ticket.timeSlot?.startTime || null,
              tickets: [],
            };
          }

          // Add the ticket to the appropriate time slot group
          groups[timeSlotId].tickets.push(ticket);
          return groups;
        },
        {}
      );

      // Convert to array and sort by time if available
      const timeSlotsArray = Object.values(timeSlots);

      return (
        <div className="flex flex-col items-start gap-1">
          {timeSlotsArray.map((timeSlot) => {
            // Count tickets by type within this time slot
            const ticketCounts = timeSlot.tickets.reduce(
              (
                counts: {
                  [key: string]: { id: string; name: string; count: number };
                },
                ticket
              ) => {
                const typeId = ticket.ticketType?.id || 'unknown';
                const typeName = ticket.ticketType?.name || 'Unknown';

                if (!counts[typeId]) {
                  counts[typeId] = { id: typeId, name: typeName, count: 0 };
                }

                counts[typeId].count += 1;
                return counts;
              },
              {}
            );

            return (
              <div key={timeSlot.id}>
                {timeSlot.time && (
                  <div className="text-secondary-foreground text-xs">
                    {formatDateTime(timeSlot.time)}
                  </div>
                )}
                <div className="border-gray border-l-2 pl-2 mt-1">
                  {Object.values(ticketCounts).map((ticketType) => (
                    <div className="font-semibold text-sm" key={ticketType.id}>
                      {ticketType.name}
                      {ticketType.count > 1 && (
                        <span className="ml-1 text-muted-foreground">
                          ({ticketType.count})
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      );
    },
  },
  {
    accessorKey: 'totalAmount',
    header: ({ column }) => {
      return (
        <div className="text-right">
          <Button
            variant="transparent"
            size="table"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Value
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        </div>
      );
    },
    cell: ({ getValue }) => {
      const value = getValue() as number;
      return <p className="text-right">{new Decimal(value).toFixed(2)}</p>;
    },
  },
];
