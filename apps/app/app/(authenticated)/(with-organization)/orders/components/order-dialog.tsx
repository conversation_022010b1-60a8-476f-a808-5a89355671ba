'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { Prisma } from '@repo/database/types';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DrawerTrigger,
} from '@repo/design-system/components/ui/drawer';
import * as React from 'react';
import { OrderForm } from '../form';

interface OrderDialogProps {
  mode?: 'create' | 'edit' | 'disabled';
  order?: Prisma.OrderGetPayload<{
    include: { user: true; tickets: true };
  }>;
}

function title(mode: 'create' | 'edit' | 'disabled') {
  return mode === 'create' ? 'Create' : 'Edit';
}

export function OrderDialog({ mode = 'create', order }: OrderDialogProps) {
  const [open, setOpen] = React.useState(false);
  const isDesktop = useIsDesktop();

  if (mode === 'disabled') {
    return <Button disabled>Create Order</Button>;
  }

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>{title(mode)} Order</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>{title(mode)} an order</DialogTitle>
          </DialogHeader>
          <OrderForm {...{ setOpen, mode, order }} />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Button>{title(mode)} Order</Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DialogTitle>{title(mode)} an order</DialogTitle>
        </DrawerHeader>
        <OrderForm {...{ setOpen, mode, order }} />
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">Cancel</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
