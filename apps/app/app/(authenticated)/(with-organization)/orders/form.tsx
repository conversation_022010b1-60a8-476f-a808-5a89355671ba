'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { PaymentStatus } from '@prisma/client';
import type { Prisma } from '@repo/database/types';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/design-system/components/ui/form';
import { useForm } from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { first } from 'radash';
import { useState } from 'react';
import { mutate } from 'swr';
import { z } from 'zod';
import { UserAutocomplete } from '../components/user-autocomplete';
import { TimeSlotAutocomplete } from '../events/[slug]/components/time-slot/components/time-slot-autocomplete';
import { createOrder } from './actions';
import { EventAutocomplete } from './components/event-autocomplete';
import { TicketTypeAutocomplete } from './components/ticket-type-autocomplete';

interface OrderFormProps {
  setOpen?: (open: boolean) => void;
  mode?: 'create' | 'edit';
  order?: Prisma.OrderGetPayload<{
    include: { user: true; tickets: true };
  }>;
}

// Define the Zod schema for order form validation
const orderFormSchema = z.object({
  userId: z.string().min(1, { message: 'User is required' }),
  // perform manual validation if userId: -1 (create new user)
  customerEmail: z.string().optional(),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),

  // autocomplete fields
  eventId: z.string().min(1, { message: 'Event is required' }),
  ticketTypeId: z.string().min(1, { message: 'Ticket type is required' }),
  timeSlotId: z.string().min(1, { message: 'Time slot is required' }),

  // order fields
  quantity: z.number().min(1, { message: 'Quantity must be at least 1' }),
  paymentMethod: z.string().min(1, { message: 'Payment method is required' }),
  paymentStatus: z.nativeEnum(PaymentStatus, {
    message: 'Payment status is required',
  }),
});

// Type for form values
export type OrderFormValues = z.infer<typeof orderFormSchema>;

export function OrderForm({ setOpen, mode = 'create', order }: OrderFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetKeys, setResetKeys] = useState({
    eventId: 0,
    timeSlotId: 0,
    ticketTypeId: 0,
    userId: 0,
  });

  const firstTicketType = first(order?.tickets ?? []);

  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      userId: order?.userId || '',
      customerEmail: order?.user?.email ?? undefined,
      customerName: order?.user?.firstName ?? undefined,
      customerPhone: order?.user?.phone ?? undefined,
      eventId: order?.eventId || '',
      ticketTypeId: firstTicketType?.ticketTypeId || '',
      timeSlotId: firstTicketType?.timeSlotId || '',
      quantity: 1,
      paymentMethod: order?.paymentMethod || 'custom',
      paymentStatus: order?.paymentStatus || PaymentStatus.custom,
    },
  });

  // Update total amount when quantity or ticket type changes
  // const updateTotalAmount = React.useCallback(
  //   (ticketTypeId: string, quantity: number) => {
  //     const ticketType = ticketTypes.find((tt) => tt.id === ticketTypeId)
  //     if (ticketType) {
  //       form.setValue('totalAmount', ticketType.price * quantity)
  //     }
  //   },
  //   [ticketTypes, form]
  // )

  // Watch for changes in quantity and ticket type
  const eventId = form.watch('eventId');
  const ticketTypeId = form.watch('ticketTypeId');
  const timeSlotId = form.watch('timeSlotId');
  const userId = form.watch('userId');

  // React.useEffect(() => {
  //   if (ticketTypeId && quantity) {
  //     updateTotalAmount(ticketTypeId, quantity)
  //   }
  // }, [ticketTypeId, quantity, updateTotalAmount])

  // Helper function to validate new customer information
  function validateNewCustomer(): boolean {
    if (userId !== '-1') {
      return true;
    }

    const customerEmail = form.getValues('customerEmail');
    const customerName = form.getValues('customerName');
    let hasErrors = false;

    if (!customerEmail || customerEmail.trim() === '') {
      form.setError('customerEmail', {
        message: 'Customer email is required when creating a new customer',
      });
      hasErrors = true;
    }

    if (!customerName || customerName.trim() === '') {
      form.setError('customerName', {
        message: 'Customer name is required when creating a new customer',
      });
      hasErrors = true;
    }

    return !hasErrors;
  }

  // Helper function to handle form submission success
  function handleSubmitSuccess() {
    toast.success(
      `Order ${mode === 'create' ? 'created' : 'updated'} successfully`
    );
    form.reset();
    setOpen?.(false);
  }

  // Helper function to handle form submission error
  function handleSubmitError(error: unknown) {
    console.error('Failed to submit order:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Something went wrong';
    toast.error(errorMessage);
  }

  async function onSubmit(values: OrderFormValues) {
    setIsSubmitting(true);

    // Validate customer information if creating a new customer
    if (!validateNewCustomer()) {
      setIsSubmitting(false);
      return;
    }

    try {
      if (mode === 'create') {
        await createOrder({ ...values });
      } else {
        // await updateOrder(
        //   order.id,
        //   values as PrismaNamespace.OrderUncheckedCreateInput
        // );
      }

      handleSubmitSuccess();
    } catch (error) {
      handleSubmitError(error);
    } finally {
      mutate((key) => typeof key === 'string' && key.startsWith('/api/orders'));
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="userId"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>User</FormLabel>
                <FormControl>
                  <UserAutocomplete
                    key={`user-${resetKeys.userId}`}
                    value={field.value ?? ''}
                    onChange={(value) => {
                      // reset other fields reliant to this field
                      form.setValue('eventId', '');
                      form.setValue('timeSlotId', '');
                      form.setValue('ticketTypeId', '');
                      form.setValue('quantity', 1);

                      // Reset keys to force re-render of dependent components
                      setResetKeys((prev) => ({
                        ...prev,
                        eventId: prev.eventId + 1,
                        timeSlotId: prev.timeSlotId + 1,
                        ticketTypeId: prev.ticketTypeId + 1,
                      }));

                      field.onChange(value);
                    }}
                  />
                </FormControl>
                {userId !== '-1' && (
                  <FormDescription>
                    Select the user for this order
                  </FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          {userId === '-1' && (
            <div className="col-span-2 space-y-4 border-b-2 pb-4">
              <FormLabel>New Customer Information</FormLabel>
              <div className="grid grid-cols-2 gap-4 ">
                <FormField
                  control={form.control}
                  name="customerEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Customer's email address
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="customerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormDescription>Customer's full name</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="customerPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="+1234567890" {...field} />
                      </FormControl>
                      <FormDescription>
                        Customer's contact number
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}

          <FormField
            control={form.control}
            name="eventId"
            render={({ field }) => (
              <FormItem className="col-span-2">
                <FormLabel>Event</FormLabel>
                <FormControl>
                  <EventAutocomplete
                    key={`event-${resetKeys.eventId}`}
                    value={field.value}
                    onChange={(value) => {
                      // reset other fields reliant to this field
                      form.setValue('timeSlotId', '');
                      form.setValue('ticketTypeId', '');
                      form.setValue('quantity', 1);

                      // Reset keys to force re-render of dependent components
                      setResetKeys((prev) => ({
                        ...prev,
                        timeSlotId: prev.timeSlotId + 1,
                        ticketTypeId: prev.ticketTypeId + 1,
                      }));

                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Select the event for this order
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="timeSlotId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Ticket Time Slot</FormLabel>
                <FormControl>
                  <TimeSlotAutocomplete
                    key={`timeslot-${resetKeys.timeSlotId}`}
                    eventId={eventId}
                    value={field.value}
                    onChange={(value) => {
                      // reset other fields reliant to this field
                      form.setValue('ticketTypeId', '');
                      form.setValue('quantity', 1);

                      // Reset keys to force re-render of dependent components
                      setResetKeys((prev) => ({
                        ...prev,
                        ticketTypeId: prev.ticketTypeId + 1,
                      }));

                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormDescription>Select the time slot</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ticketTypeId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Ticket Type</FormLabel>
                <FormControl>
                  <TicketTypeAutocomplete
                    key={`tickettype-${resetKeys.ticketTypeId}`}
                    eventId={eventId}
                    timeSlotId={timeSlotId}
                    value={field.value}
                    onChange={(value) => {
                      // reset other fields reliant to this field
                      form.setValue('quantity', 1);

                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormDescription>Select the ticket type</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    {...field}
                    onChange={(e) =>
                      field.onChange(Number.parseInt(e.target.value, 10))
                    }
                    disabled={!eventId || !ticketTypeId || !timeSlotId}
                  />
                </FormControl>
                <FormDescription>Number of tickets to order</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Method</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="bank_transfer">
                        Bank Transfer
                      </SelectItem>
                      <SelectItem value="duitnow_qr">DuitNow QR</SelectItem>
                      <SelectItem value="ewallet">E-Wallet</SelectItem>
                      <SelectItem value="credit_debit_card">
                        Credit/Debit Cards
                      </SelectItem>
                      <SelectItem value="fpx">FPX</SelectItem>
                      <SelectItem value="cheque">Cheque</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>Method of payment</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="paymentStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Status</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="sponsored">Sponsored</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                      <SelectItem value="disputed">Disputed</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>Current status of the payment</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormItem>
            <FormLabel>Order Status</FormLabel>
            <FormControl>
              <Select defaultValue="reserved" disabled>
                <SelectTrigger>
                  <SelectValue placeholder="Select order status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reserved">Reserved</SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormDescription>
              Orders created manually are marked as 'Reserved'
            </FormDescription>
            <FormMessage />
          </FormItem>
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            className="max-md:w-full"
            disabled={isSubmitting}
          >
            {mode === 'create' ? 'Create' : 'Update'} Order
          </Button>
        </div>
      </form>
    </Form>
  );
}
