import type { SerializedOrder } from '@/types';
import { MailIcon, PhoneIcon } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { title } from 'radash';

interface OrderCustomerProps {
  user: SerializedOrder['user'];
}

export function OrderCustomer({ user }: OrderCustomerProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Name</p>
            <p className="font-medium">{`${user.firstName} ${user.lastName}`}</p>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Account Type</p>
            <p className="font-medium capitalize">{title(user.role)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Email</p>
            <div className="flex items-center gap-2">
              <p className="font-medium">{user.email}</p>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MailIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Phone</p>
            <div className="flex items-center gap-2">
              <p className="font-medium">{user.phone || 'N/A'}</p>
              {user.phone && (
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <PhoneIcon className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
