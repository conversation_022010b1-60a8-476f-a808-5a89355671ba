'use client';

import type { SerializedOrder } from '@/types';
import { Info, Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { useState } from 'react';
import { updateOrderPaymentStatus } from '../action';

interface OrderPaymentStatusProps {
  order: SerializedOrder;
}

export function OrderPaymentStatus({ order }: OrderPaymentStatusProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState<
    string | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);

  const handlePaymentStatusChange = (newStatus: string) => {
    setSelectedPaymentStatus(newStatus);
    setIsOpen(true);
  };

  const handleConfirm = async () => {
    if (selectedPaymentStatus) {
      setIsLoading(true);

      try {
        const result = await updateOrderPaymentStatus(
          order.id,
          selectedPaymentStatus as
            | 'pending'
            | 'paid'
            | 'cancelled'
            | 'refunded'
            | 'custom'
        );

        if (result.success) {
          // Show success message with appropriate status text
          const statusText = {
            pending: 'payment marked as pending',
            paid: 'payment marked as paid',
            cancelled: 'payment cancelled',
            refunded: 'payment refunded',
            custom: 'payment marked as custom',
          }[selectedPaymentStatus];

          toast.success(`Order ${statusText} successfully`);
        } else {
          throw new Error(result.error);
        }
      } catch (error) {
        // Show error message
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Unable to update payment status. Please try again.';

        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {order.status !== 'reserved' && (
        <Tooltip>
          <TooltipTrigger>
            <Badge variant="reserved">
              <Info />
              Standard Payment
            </Badge>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            Payment status can only be modified for 'Reserved' orders.
          </TooltipContent>
        </Tooltip>
      )}

      <Select
        value={order.paymentStatus}
        onValueChange={handlePaymentStatusChange}
        disabled={isLoading || order.status !== 'reserved'}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select payment status" />
          {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="pending">Pending</SelectItem>
          <SelectItem value="paid">Paid</SelectItem>
          <SelectItem value="cancelled">Cancelled</SelectItem>
          <SelectItem value="refunded">Refunded</SelectItem>
          <SelectItem value="custom">Custom</SelectItem>
        </SelectContent>
      </Select>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Order Payment Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the payment status to{' '}
              <strong>{selectedPaymentStatus}</strong>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
