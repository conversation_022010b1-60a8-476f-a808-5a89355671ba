import type { SerializedOrder } from '@/types';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';

interface OrderRemarkProps {
  order: SerializedOrder;
}

export function OrderRemark({ order }: OrderRemarkProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Remark</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          <p className="text-muted-foreground text-sm">
            {order.event?.checkoutFormQuestion}
          </p>
          <p className="font-medium">{order.checkoutFormAnswer}</p>
        </div>
      </CardContent>
    </Card>
  );
}
