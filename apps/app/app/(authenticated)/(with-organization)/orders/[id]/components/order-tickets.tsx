'use client';

import type { SerializedOrder } from '@/types';
import { TicketStatus } from '@prisma/client';
import {
  DownloadIcon,
  ExternalLinkIcon,
  Loader2,
  MailIcon,
  PhoneIcon,
} from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from '@repo/design-system/components/ui/card';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  formatCurrency,
  formatDate,
  formatDateTime,
  formatTime,
} from '@repo/design-system/lib/format';
import { downloadTicket } from '@repo/email/utils/pdf';
import Link from 'next/link';
import { title } from 'radash';
import { useState } from 'react';

interface OrderTicketsProps {
  tickets: SerializedOrder['tickets'];
}

export function OrderTickets({ tickets }: OrderTicketsProps) {
  const [downloadingTicketId, setDownloadingTicketId] = useState<string | null>(
    null
  );

  const handleDownloadTicket = async (ticketId: string) => {
    try {
      setDownloadingTicketId(ticketId);
      const downloadUrl = await downloadTicket(ticketId);

      // Open the PDF in a new tab
      if (downloadUrl) {
        window.open(downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Error downloading ticket:', error);
      toast.error('Failed to download ticket. Please try again.');
    } finally {
      setDownloadingTicketId(null);
    }
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {tickets.map((ticket) => {
        if (!ticket) {
          return null;
        }

        const isDownloading = downloadingTicketId === ticket.id;

        return (
          <Card key={ticket.id} className="gap-0 p-0">
            <CardHeader className="bg-muted/50 p-4">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1">
                  <p className="font-medium">{ticket.event?.title}</p>
                </div>
                <Badge variant={ticket.status} className="px-2 py-0 text-xs">
                  {title(ticket.status)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="grid grid-cols-2 space-y-3 p-4">
              <div className="text-sm">
                <p className="mb-1 text-muted-foreground">Ticket Code</p>
                <p className="font-medium">{ticket.slug}</p>
              </div>
              <div className="text-sm">
                <p className="mb-1 text-muted-foreground">Ticket Type</p>
                <p className="font-medium">{ticket.ticketType.name}</p>
              </div>

              {ticket.timeSlot && (
                <div className="text-sm">
                  <p className="mb-1 text-muted-foreground">Time Slot</p>
                  <p>{formatDate(new Date(ticket.timeSlot.startTime))}</p>
                  <p>
                    {formatTime(new Date(ticket.timeSlot.startTime))} -{' '}
                    {formatTime(new Date(ticket.timeSlot.endTime))}
                  </p>
                </div>
              )}

              <div className="text-sm">
                <p className="mb-1 text-muted-foreground">Price</p>
                <p className="font-medium">
                  {formatCurrency(ticket.ticketType.price)}
                </p>
              </div>

              {/* TODO: show qr code */}
              {/* {ticket.qrCode && (
                <div className="flex justify-center py-2">
                  <div className="relative h-32 w-32">
                    <Image
                      src={ticket.qrCode}
                      alt="Ticket QR Code"
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>
              )} */}

              {ticket.ownerName && (
                <div className="col-span-2 mt-3 space-y-1 border-t pt-3 text-sm">
                  <p className="mb-1 text-muted-foreground">Ticket Owner</p>
                  <div className="font-medium">{ticket.ownerName}</div>
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <MailIcon className="h-3 w-3" />
                    {ticket.ownerEmail}
                  </div>
                  {ticket.ownerPhone && (
                    <div className="flex items-center gap-1 text-muted-foreground">
                      <PhoneIcon className="h-3 w-3" />
                      {ticket.ownerPhone}
                    </div>
                  )}
                </div>
              )}
              {ticket.purchaseDate && (
                <div className="text-sm">
                  <p className="mb-1 text-muted-foreground">Purchased on</p>
                  <p>{formatDateTime(new Date(ticket.purchaseDate))}</p>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-wrap justify-end gap-2 p-4">
              {ticket.event?.slug && (
                <Button variant="outline" size="sm" className="gap-2" asChild>
                  <Link href={`/events/${ticket.event.slug}`}>
                    <ExternalLinkIcon className="h-4 w-4" />
                    View Event
                  </Link>
                </Button>
              )}
              {(ticket.status === TicketStatus.purchased ||
                ticket.status === TicketStatus.reserved) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  onClick={() => handleDownloadTicket(ticket.id)}
                  disabled={isDownloading}
                >
                  {isDownloading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Downloading...
                    </>
                  ) : (
                    <>
                      <DownloadIcon className="h-4 w-4" />
                      Download Ticket
                    </>
                  )}
                </Button>
              )}
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
}
