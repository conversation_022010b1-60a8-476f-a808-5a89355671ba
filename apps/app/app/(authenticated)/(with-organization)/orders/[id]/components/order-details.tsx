import type { SerializedOrder } from '@/types';
import { Info } from '@repo/design-system/components/icons';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Separator } from '@repo/design-system/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { formatCurrency } from '@repo/design-system/lib/format';
import { title } from 'radash';
import { OrderPaymentStatus } from './order-payment-status';

interface OrderDetailsProps {
  order: SerializedOrder;
}

export function OrderDetails({ order }: OrderDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <CardTitle>Payment Information</CardTitle>
          <OrderPaymentStatus order={order} />
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className=" space-y-4 ">
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Payment Method</p>
            <div className="flex items-center">
              <p className="font-medium capitalize">
                {order.status === 'reserved'
                  ? 'N/A'
                  : title(order.paymentMethod)}
              </p>
              {order.status === 'reserved' && (
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="ml-2 h-4 w-4" />
                  </TooltipTrigger>
                  <TooltipContent>This is a reserved order.</TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-sm">Transaction ID</p>
            <div className="flex items-center">
              <p className="font-medium">
                {order.status === 'reserved'
                  ? 'N/A'
                  : order.transactionId || 'N/A'}
              </p>
              {order.status === 'reserved' && (
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="ml-2 h-4 w-4" />
                  </TooltipTrigger>
                  <TooltipContent>This is a reserved order.</TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="mb-6 font-medium">Order Summary</h3>
          <div className="space-y-2">
            {order.tickets.map((ticket, index) => {
              if (!ticket || !ticket.event) {
                return null;
              }

              return (
                <div key={index} className="flex justify-between">
                  <span>
                    {ticket.event.title} - {ticket.ticketType.name}
                  </span>
                  <span>{formatCurrency(ticket.ticketType.price)}</span>
                </div>
              );
            })}

            <Separator />

            <div className="flex justify-between font-medium">
              <span>Total</span>
              <span>{formatCurrency(order.totalAmount)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
