'use client';

import { DeleteConfirmationDialog } from '@/app/(authenticated)/(with-organization)/components/delete-confirmation-dialog';
import type { SerializedOrder } from '@/types';
import {
  DollarSign,
  Loader2,
  PrinterIcon,
  Send,
} from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { toast } from '@repo/design-system/components/ui/sonner';
import { formatDateTime } from '@repo/design-system/lib/format';
import { formatCurrency } from '@repo/design-system/lib/format';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { deleteReservedOrder, sendEmail } from '../action';
import { OrderStatus } from './order-status';

interface OrderSummaryProps {
  order: SerializedOrder;
}

export function OrderSummary({ order }: OrderSummaryProps) {
  const router = useRouter();

  const [isSending, setIsSending] = useState<boolean>(false);

  const handleDeleteOrder = async () => {
    const result = await deleteReservedOrder(order.id);

    if (result.success) {
      toast.success('Order deleted successfully');
      // Redirect to orders list
      router.push('/orders');
    } else {
      toast.error(
        result.error ||
          'Please try again or contact support if the issue persists.'
      );
      throw new Error(result.error);
    }
  };

  const handleSendEmail = async (orderId: string) => {
    try {
      setIsSending(true);
      toast.loading('Sending Order Confirmation Email', {
        id: 'order-confirmation',
      });

      const response = await sendEmail(orderId);

      if (response.success) {
        toast.success('Email successfully sent', {
          id: 'order-confirmation',
        });
      } else {
        console.error('Error sending email:', response.error);
        toast.error(
          response.error || 'Failed to send email. Please try again.',
          {
            id: 'order-confirmation',
          }
        );
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('Failed to send email. Please try again.', {
        id: 'order-confirmation',
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="grid gap-4 md:grid-cols-4">
      <Card className="md:col-span-4">
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <CardTitle className="text-2xl">Order #{order.id}</CardTitle>
            <OrderStatus order={order} />
          </div>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-1">
              <p className="text-muted-foreground text-sm">Order Date</p>
              <p className="font-medium">{formatDateTime(order.orderedAt)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-muted-foreground text-sm">Total Amount</p>
              <p className="font-medium text-lg">
                {formatCurrency(order.totalAmount)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-muted-foreground text-sm">Total Tickets</p>
              <p className="font-medium">{order.tickets?.length ?? 0}</p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0">
          <div className="flex w-full justify-between">
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" className="gap-2">
                <PrinterIcon className="h-4 w-4" />
                Print Order
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="gap-2"
                onClick={() => handleSendEmail(order.id)}
                disabled={isSending}
              >
                {isSending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    Send Order Confirmation
                  </>
                )}
              </Button>
            </div>

            {order.status === 'reserved' ? (
              <DeleteConfirmationDialog
                variant="destructive"
                size="sm"
                title="Delete Order"
                description="Are you sure you want to delete this order? This action cannot be undone and will permanently delete the order and all associated tickets."
                deleteButtonText="Delete Order"
                onDelete={handleDeleteOrder}
                onError={() =>
                  toast.error(
                    'Please try again or contact support if the issue persists.'
                  )
                }
              />
            ) : (
              <Button
                variant="destructive"
                size="sm"
                className="gap-2"
                disabled
                // TODO: Implement refund functionality
              >
                <DollarSign className="h-4 w-4" />
                Refund Order
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
