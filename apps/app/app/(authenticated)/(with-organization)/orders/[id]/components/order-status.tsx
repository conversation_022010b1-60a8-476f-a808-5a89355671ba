'use client';

import type { SerializedOrder } from '@/types';
import { Info, Loader2 } from '@repo/design-system/components/icons';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { useState } from 'react';
import { updateOrderStatus } from '../action';

interface OrderStatusProps {
  order: SerializedOrder;
}

export function OrderStatus({ order }: OrderStatusProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleStatusChange = (newStatus: string) => {
    setSelectedStatus(newStatus);
    setIsOpen(true);
  };

  const handleConfirm = async () => {
    if (selectedStatus) {
      if (selectedStatus === 'reserved') {
        return;
      }

      setIsLoading(true);

      try {
        const result = await updateOrderStatus(
          order.id,
          selectedStatus as 'pending' | 'completed' | 'cancelled'
        );

        if (result.success) {
          // Show success message with appropriate status text
          const statusText = {
            pending: 'marked as pending',
            completed: 'marked as completed',
            cancelled: 'cancelled',
          }[selectedStatus];

          toast.success(`Order ${statusText} successfully`);
        } else {
          throw new Error(result.error);
        }
      } catch (error) {
        // Show error message
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Unable to update order status. Please try again.';

        toast.error(errorMessage);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {order.status === 'reserved' && (
        <Tooltip>
          <TooltipTrigger>
            <Badge variant="reserved">
              <Info />
              Reserved Order
            </Badge>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            This order was manually created, therefore the status is reserved
            and cannot be updated.
          </TooltipContent>
        </Tooltip>
      )}

      <Select
        value={order.status}
        onValueChange={handleStatusChange}
        disabled={isLoading || order.status === 'reserved'}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select status" />
          {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
        </SelectTrigger>
        <SelectContent>
          {order.status === 'reserved' && (
            <SelectItem value="reserved">Reserved</SelectItem>
          )}
          <SelectItem value="pending">Pending</SelectItem>
          <SelectItem value="completed">Completed</SelectItem>
          <SelectItem value="cancelled">Cancelled</SelectItem>
        </SelectContent>
      </Select>

      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Order Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the order status to{' '}
              {selectedStatus}? This will update the status for all tickets in
              this order.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Continue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
