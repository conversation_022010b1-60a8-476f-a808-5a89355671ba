'use server';

import { database, serializePrisma } from '@repo/database';
import { sendOrderConfirmationEmail } from '@repo/email/utils/order-confirmation';
import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';
import { notFound } from 'next/navigation';

export async function getOrder(id: string) {
  const order = await database.order.findUnique({
    where: {
      id,
    },
    select: {
      id: true,
      userId: true,
      eventId: true,
      event: true,
      status: true,
      totalAmount: true,
      paymentMethod: true,
      transactionId: true,
      paymentStatus: true,
      orderedAt: true,
      createdAt: true,
      updatedAt: true,
      user: true,
      tickets: {
        include: {
          event: true,
          ticketType: true,
          timeSlot: true,
        },
      },
      checkoutFormAnswer: true,
    },
  });

  if (!order) {
    notFound();
  }

  return serializePrisma(order);
}

export async function updateOrderStatus(
  orderId: string,
  status: 'pending' | 'completed' | 'cancelled'
) {
  try {
    // Use a transaction to ensure both order and tickets are updated consistently
    await database.$transaction(async (tx) => {
      // Update the order status
      await tx.order.update({
        where: {
          id: orderId,
        },
        data: {
          status,
        },
      });

      // Sync ticket status to order status
      // order: pending/cancelled => ticket: cancelled
      // order: completed => ticket: purchased
      await tx.ticket.updateMany({
        where: {
          orderId,
        },
        data: {
          status:
            status === 'pending' || status === 'cancelled'
              ? 'cancelled'
              : 'purchased',
        },
      });
    });

    revalidatePath(`/orders/${orderId}`);

    return {
      success: true,
      message: `Order status updated to ${status}`,
    };
  } catch (error) {
    console.error('Error updating order status:', error);
    return {
      success: false,
      error: 'Unable to update order status. Please try again.',
    };
  }
}

export async function updateOrderPaymentStatus(
  orderId: string,
  paymentStatus: 'pending' | 'paid' | 'cancelled' | 'refunded' | 'custom'
) {
  try {
    const order = await database.order.update({
      where: {
        id: orderId,
      },
      data: {
        paymentStatus,
      },
      select: {
        id: true,
      },
    });

    revalidatePath(`/orders/${orderId}`);

    return {
      success: true,
      order,
    };
  } catch (error) {
    console.error('Error updating order payment status:', error);
    return {
      success: false,
      error: 'Unable to update order payment status. Please try again.',
    };
  }
}

export async function deleteReservedOrder(orderId: string) {
  try {
    // First, check if the order exists and has 'reserved' status
    const order = await database.order.findUnique({
      where: {
        id: orderId,
      },
      select: {
        id: true,
        status: true,
        tickets: {
          select: {
            id: true,
            timeSlotId: true,
            ticketTypeId: true,
          },
        },
      },
    });

    if (!order) {
      return {
        success: false,
        error: 'Order not found. Please try again.',
      };
    }

    if (order.status !== 'reserved') {
      return {
        success: false,
        error:
          'Only reserved orders can be deleted. Please contact support for assistance with other orders.',
      };
    }

    // Delete the order and all associated tickets
    // Also restore inventory for each ticket
    await database.$transaction(async (tx) => {
      // Group tickets by timeSlot and ticketType to count them
      const ticketCounts = order.tickets.reduce(
        (acc, ticket) => {
          const key = `${ticket.timeSlotId}_${ticket.ticketTypeId}`;
          if (!acc[key]) {
            acc[key] = {
              timeSlotId: ticket.timeSlotId,
              ticketTypeId: ticket.ticketTypeId,
              count: 0,
            };
          }
          acc[key].count += 1;
          return acc;
        },
        {} as Record<
          string,
          { timeSlotId: string; ticketTypeId: string; count: number }
        >
      );

      // Restore inventory for each ticket group
      for (const key in ticketCounts) {
        if (Object.hasOwn(ticketCounts, key)) {
          const { timeSlotId, ticketTypeId, count } = ticketCounts[key];

          // Find the current inventory
          const inventory = await tx.inventory.findUnique({
            where: {
              timeSlotId_ticketTypeId: {
                timeSlotId,
                ticketTypeId,
              },
            },
          });

          if (inventory) {
            // Restore inventory quantity
            await tx.inventory.update({
              where: {
                id: inventory.id,
              },
              data: {
                quantity: {
                  increment: count,
                },
              },
            });
          }
        }
      }

      // Delete all tickets associated with this order
      await tx.ticket.deleteMany({
        where: {
          orderId,
        },
      });

      // Then delete the order itself
      await tx.order.delete({
        where: {
          id: orderId,
        },
      });
    });

    revalidatePath('/orders');

    return {
      success: true,
      message: 'Order deleted successfully',
    };
  } catch (error) {
    console.error('Error deleting order:', error);
    return {
      success: false,
      error: 'Unable to delete order. Please try again.',
    };
  }
}

export async function sendEmail(orderId: string) {
  try {
    const response = await sendOrderConfirmationEmail(orderId);

    // Return the success status directly from the email sending function
    return {
      success: response.success,
      message: 'Order confirmation email sent successfully',
    };
  } catch (error) {
    const message = parseError(error);
    log.error(`Error sending order confirmation email: ${message}`, {
      error,
      orderId,
    });

    // Return a structured error response instead of throwing
    return {
      success: false,
      error: 'Failed to send order confirmation email. Please try again.',
    };
  }
}
