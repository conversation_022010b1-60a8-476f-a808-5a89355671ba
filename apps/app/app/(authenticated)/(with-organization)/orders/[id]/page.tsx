import { OrderRemark } from '@/app/(authenticated)/(with-organization)/orders/[id]/components/order-remark';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from '@repo/design-system/components/ui/tabs';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getOrder } from './action';
import { OrderCustomer } from './components/order-customer';
import { OrderDetails } from './components/order-details';
import { OrderSummary } from './components/order-summary';
import { OrderTickets } from './components/order-tickets';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const order = await getOrder(id);

  const title = 'Order - TicketCARE';
  const description = `Details for order ${order.id}`;

  return {
    title,
    description,
  };
}

export default async function OrderDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const order = await getOrder(id);

  return (
    <>
      <Header pages={['Orders']} page={`Order #${order.id}`} />

      <div className="flex-1 space-y-6 p-4 pt-6 lg:p-8">
        {/* Order Summary Section */}
        <OrderSummary order={order} />

        {/* Order Details Tabs */}
        <Tabs defaultValue="details" className="w-full">
          <TabsList>
            <TabsTrigger value="details">Order Details</TabsTrigger>
            <TabsTrigger value="tickets">Tickets</TabsTrigger>
            <TabsTrigger value="customer">Customer</TabsTrigger>
            <TabsTrigger value="remark">Remark</TabsTrigger>
          </TabsList>

          {/* Order Details Tab */}
          <TabsContent value="details" className="space-y-4 pt-4">
            <OrderDetails order={order} />
          </TabsContent>

          {/* Tickets Tab */}
          <TabsContent value="tickets" className="space-y-4 pt-4">
            <OrderTickets tickets={order.tickets} />
          </TabsContent>

          {/* Customer Tab */}
          <TabsContent value="customer" className="space-y-4 pt-4">
            <OrderCustomer user={order.user} />
          </TabsContent>

          {/* Remark Tab */}
          <TabsContent value="remark" className="space-y-4 pt-4">
            <OrderRemark order={order} />
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
