import { getOrganizer } from '@/app/(authenticated)/(with-organization)/admin/organizers/actions';
import { MetadataCard } from '@/app/(authenticated)/(with-organization)/settings/components/metadata-card';
import { NotificationPreferencesCard } from '@/app/(authenticated)/(with-organization)/settings/components/notification-preferences-card';
import { PaymentPlatformCard } from '@/app/(authenticated)/(with-organization)/settings/components/payment-platform-card';
import { auth } from '@repo/auth/server';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import type { ReactElement } from 'react';
import { Header } from '../components/header';

const title = 'TicketCARE - Settings';
const description = 'TicketCARE - Settings';

export const metadata: Metadata = {
  title,
  description,
};

export default async function SettingsPage(): Promise<ReactElement> {
  const sessionData = await auth.api.getSession({
    headers: await headers(),
  });
  const { organizerId } = sessionData?.session ?? {};

  if (!sessionData?.user) {
    return notFound();
  }

  const organizer = await getOrganizer(organizerId ?? '');

  return (
    <>
      <Header page="Settings" />
      <div className="flex items-center justify-between gap-4 px-4">
        <div>
          <h1 className="font-bold text-3xl">Settings</h1>
          <p className="text-muted-foreground">
            Set preferences, manage integrations, and tailor app functionality.
          </p>
        </div>
      </div>
      <main className="flex-1 space-y-4 p-4 pt-6 md:px-4">
        <Tabs defaultValue="notifications">
          <div className="grid grid-cols-[200px_4fr] gap-4">
            <div>
              <TabsList className="flex h-auto w-full flex-col">
                <TabsTrigger
                  value="notifications"
                  className="w-full justify-start px-4 py-3"
                >
                  Notifications
                </TabsTrigger>
                <TabsTrigger
                  value="payment"
                  className="w-full justify-start px-4 py-3"
                >
                  Custom Payment
                </TabsTrigger>
                <TabsTrigger
                  value="metadata"
                  className="w-full justify-start px-4 py-3"
                >
                  Metadata
                </TabsTrigger>
              </TabsList>
            </div>
            <div>
              <TabsContent value="notifications" className="mt-0 space-y-4">
                <NotificationPreferencesCard organizer={organizer} />
              </TabsContent>
              <TabsContent value="payment" className="mt-0 space-y-4">
                <PaymentPlatformCard organizer={organizer} />
              </TabsContent>
              <TabsContent value="metadata" className="mt-0 space-y-4">
                <MetadataCard organizer={organizer} />
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </main>
    </>
  );
}
