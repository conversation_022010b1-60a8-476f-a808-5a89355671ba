'use server';

import type { ConfigureChipFormValues } from '@/app/(authenticated)/(with-organization)/settings/components/configure-chip-dialog';
import { env } from '@/env';
import { auth } from '@repo/auth/server';
import { decrypt, encrypt, database as prisma } from '@repo/database';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';

/**
 * Creates a webhook on CHIP platform
 */
async function createChipWebhook(secretKey: string) {
  try {
    // Get the current hostname for the webhook URL
    const webhookUrl = `${env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`;

    const response = await fetch(`${env.CHIP_BASE_URL}/webhooks/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${secretKey}`,
      },
      body: JSON.stringify({
        title: 'TicketCARE Webhook',
        all_events: true,
        callback: webhookUrl,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Failed to create CHIP webhook:', errorData);
      throw new Error(
        `CHIP webhook creation failed: ${JSON.stringify(errorData)}`
      );
    }

    const webhookData = await response.json();
    return webhookData;
  } catch (error) {
    console.error('Error creating CHIP webhook:', error);
    throw error;
  }
}

/**
 * Saves CHIP payment gateway configuration for an organizer
 */
export async function saveChipConfiguration(formData: ConfigureChipFormValues) {
  try {
    const sessionData = await auth.api.getSession({
      headers: await headers(),
    });
    const { organizerId } = sessionData?.session ?? {};

    // Get current user session
    if (!sessionData) {
      return { success: false, error: 'Unauthorized' };
    }

    if (!organizerId) {
      // organizer hasn't complete onboarding
      return {
        success: false,
        error:
          'Please complete your onboarding before trying to setup payment gateway.',
      };
    }

    // Find the organizer associated with the current user
    const organizer = await prisma.organizer.findUnique({
      where: { id: organizerId },
      select: { id: true },
    });

    if (!organizer) {
      return { success: false, error: 'Organizer not found' };
    }

    // Create webhook on CHIP platform
    const webhookData = await createChipWebhook(formData.secretKey);

    // Extract the webhook secret from the response
    const webhookSecret = webhookData.public_key || '';

    // Encrypt sensitive data
    const encryptedSecretKey = encrypt(formData.secretKey);
    const encryptedBrandId = encrypt(formData.brandId);
    const encryptedWebhookSecret = encrypt(webhookSecret);

    if (encryptedSecretKey === null || encryptedWebhookSecret === null) {
      return { success: false, error: 'Encryption failed' };
    }

    // Update the organizer record with the encrypted configuration
    await prisma.organizer.update({
      where: { id: organizer.id },
      data: {
        chipSecretKey: encryptedSecretKey,
        chipBrandId: encryptedBrandId,
        chipWebhookSecret: encryptedWebhookSecret,
      },
    });

    revalidatePath('/settings');
    return { success: true };
  } catch (error) {
    console.error('Failed to save CHIP configuration:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to save configuration',
    };
  }
}

/**
 * Resets CHIP payment gateway configuration for the current organizer
 */
export async function resetChipConfiguration() {
  try {
    const sessionData = await auth.api.getSession({
      headers: await headers(),
    });
    const { organizerId } = sessionData?.session ?? {};

    // Get current user session
    if (!sessionData) {
      return { success: false, error: 'Unauthorized' };
    }

    if (!organizerId) {
      return { success: false, error: 'Organizer ID not found' };
    }

    // Get the organizer record
    const organizer = await prisma.organizer.findUnique({
      where: { id: organizerId },
      select: { id: true },
    });

    if (!organizer) {
      return { success: false, error: 'Organizer not found' };
    }

    // Reset the configuration
    await prisma.organizer.update({
      where: { id: organizer.id },
      data: {
        chipSecretKey: null,
        chipBrandId: null,
        chipWebhookSecret: null,
      },
    });

    revalidatePath('/settings');
    return { success: true };
  } catch (error) {
    console.error('Failed to reset CHIP configuration:', error);
    return { success: false, error: 'Failed to reset configuration' };
  }
}

/**
 * Retrieves CHIP payment gateway configuration for the current organizer
 */
export async function getChipConfiguration() {
  try {
    const sessionData = await auth.api.getSession({
      headers: await headers(),
    });
    const { organizerId } = sessionData?.session ?? {};

    if (!sessionData) {
      // Get current user session
      return { success: false, error: 'Unauthorized' };
    }

    if (!organizerId) {
      return { success: false, error: 'Organizer ID not found' };
    }

    // Find the organizer with their CHIP configuration
    const organizer = await prisma.organizer.findUnique({
      where: { id: organizerId },
      select: {
        chipSecretKey: true,
        chipBrandId: true,
        chipWebhookSecret: true,
      },
    });

    if (!organizer) {
      return { success: false, error: 'Organizer not found' };
    }

    // Decrypt the sensitive data
    const secretKey = organizer.chipSecretKey
      ? decrypt(organizer.chipSecretKey)
      : null;
    const brandId = organizer.chipBrandId
      ? decrypt(organizer.chipBrandId)
      : null;
    const webhookSecret = organizer.chipWebhookSecret
      ? decrypt(organizer.chipWebhookSecret)
      : null;

    return {
      success: true,
      data: {
        secretKey: secretKey || '',
        brandId: brandId || '',
        webhookSecret: webhookSecret || '',
      },
    };
  } catch (error) {
    console.error('Failed to retrieve CHIP configuration:', error);
    return { success: false, error: 'Failed to retrieve configuration' };
  }
}
