import type { SerializedOrganizer } from '@/types';
import { Clock } from '@repo/design-system/components/icons';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDate } from '@repo/design-system/lib/format';

// Component for metadata card
export function MetadataCard({
  organizer,
}: { organizer: SerializedOrganizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-4 w-4" />
          Metadata
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Created
            </dt>
            {organizer?.createdAt && (
              <dd>{formatDate(new Date(organizer.createdAt))}</dd>
            )}
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Last Updated
            </dt>
            {organizer?.updatedAt && (
              <dd>{formatDate(new Date(organizer.updatedAt))}</dd>
            )}
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              User ID
            </dt>
            <dd>{organizer?.userId || 'Not linked'}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}
