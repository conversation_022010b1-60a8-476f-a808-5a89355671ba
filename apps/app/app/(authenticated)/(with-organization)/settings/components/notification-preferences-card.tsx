import { OrganizerNotification } from '@/app/(authenticated)/(with-organization)/admin/organizers/components/organizer-notification';
import type { SerializedOrganizer } from '@/types';
import { Bell } from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';

// Component for notification preferences card
export function NotificationPreferencesCard({
  organizer,
}: { organizer: SerializedOrganizer }) {
  return (
    <Card shadow={false}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-4 w-4" />
          Notification Preferences
        </CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Email Notifications
            </dt>
            <dd>
              <Badge
                variant={organizer?.emailNotifications ? 'success' : 'outline'}
              >
                {organizer?.emailNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              SMS Notifications
            </dt>
            <dd>
              <Badge
                variant={organizer?.smsNotifications ? 'success' : 'outline'}
              >
                {organizer?.smsNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </dd>
          </div>
          <div>
            <dt className="font-medium text-muted-foreground text-sm">
              Push Notifications
            </dt>
            <dd>
              <Badge
                variant={organizer?.pushNotifications ? 'success' : 'outline'}
              >
                {organizer?.pushNotifications ? 'Enabled' : 'Disabled'}
              </Badge>
            </dd>
          </div>
        </dl>
      </CardContent>
      <CardFooter className="flex items-center justify-end">
        <OrganizerNotification organizer={organizer} />
      </CardFooter>
    </Card>
  );
}
