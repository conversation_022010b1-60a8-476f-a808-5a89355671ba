import { getChipConfiguration } from '@/app/(authenticated)/(with-organization)/settings/action';
import { ConfigureChipDialog } from '@/app/(authenticated)/(with-organization)/settings/components/configure-chip-dialog';
import ChipLogo from '@/public/chip-logo.png';
import <PERSON>e<PERSON><PERSON> from '@/public/stripe-logo.png';
import type { SerializedOrganizer } from '@/types';
import {
  CreditCard,
  ExternalLink,
  InfoIcon,
} from '@repo/design-system/components/icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import Image from 'next/image';
import Link from 'next/link';

export async function PaymentPlatformCard({
  organizer,
}: { organizer: SerializedOrganizer }) {
  // fetch decrypted chip configurations
  const { data: chipConfiguration } = await getChipConfiguration();

  return (
    <Card shadow={false}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-4 w-4" />
          Supported Payment Gateways
        </CardTitle>
        <CardDescription className="flex items-start gap-2">
          <InfoIcon className="h-4 w-4 mt-[2px]" />
          <div>
            <p className="font-bold">Only applicable to Premium events.</p>
            <p>
              Process payments with your own payment gateway account instead of
              the default TicketCARE payment gateway.
            </p>
          </div>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <Image src={ChipLogo} alt="chip-payment" width={160} height={40} />
          <div className="flex-1">
            <Link
              href="https://www.chip-in.asia/"
              target="_blank"
              referrerPolicy="no-referrer"
              className="text-muted-foreground hover:text-primary hover:underline"
            >
              <div className="flex items-center gap-1">
                <span className="font-medium text-lg">CHIP</span>
                <span>
                  <ExternalLink size={14} />
                </span>
              </div>
            </Link>
            {organizer?.chipSecretKey &&
            organizer?.chipBrandId &&
            organizer?.chipWebhookSecret ? (
              <Badge variant="success">Connected</Badge>
            ) : (
              <Badge variant="pending">Not connected</Badge>
            )}
          </div>
          <ConfigureChipDialog
            secretKey={chipConfiguration?.secretKey}
            brandId={chipConfiguration?.brandId}
            webhookSecret={chipConfiguration?.webhookSecret}
          />
        </div>

        <div className="flex items-center justify-between gap-4">
          <Image src={StripeLogo} alt="chip-payment" width={160} height={40} />
          <div className="flex-1">
            <Link
              href="https://stripe.com/"
              target="_blank"
              referrerPolicy="no-referrer"
              className="text-muted-foreground hover:text-primary hover:underline"
            >
              <div className="flex items-center gap-1">
                <span className="font-medium text-lg">Stripe</span>
                <span>
                  <ExternalLink size={14} />
                </span>
              </div>
            </Link>
          </div>
          <Button variant="secondary" disabled>
            Coming soon
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
