'use client';

import { DeleteConfirmationDialog } from '@/app/(authenticated)/(with-organization)/components/delete-confirmation-dialog';
import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import { Eye, EyeOff } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
} from '@repo/design-system/components/ui/drawer';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useState } from 'react';
import { z } from 'zod';
import { resetChipConfiguration, saveChipConfiguration } from '../action';

const configureChipSchema = z.object({
  secretKey: z.string().min(1, 'Secret Key is required'),
  brandId: z.string().min(1, 'Brand ID is required'),
  // webhookSecret is no longer required as it will be automatically generated
});

export type ConfigureChipFormValues = z.infer<typeof configureChipSchema>;

interface ConfigureChipDialogProps {
  secretKey?: string;
  brandId?: string;
  webhookSecret?: string;
}

export function ConfigureChipDialog({
  secretKey,
  brandId,
}: ConfigureChipDialogProps) {
  const isConnected = !!secretKey && !!brandId;

  const [open, setOpen] = useState(false);
  const isDesktop = useIsDesktop();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [showBrandId, setShowBrandId] = useState(false);

  const form = useForm<ConfigureChipFormValues>({
    defaultValues: {
      secretKey: secretKey ?? '',
      brandId: brandId ?? '',
    },
  });

  async function onSubmit(values: ConfigureChipFormValues) {
    try {
      setIsSubmitting(true);

      // Save the configuration using the server action
      const result = await saveChipConfiguration(values);

      if (result.success) {
        toast.success('CHIP configuration saved successfully');
        setOpen(false);
      } else {
        toast.error(result.error || 'Failed to save configuration');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleReset = async () => {
    try {
      setIsSubmitting(true);

      // Reset the configuration using the server action
      const result = await resetChipConfiguration();

      if (result.success) {
        toast.success('CHIP configuration reset successfully');
        setOpen(false);
      } else {
        toast.error(result.error || 'Failed to reset configuration');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Something went wrong';

      toast.error(errorMessage);
    } finally {
      // reset form
      form.reset();
      setIsSubmitting(false);
    }
  };

  const ConfigureChipForm = () => (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full space-y-4 px-4 md:px-0"
      >
        <FormField
          control={form.control}
          name="secretKey"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Secret Key</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="Enter your CHIP Secret Key"
                    {...field}
                    type={showSecretKey ? 'text' : 'password'}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowSecretKey(!showSecretKey)}
                  >
                    {showSecretKey ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormDescription>
                The secret key provided by CHIP for API authentication.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="brandId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Brand ID</FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    placeholder="Enter your CHIP Brand ID"
                    {...field}
                    type={showBrandId ? 'text' : 'password'}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowBrandId(!showBrandId)}
                  >
                    {showBrandId ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormDescription>
                Your unique brand identifier from CHIP.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between">
          <DeleteConfirmationDialog
            title="Reset Chip Setting"
            description={
              'Are you sure you want to reset the CHIP settings? This will delete all the existing configurations.'
            }
            deleteButtonText="Reset Setting"
            showIcon={false}
            onDelete={handleReset}
            variant="destructive"
            disabled={isSubmitting || !isConnected}
            className="max-md:w-full"
          />

          <Button
            type="submit"
            className="max-md:w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save Configuration'}
          </Button>
        </div>
      </form>
    </Form>
  );

  if (isDesktop) {
    return (
      <>
        <Button variant="outline" onClick={() => setOpen(true)}>
          Configure
        </Button>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle>Configure CHIP Payment Gateway</DialogTitle>
            </DialogHeader>
            <ConfigureChipForm />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <Button variant="outline" onClick={() => setOpen(true)}>
        Configure
      </Button>
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerContent>
          <DrawerHeader className="text-left">
            <DialogTitle>Configure CHIP Payment Gateway</DialogTitle>
          </DrawerHeader>
          <ConfigureChipForm />
          <DrawerFooter className="pt-2">
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </>
  );
}
