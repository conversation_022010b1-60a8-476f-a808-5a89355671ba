'use client';

import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { UserButton } from '@repo/auth/components/user-button';
import { useSignOutWithReset } from '@repo/auth/utils/sign-out-handler';
import type { User } from '@repo/database/types';
import {
  CalendarIcon,
  CodeIcon,
  Settings2Icon,
  ShoppingBagIcon,
  UserCog2Icon,
  UsersRoundIcon,
  RadioIcon,
  NetworkIcon,
  UsersIcon,
  ShirtIcon,
  SparklesIcon,
  ShieldIcon,
  ChevronRightIcon,
  BuildingIcon,
  TargetIcon,
  MegaphoneIcon,
  UserCheckIcon,
  QrCodeIcon,
  TrendingUpIcon,
  PlugIcon,
  TicketIcon,
  MapPinIcon,
  SmartphoneIcon,
  PieChartIcon,
  WalletIcon,
  HelpingHandIcon,
  SettingsIcon,
} from '@repo/design-system/components/icons';
import { ModeToggle } from '@repo/design-system/components/mode-toggle';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@repo/design-system/components/ui/collapsible';
import { cn } from '@repo/design-system/lib/utils';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  sidebarMenuButtonVariants,
} from '@repo/design-system/components/ui/sidebar';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import type { ReactNode } from 'react';
import type { LucideIcon } from 'lucide-react';
import { useFrillWidget } from '@/app/hooks/use-frill-widget';

type GlobalSidebarProperties = {
  user?: User;
  activeOrganizationId?: string;
  activeOrganizationName?: string;
  readonly children: ReactNode;
};

type NavItem = {
  title: string;
  icon: LucideIcon;
  url?: string;
  isActive?: boolean;
};

type MenuSection = {
  title: string;
  icon: LucideIcon;
  items: NavItem[];
  showToRole?: 'super-admin' | 'all';
  defaultOpen?: boolean;
  isCollapsible?: boolean;
};

const menuSections: MenuSection[] = [
  {
    title: 'Platform',
    icon: BuildingIcon,
    showToRole: 'super-admin',
    items: [
      {
        title: 'Premium Tiers',
        url: '/admin/premium-tiers',
        icon: Settings2Icon,
        isActive: true,
      },
      {
        title: 'Venues',
        url: '/admin/venues',
        icon: MapPinIcon,
      },
      {
        title: 'Organizers',
        url: '/admin/organizers',
        icon: UserCog2Icon,
      },
      {
        title: 'Roadmap',
        url: '/admin/roadmap',
        icon: CodeIcon,
      },
    ],
  },
  {
    title: 'Ticketing',
    icon: TicketIcon,
    defaultOpen: true,
    items: [
      {
        title: 'Events',
        url: '/events',
        icon: CalendarIcon,
        isActive: true,
      },
      {
        title: 'Orders',
        url: '/orders',
        icon: ShoppingBagIcon,
      },
      {
        title: 'Guests',
        url: '/guests',
        icon: UsersRoundIcon,
      },
      {
        title: 'SeatSmart',
        icon: Settings2Icon,
      },
      {
        title: 'Passport',
        icon: ShieldIcon,
      },
    ],
  },
  {
    title: 'Promoting',
    icon: MegaphoneIcon,
    isCollapsible: true,
    items: [
      {
        title: 'ReachStudio',
        icon: RadioIcon,
      },
      {
        title: 'InnerCircle',
        icon: TargetIcon,
      },
    ],
  },
  {
    title: 'Managing',
    icon: UserCheckIcon,
    isCollapsible: true,
    items: [
      {
        title: 'FanKeeper',
        icon: UsersIcon,
      },
      {
        title: 'CrowdConnect',
        icon: NetworkIcon,
      },
      {
        title: 'GearLounge',
        icon: ShirtIcon,
      },
    ],
  },
  {
    title: 'Scanning',
    icon: QrCodeIcon,
    isCollapsible: true,
    items: [
      {
        title: 'App',
        icon: SmartphoneIcon,
      },
    ],
  },
  {
    title: 'Reporting',
    icon: TrendingUpIcon,
    isCollapsible: true,
    items: [
      {
        title: 'Analytics',
        icon: PieChartIcon,
      },
    ],
  },
  {
    title: 'Integrating',
    icon: PlugIcon,
    isCollapsible: true,
    items: [
      {
        title: 'CheckoutFlow',
        url: '/checkoutflow',
        icon: WalletIcon,
      },
      {
        title: 'ImpactSpark',
        url: '/impactspark',
        icon: SparklesIcon,
      },
      {
        title: 'ComplySync',
        url: '/complysync',
        icon: ShieldIcon,
      },
    ],
  },
];

export const GlobalSidebar = ({
  user,
  // activeOrganizationName,
  children,
}: GlobalSidebarProperties) => {
  // const sidebar = useSidebar();
  // const { data: organization } = useActiveOrganization();
  const router = useRouter();
  const signOut = useSignOutWithReset();

  const whatsNewRef = useFrillWidget('widget', 'widget_sFgAEH_6bYVIK');
  const roadmapRef = useFrillWidget('widget', 'widget_caBzpt_aDYg5b');
  const featureRequestRef = useFrillWidget('widget', 'widget_N3KgST_SjJ0F2');

  return (
    <>
      <Sidebar variant="inset">
        <SidebarHeader>
          <Link href="/">
            <Image
              src={darkLogo}
              alt="Logo"
              width={124}
              height={24}
              className="hidden dark:block"
            />
            <Image
              src={lightLogo}
              alt="Logo"
              width={124}
              height={24}
              className="block dark:hidden"
            />
          </Link>
          {/* {activeOrganizationName && (
            <SidebarMenu>
              <SidebarMenuItem>
                <div
                  className={cn(
                    'h-[36px] overflow-hidden transition-all [&>div]:w-full',
                    sidebar.open ? '' : '-mx-1'
                  )}
                >
                  <OrganizationSwitcher
                    activeOrganization={
                      organization || {
                        name: activeOrganizationName,
                      }
                    }
                  />
                </div>
              </SidebarMenuItem>
            </SidebarMenu>
          )} */}
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              {menuSections
                .filter((section) => {
                  if (section.showToRole === 'super-admin') {
                    return user?.role === 'super-admin';
                  }
                  return true;
                })
                .map((section) => {
                  return (
                    <SidebarMenu key={section.title}>
                      <Collapsible
                        asChild
                        defaultOpen={section.defaultOpen}
                        className="group/collapsible"
                      >
                        <SidebarMenuItem>
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton>
                              <section.icon />
                              <span>{section.title}</span>
                              <ChevronRightIcon className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="space-y-1">
                            {section.items.map((item: NavItem) => (
                              <SidebarMenuSub key={item.title}>
                                <SidebarMenuSubButton
                                  asChild={Boolean(item.url)}
                                >
                                  {item.url ? (
                                    <Link href={item.url}>
                                      <item.icon />
                                      <span>{item.title}</span>
                                    </Link>
                                  ) : (
                                    <div
                                      className={cn(
                                        sidebarMenuButtonVariants(),
                                        'pointer-events-none opacity-50 px-0'
                                      )}
                                    >
                                      <item.icon />
                                      <span>{item.title}</span>
                                    </div>
                                  )}
                                </SidebarMenuSubButton>
                              </SidebarMenuSub>
                            ))}
                          </CollapsibleContent>
                        </SidebarMenuItem>
                      </Collapsible>
                    </SidebarMenu>
                  );
                })}
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel>Organizer</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="gap-0">
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <Link href="/settings">
                      <SettingsIcon />
                      <span>Settings</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup className="mt-auto">
            <SidebarGroupLabel>Products</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="gap-0">
                <SidebarMenuItem>
                  <SidebarMenuButton
                    onClick={() => whatsNewRef.current?.open()}
                    className="cursor-pointer"
                  >
                    <SparklesIcon />
                    <span>What's New</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton
                    onClick={() => roadmapRef.current?.open()}
                    className="cursor-pointer"
                  >
                    <CodeIcon />
                    <span>Roadmap</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton
                    onClick={() => featureRequestRef.current?.open()}
                    className="cursor-pointer"
                  >
                    <Settings2Icon />
                    <span>Feature Request</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup>
            <SidebarGroupLabel>Help</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <Link href="https://docs.ticketcare.my" target="_blank">
                      <HelpingHandIcon />
                      <span>Help Centre</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                {/* <SidebarMenuItem>
                  <SidebarMenuButton
                    onClick={() => {
                      window.Assistant('open');
                    }}
                    className="cursor-pointer"
                  >
                    <Settings2Icon />
                    <span>Contact Support</span>
                  </SidebarMenuButton>
                </SidebarMenuItem> */}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem className="flex items-center gap-2">
              <UserButton
                user={user}
                showName
                onSignOut={() => {
                  signOut();
                }}
                onProfileClick={() => {
                  router.push('/profile');
                }}
                className="flex-1"
              />
              <div className="flex shrink-0 items-center gap-px">
                <ModeToggle />
              </div>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
      <SidebarInset>{children}</SidebarInset>
    </>
  );
};
