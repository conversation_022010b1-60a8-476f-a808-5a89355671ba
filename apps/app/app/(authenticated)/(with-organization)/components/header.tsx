import {
  <PERSON><PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@repo/design-system/components/ui/breadcrumb';
import { Separator } from '@repo/design-system/components/ui/separator';
import { SidebarTrigger } from '@repo/design-system/components/ui/sidebar';
import { dash, isEmpty } from 'radash';
import { Fragment, type ReactNode } from 'react';

type HeaderProps = {
  pages?: string[];
  page: string;
  children?: ReactNode;
};

const adminPages = ['Organizers', 'Premium Tiers', 'Venues'];

export const Header = ({ pages, page, children }: HeaderProps) => {
  // Helper function to generate the breadcrumb link URL
  const getBreadcrumbHref = (pageName: string, index: number): string => {
    // If pages is undefined, provide a safe fallback
    if (!pages) {
      return adminPages.includes(pageName)
        ? `/admin/${dash(pageName.toLowerCase())}`
        : `/${dash(pageName.toLowerCase())}`;
    }

    // Check if we're in an admin section by examining the first page
    const isAdminSection = pages.length > 0 && adminPages.includes(pages[0]);

    if (index === 0) {
      // First level path
      return isAdminSection
        ? `/admin/${dash(pageName.toLowerCase())}`
        : `/${dash(pageName.toLowerCase())}`;
    }

    // For nested paths, build the full path with proper prefix
    const pathSegments = pages
      .slice(0, index + 1)
      .map((p) => dash(p.toLowerCase()));

    // Add admin prefix if we're in admin section
    return isAdminSection
      ? `/admin/${pathSegments.slice(1).join('/')}` // Skip the first segment as it's already in the /admin/ part
      : `/${pathSegments.join('/')}`;
  };

  return (
    <header className="flex h-16 shrink-0 items-center justify-between gap-2">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {pages?.map((page, index) => (
              <Fragment key={page}>
                {index > 0 && (
                  <BreadcrumbSeparator className="hidden md:block" />
                )}
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href={getBreadcrumbHref(page, index)}>
                    {page}
                  </BreadcrumbLink>
                </BreadcrumbItem>
              </Fragment>
            ))}
            {!isEmpty(pages) && (
              <BreadcrumbSeparator className="hidden md:block" />
            )}
            <BreadcrumbItem>
              <BreadcrumbPage>{page}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      {children}
    </header>
  );
};
