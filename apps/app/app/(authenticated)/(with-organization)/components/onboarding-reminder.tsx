'use client';

import { useSession } from '@repo/auth/client';
import { useOnboardingStore } from '@repo/auth/store/onboarding-store';
import { SIGN_OUT_IN_PROGRESS } from '@repo/auth/utils/sign-out-handler';
import { AlertCircle } from '@repo/design-system/components/icons';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export function OnboardingReminder() {
  const router = useRouter();
  const { data: sessionData } = useSession();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isSigningOut, setIsSigningOut] = useState(false);
  const { isComplete, setComplete } = useOnboardingStore();

  // Check if sign-out is in progress
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const signOutInProgress =
        localStorage.getItem(SIGN_OUT_IN_PROGRESS) === 'true';
      setIsSigningOut(signOutInProgress);

      // Clean up the flag if it exists (in case the page was refreshed during sign-out)
      if (signOutInProgress) {
        const cleanup = () => {
          localStorage.removeItem(SIGN_OUT_IN_PROGRESS);
          setIsSigningOut(false);
        };

        // Clean up after a short delay to ensure the sign-out process has time to complete
        const timeoutId = setTimeout(cleanup, 5000);
        return () => clearTimeout(timeoutId);
      }
    }
  }, []);

  // Check if the user has completed onboarding by checking if user has organizer id
  useEffect(() => {
    const checkOnboardingStatus = () => {
      try {
        const onboardingComplete = sessionData?.session?.organizerId != null;
        // Update the client-side store to match the server state
        if (onboardingComplete && !isComplete) {
          setComplete(true);
        }

        setLoading(false);
      } catch (error) {
        log.warn('Failed to check onboarding status', { error });
        setLoading(false);
      }
    };

    // If onboarding is already complete, no need to check again
    if (mounted && !isComplete && !isSigningOut) {
      checkOnboardingStatus();
    }
  }, [sessionData, mounted, isComplete, setComplete, isSigningOut]);

  // Only check the store after mounting to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything during SSR, loading, sign-out, or if onboarding is complete
  if (!mounted || loading || isSigningOut || isComplete) {
    return null;
  }

  return (
    <Alert variant="warning" className="mx-4 mt-4 w-[calc(100%-2rem)]">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Complete your organizer profile</AlertTitle>
      <AlertDescription>
        <div className="flex w-full items-center justify-between">
          <p className="flex-1">
            You haven&apos;t completed your profile setup. This information is
            required to use all features of the platform.
          </p>
          <Button
            variant="warning"
            size="sm"
            className="whitespace-nowrap"
            onClick={() => router.push('/onboarding')}
          >
            Complete Setup
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
