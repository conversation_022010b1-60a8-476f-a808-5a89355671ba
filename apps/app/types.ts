import type { getEventDonations } from '@/app/(authenticated)/(with-organization)/events/[slug]/donations/action';
import type { UserWithOrders } from '@repo/database/types';
import type { getOrganizer } from './app/(authenticated)/(with-organization)/admin/organizers/actions';
import type { getPremiumTier } from './app/(authenticated)/(with-organization)/admin/premium-tiers/actions';
import type {
  getVenue,
  getVenueBySlug,
} from './app/(authenticated)/(with-organization)/admin/venues/actions';
import type { getEvent } from './app/(authenticated)/(with-organization)/events/[slug]/actions';
import type { getOrders } from './app/(authenticated)/(with-organization)/orders/actions';

// Users API response types (shared from packages/database/types.ts)
export type SerializedUsers = UserWithOrders[];
export type SerializedUser = UserWithOrders;

export type SerializedEvent = Awaited<ReturnType<typeof getEvent>>;

export type SerializedDonations = Awaited<ReturnType<typeof getEventDonations>>;
export type SerializedDonation = SerializedDonations[number];

export type SerializedOrders = Awaited<ReturnType<typeof getOrders>>;
export type SerializedOrder = SerializedOrders[number];

export type SerializedPremiumTier = Awaited<ReturnType<typeof getPremiumTier>>;

export type SerializedVenue = Awaited<ReturnType<typeof getVenue>>;
export type SerializedVenueWithEvents = Awaited<
  ReturnType<typeof getVenueBySlug>
>;

export type SerializedOrganizer = Awaited<ReturnType<typeof getOrganizer>>;
export type SerializedOrganizers = SerializedOrganizer[];
