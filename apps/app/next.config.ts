import { env } from '@/env';
import { config, withAnalyzer } from '@repo/next-config';
import { withLogging, withSentry } from '@repo/observability/next-config';
import type { NextConfig } from 'next';

// Start with the base config
let nextConfig: NextConfig = withLogging(config);

// Add configuration for Prisma as an external package
nextConfig.serverExternalPackages = ['@prisma/client'];

if (env.VERCEL) {
  nextConfig = withSentry(nextConfig);
}

if (env.ANALYZE === 'true') {
  nextConfig = withAnalyzer(nextConfig);
}

// ignore linting during build, we do it via pnpm lint instead
nextConfig.eslint = {
  // Warning: This allows production builds to successfully complete even if
  // your project has ESLint errors.
  ignoreDuringBuilds: true,
};

// !! WARN !!
// Dangerously allow production builds to successfully complete even if
// your project has type errors.
// !! WARN !!
nextConfig.typescript = {
  ignoreBuildErrors: true,
};

export default nextConfig;
