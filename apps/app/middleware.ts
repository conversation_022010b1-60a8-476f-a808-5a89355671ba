import {
  noseconeMiddleware,
  noseconeOptions,
  noseconeOptionsWithToolbar,
} from '@repo/security/middleware';
import { auth } from '@repo/auth/server';
import { NextResponse, type NextRequest } from 'next/server';
import { env } from './env';

const securityHeaders = env.FLAGS_SECRET
  ? noseconeMiddleware(noseconeOptionsWithToolbar)
  : noseconeMiddleware(noseconeOptions);

/**
 * Role-based middleware for organizer app
 * Redirects customers to the web application
 */
const handleRoleBasedAccess = async (request: NextRequest) => {
  const pathname = request.nextUrl.pathname;

  // Skip auth check for public routes and auth routes
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/sign-in') ||
    pathname.startsWith('/sign-up') ||
    pathname.startsWith('/thank-you') ||
    pathname.includes('.')
  ) {
    return null;
  }

  try {
    // Check if user is authenticated and get their role
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (session?.user) {
      // If user is a customer, redirect to web app
      if (session.user.role === 'customer') {
        const webUrl = env.NEXT_PUBLIC_WEB_URL || 'https://www.ticketcare.my';
        return NextResponse.redirect(new URL('/account', webUrl));
      }

      // If user doesn't have organizer access, redirect to web app
      if (
        !session.session?.organizerId &&
        session.user.role !== 'super-admin' &&
        session.user.role !== 'admin'
      ) {
        const webUrl = env.NEXT_PUBLIC_WEB_URL || 'https://www.ticketcare.my';
        return NextResponse.redirect(new URL('/account', webUrl));
      }
    }
  } catch (error) {
    console.error('Error checking user role in middleware:', error);
    // Continue to allow access if there's an error checking the session
  }

  return null;
};

const middleware = async (request: NextRequest) => {
  // Check role-based access first
  const roleResponse = await handleRoleBasedAccess(request);
  if (roleResponse) return roleResponse;

  // Apply security headers
  return securityHeaders();
};

export default middleware;

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
