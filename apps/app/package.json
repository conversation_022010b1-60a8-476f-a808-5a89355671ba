{"name": "app", "private": true, "scripts": {"dev": "next dev -p 3000 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true pnpm build", "test": "NODE_ENV=test vitest run", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@prisma/client": "6.4.1", "@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/design-system": "workspace:*", "@repo/email": "workspace:*", "@repo/feature-flags": "workspace:*", "@repo/next-config": "workspace:*", "@repo/notifications": "workspace:*", "@repo/observability": "workspace:*", "@repo/security": "workspace:*", "@repo/seo": "workspace:*", "@repo/webhooks": "workspace:*", "@sentry/nextjs": "^9.4.0", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-table": "^8.21.2", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "fuse.js": "^7.1.0", "import-in-the-middle": "^1.13.1", "nanoid": "^5.1.5", "next": "15.1.7", "next-themes": "^0.4.4", "radash": "^12.1.0", "react": "19.0.0", "react-dom": "19.0.0", "require-in-the-middle": "^7.5.2", "swr": "^2.3.3", "usehooks-ts": "^3.1.1", "zod": "^3.24.2"}, "devDependencies": {"@repo/testing": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "jsdom": "^26.0.0", "tailwindcss": "^4.0.12", "typescript": "^5.8.2", "vitest": "^3.0.7"}}