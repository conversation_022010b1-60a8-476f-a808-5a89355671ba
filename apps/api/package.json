{"name": "api", "private": true, "scripts": {"dev": "concurrently \"pnpm:next\" \"pnpm:stripe\"", "next": "next dev -p 3002 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true pnpm build", "test": "NODE_ENV=test vitest run", "stripe": "stripe listen --forward-to localhost:3002/webhooks/stripe", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/design-system": "workspace:*", "@repo/email": "workspace:*", "@repo/next-config": "workspace:*", "@repo/observability": "workspace:*", "@repo/payments": "workspace:*", "@repo/rate-limit": "workspace:*", "@repo/testing": "workspace:*", "@repo/storage": "workspace:*", "@sentry/nextjs": "^9.4.0", "@t3-oss/env-nextjs": "^0.12.0", "next": "15.1.7", "react": "19.0.0", "react-dom": "19.0.0", "svix": "^1.63.0", "vitest": "^3.0.7", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "concurrently": "^9.1.2", "typescript": "^5.8.2"}}