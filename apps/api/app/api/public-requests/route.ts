import { isSuperAdmin, toAuthResult } from '@repo/auth';
import { auth } from '@repo/auth/server';
import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const querySchema = z.object({
  query: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
});

/**
 * GET /api/public-requests
 * Get paginated public requests for admin review
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!isSuperAdmin(toAuthResult(session))) {
      return NextResponse.json(
        { error: 'Unauthorized: Only super admins can access public requests' },
        { status: 403 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      query: searchParams.get('query') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
    };

    const { query, page, limit } = querySchema.parse(queryParams);

    // Build where clause for search
    const where = query
      ? {
          OR: [
            { title: { contains: query, mode: 'insensitive' as const } },
            { description: { contains: query, mode: 'insensitive' as const } },
            { name: { contains: query, mode: 'insensitive' as const } },
            { email: { contains: query, mode: 'insensitive' as const } },
          ],
        }
      : {};

    // Get total count for pagination
    const totalCount = await database.publicRequest.count({ where });

    // Get paginated requests
    const requests = await database.publicRequest.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
      include: {
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        convertedToTask: {
          select: {
            id: true,
            title: true,
            status: true,
          },
        },
      },
    });

    const totalPages = Math.ceil(totalCount / limit);

    log.info('Public requests fetched via API', {
      count: requests.length,
      totalCount,
      page,
      limit,
      query,
    });

    return NextResponse.json({
      data: requests,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    });
  } catch (error) {
    log.error('Error fetching public requests via API', { error });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
