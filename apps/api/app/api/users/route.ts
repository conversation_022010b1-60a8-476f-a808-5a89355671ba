import { type AuthResult, withOrganizer } from '@/app/lib/api';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import {
  MAX_ROWS,
  type Prisma,
  userWithOrdersSelect,
} from '@repo/database/types';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('User search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const eventId = searchParams.get('eventId');
  const query = searchParams.get('query') ?? '';
  const page = Number(searchParams.get('page') ?? '1');
  const limit = Number(searchParams.get('limit') ?? MAX_ROWS);
  const skip = (page - 1) * limit;

  // Get organizer filter using permission utilities
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to user data
  if (organizerId === null) {
    return NextResponse.json(
      {
        data: [],
        pagination: {
          total: 0,
          pageCount: 0,
          currentPage: page,
        },
      },
      {
        status: 200,
      }
    );
  }

  const whereCondition: Prisma.UserWhereInput = {
    OR: [
      {
        name: {
          contains: query,
          mode: 'insensitive',
        },
      },
      {
        email: {
          contains: query,
          mode: 'insensitive',
        },
      },
      {
        phone: {
          contains: query,
          mode: 'insensitive',
        },
      },
    ],
    AND: [
      {
        orders: {
          some: {
            ...(eventId ? { eventId } : {}),
            event: {
              organizerId: organizerId ?? undefined,
            },
          },
        },
      },
    ],
  };

  const [users, _total] = await Promise.all([
    database.user.findMany({
      where: whereCondition,
      select: userWithOrdersSelect(organizerId),
      skip,
      take: limit,
    }),
    database.user.count({
      where: whereCondition,
    }),
  ]);

  return NextResponse.json(
    {
      data: serializePrisma(users),
      pagination: {
        total: _total,
        pageCount: Math.ceil(_total / MAX_ROWS),
        currentPage: page,
      },
    },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
