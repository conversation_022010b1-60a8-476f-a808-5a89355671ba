import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('Inventory search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const ticketTypeId = searchParams.get('ticketTypeId');

  // Get organizer filter using permission utilities
  // Super admins can see all inventory, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to inventory data
  if (organizerId === null) {
    return NextResponse.json(
      { data: [] },
      {
        status: 200,
      }
    );
  }

  const inventories = await database.inventory.findMany({
    where: {
      ticketTypeId: ticketTypeId ? ticketTypeId : undefined,
      ticketType: {
        event: {
          organizerId: organizerId ?? undefined,
        },
      },
    },
    orderBy: {
      timeSlot: {
        startTime: 'asc',
      },
    },
    select: {
      id: true,
      quantity: true,
      timeSlot: {
        select: {
          id: true,
          startTime: true,
          endTime: true,
          doorsOpen: true,
          _count: {
            select: {
              tickets: {
                where: {
                  ticketTypeId: ticketTypeId ? ticketTypeId : undefined,
                },
              },
            },
          },
        },
      },
    },
  });

  return NextResponse.json(
    { data: serializePrisma(inventories) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
