import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('Venue search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');

  // Get organizer filter using permission utilities
  // Super admins can see all venues, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to venues data
  if (organizerId === null) {
    return NextResponse.json(
      { data: [] },
      {
        status: 200,
      }
    );
  }

  const venues = await database.venue.findMany({
    where: {
      name: {
        contains: query ?? '',
        mode: 'insensitive',
      },
      events: {
        some: {
          organizerId: organizerId ?? undefined,
        },
      },
    },
    select: {
      id: true,
      name: true,
    },
  });

  return NextResponse.json(
    { data: serializePrisma(venues) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
