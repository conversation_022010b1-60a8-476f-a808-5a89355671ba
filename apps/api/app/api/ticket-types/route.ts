import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('Ticket type search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const eventId = searchParams.get('eventId');
  const timeSlotId = searchParams.get('timeSlotId');

  // Get organizer filter using permission utilities
  // Super admins can see all ticket types, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to ticket types data
  if (organizerId === null) {
    return NextResponse.json(
      { data: [] },
      {
        status: 200,
      }
    );
  }

  const ticketTypes = await database.ticketType.findMany({
    where: {
      name: {
        contains: query ?? '',
        mode: 'insensitive',
      },
      eventId: eventId ? eventId : undefined,
      event: {
        organizerId: organizerId ?? undefined,
      },
      inventory: {
        some: {
          timeSlotId: timeSlotId ? timeSlotId : undefined,
        },
      },
    },
    select: {
      id: true,
      name: true,
    },
  });

  return NextResponse.json(
    { data: serializePrisma(ticketTypes) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
