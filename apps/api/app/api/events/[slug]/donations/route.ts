import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { MAX_ROWS, type Prisma } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (
  request: NextRequest,
  authResult: AuthResult,
  context: { params: Promise<{ slug: string }> }
) => {
  log.info('Donation search request received', {
    organizerId: authResult.organizerId,
  });

  const { slug } = await context.params;
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query') ?? '';
  const page = Number(searchParams.get('page') ?? '1');
  const limit = Number(searchParams.get('limit') ?? MAX_ROWS);
  const skip = (page - 1) * limit;

  if (!slug) {
    return NextResponse.json(
      {
        error: 'Event slug is required',
      },
      { status: 400 }
    );
  }

  // Get organizer filter using permission utilities
  // Super admins can see all donations, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to donations data
  if (organizerId === null) {
    return NextResponse.json(
      {
        data: [],
        pagination: {
          total: 0,
          pageCount: 0,
          currentPage: page,
        },
      },
      {
        status: 200,
      }
    );
  }

  try {
    // First, find the event by slug and ensure it belongs to the organizer
    const event = await database.event.findFirst({
      where: {
        slug,
        organizerId: organizerId ?? undefined,
      },
      select: { id: true },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    const eventId = event.id;

    const whereCondition: Prisma.EventDonationWhereInput = {
      eventId,
      OR: [
        // Search by name
        {
          name: {
            contains: query,
            mode: 'insensitive',
          },
        },
        // Search by email
        {
          email: {
            contains: query,
            mode: 'insensitive',
          },
        },
        // Search by transaction ID
        {
          transactionId: {
            contains: query,
            mode: 'insensitive',
          },
        },
      ],
    };

    const [donations, _total] = await Promise.all([
      database.eventDonation.findMany({
        where: whereCondition,
        select: {
          id: true,
          donationModuleId: true,
          eventId: true,
          name: true,
          email: true,
          companyName: true,
          companyLogo: true,
          message: true,
          amount: true,
          transactionId: true,
          paymentMethod: true,
          status: true,
          paymentStatus: true,
          donatedAt: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          donatedAt: 'desc',
        },
        skip,
        take: limit,
      }),
      database.eventDonation.count({
        where: whereCondition,
      }),
    ]);

    return NextResponse.json(
      {
        data: serializePrisma(donations),
        pagination: {
          total: _total,
          pageCount: Math.ceil(_total / MAX_ROWS),
          currentPage: page,
        },
      },
      {
        status: 200,
      }
    );
  } catch (error) {
    log.error('Failed to fetch donations', { error });
    return NextResponse.json(
      { error: 'Failed to fetch donations' },
      { status: 500 }
    );
  }
};

export const OPTIONS = withOrganizer<{ params: Promise<{ slug: string }> }>(
  handler
);
export const GET = withOrganizer<{ params: Promise<{ slug: string }> }>(
  handler
);
