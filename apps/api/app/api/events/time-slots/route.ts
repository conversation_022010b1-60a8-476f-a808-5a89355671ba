import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('Time slot search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const eventId = searchParams.get('eventId');

  // Get organizer filter using permission utilities
  // Super admins can see all time slots, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to time slots data
  if (organizerId === null) {
    return NextResponse.json(
      { data: [] },
      {
        status: 200,
      }
    );
  }

  // If eventId is provided, find all time slots for that event
  // This requires joining through eventDates since time slots are linked to event dates
  const timeSlots = await database.timeSlot.findMany({
    where: {
      eventDate: eventId
        ? {
            eventId: eventId,
            event: {
              organizerId: organizerId ?? undefined,
            },
          }
        : {
            event: {
              organizerId: organizerId ?? undefined,
            },
          },
    },
    select: {
      id: true,
      startTime: true,
      endTime: true,
      doorsOpen: true,
      eventDate: {
        select: {
          id: true,
          date: true,
          event: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      },
    },
    orderBy: [
      {
        eventDate: {
          date: 'asc',
        },
      },
      {
        startTime: 'asc',
      },
    ],
  });

  return NextResponse.json(
    { data: serializePrisma(timeSlots) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
