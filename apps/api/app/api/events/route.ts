import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { MAX_ROWS, type Prisma } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('Event search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const page = Number(searchParams.get('page') ?? '1');
  const limit = Number(searchParams.get('limit') ?? MAX_ROWS);
  const skip = (page - 1) * limit;

  // Get organizer filter using permission utilities
  // Super admins can see all events, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to events data
  if (organizerId === null) {
    return NextResponse.json(
      {
        data: [],
        pagination: {
          total: 0,
          pageCount: 0,
          currentPage: page,
        },
      },
      {
        status: 200,
      }
    );
  }

  const whereCondition: Prisma.EventWhereInput = {
    status: {
      in: ['published', 'sold_out'],
    },
    OR: [
      {
        title: {
          contains: query ?? '',
          mode: 'insensitive',
        },
      },
    ],
    AND: [
      {
        organizerId: organizerId ?? undefined,
      },
    ],
  };

  const [events, _total] = await Promise.all([
    database.event.findMany({
      where: whereCondition,
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        startTime: 'asc',
      },
      skip,
      take: limit,
    }),
    database.event.count({
      where: whereCondition,
    }),
  ]);

  return NextResponse.json(
    {
      data: serializePrisma(events),
      pagination: {
        total: _total,
        pageCount: Math.ceil(_total / MAX_ROWS),
        currentPage: page,
      },
    },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
