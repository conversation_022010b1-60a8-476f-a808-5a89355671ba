import { withNextCors } from '@/app/lib/api';
import { env } from '@/env';
import { log } from '@repo/observability/log';
import { put } from '@repo/storage';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

async function handler(request: NextRequest) {
  log.info('File upload request received');

  try {
    // Validate the upload key from header
    const uploadKey = request.headers.get('x-api-upload-key');
    const validKey = env.CUSTOM_B2_UPLOAD_KEY;

    // If key is missing or invalid, return 401 Unauthorized
    if (!uploadKey || uploadKey !== validKey) {
      log.warn('Invalid or missing upload key');
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'File is required' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
    ];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Please upload a valid image or PDF file' },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Please upload a file smaller than 5MB' },
        { status: 400 }
      );
    }

    const access =
      (formData.get('access') as 'public' | 'private') || 'private';

    const result = await put(file.name, file, {
      access,
      contentType: file.type,
    });

    return NextResponse.json({
      success: true,
      url: result.url,
      pathname: result.pathname,
    });
  } catch (error) {
    console.error('Failed to upload file:', error);

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

export const OPTIONS = withNextCors(handler);
export const POST = withNextCors(handler);
