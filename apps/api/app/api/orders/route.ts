import { withOrganizer } from '@/app/lib/api';
import { type AuthResult, getOrganizerFilter } from '@repo/auth';
import { database, serializePrisma } from '@repo/database';
import { MAX_ROWS, type Prisma } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest, authResult: AuthResult) => {
  log.info('Order search request received', {
    organizerId: authResult.organizerId,
  });

  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query') ?? '';
  const userId = searchParams.get('userId') ?? undefined;
  const page = Number(searchParams.get('page') ?? '1');
  const limit = Number(searchParams.get('limit') ?? MAX_ROWS);
  const skip = (page - 1) * limit;

  // Get organizer filter using permission utilities
  // Super admins can see all orders, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to orders data
  if (organizerId === null) {
    return NextResponse.json(
      { data: [] },
      {
        status: 200,
      }
    );
  }

  const whereCondition: Prisma.OrderWhereInput = {
    OR: [
      // Search by transaction ID
      {
        id: {
          contains: query,
        },
      },
      // Search by user name or email
      {
        user: {
          name: {
            contains: query,
            mode: 'insensitive',
          },
          email: {
            contains: query,
            mode: 'insensitive',
          },
        },
      },
      // Search by event title
      {
        event: {
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
      },
    ],
    AND: [
      {
        tickets: {
          every: {
            event: {
              organizerId: organizerId ?? undefined,
            },
          },
        },
      },
      // Optional filter by a specific user
      // When userId is undefined it has no effect
      userId ? { userId } : {},
    ],
  };

  const [orders, _total] = await Promise.all([
    database.order.findMany({
      where: whereCondition,
      select: {
        id: true,
        userId: true,
        eventId: true,
        event: true,
        status: true,
        totalAmount: true,
        paymentMethod: true,
        transactionId: true,
        paymentStatus: true,
        orderedAt: true,
        createdAt: true,
        updatedAt: true,
        user: true,
        tickets: {
          include: {
            event: true,
            ticketType: true,
            timeSlot: true,
          },
        },
      },
      orderBy: {
        orderedAt: 'desc',
      },
      skip,
      take: limit,
    }),
    database.order.count({
      where: whereCondition,
    }),
  ]);

  return NextResponse.json(
    {
      data: serializePrisma(orders),
      pagination: {
        total: _total,
        pageCount: Math.ceil(_total / MAX_ROWS),
        currentPage: page,
      },
    },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
