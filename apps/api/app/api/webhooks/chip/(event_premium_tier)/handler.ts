import { analytics } from '@repo/analytics/posthog/server';
import { database } from '@repo/database';
import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import type { ChipPaymentResponse } from '@repo/payments/chip';
import { revalidatePath } from 'next/cache';

/**
 * Handle premium tier upgrade payment
 * @param data Payment data from webhook
 */
export const handlePremiumUpgradePayment = async (
  data: ChipPaymentResponse
) => {
  if (!data.id) {
    log.warn('Premium upgrade payment missing ID');
    return;
  }

  try {
    // Find the premium upgrade record using the Chip payment ID as transaction ID
    const eventPremiumUpgrade = await database.eventPremiumUpgrade.findFirst({
      where: {
        transactionId: data.id, // The transactionId field stores the Chip payment ID
      },
      include: {
        event: true,
        organizer: true,
        premiumTier: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!eventPremiumUpgrade) {
      log.warn(`Premium upgrade not found for payment ID: ${data.id}`);
      return;
    }

    // Update the premium upgrade status
    await database.eventPremiumUpgrade.update({
      where: {
        id: eventPremiumUpgrade.id,
      },
      data: {
        status: 'completed',
        paymentStatus: 'paid',
      },
    });

    // Update the event with premium tier & ticket limits
    await database.event.update({
      where: {
        id: eventPremiumUpgrade.eventId,
      },
      data: {
        isPremiumEvent: true,
        premiumTierId: eventPremiumUpgrade.premiumTierId,
        maxTicketsPerEvent: eventPremiumUpgrade.premiumTier?.maxTicketsPerEvent,
      },
    });

    log.info(
      `Event ${eventPremiumUpgrade.eventId} upgraded to premium tier ${eventPremiumUpgrade.premiumTierId}`
    );

    // Revalidate the event page
    if (eventPremiumUpgrade.event?.slug) {
      revalidatePath(`/events/${eventPremiumUpgrade.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/admin/premium-tiers');

    // Track the event in analytics
    analytics.capture({
      event: 'event_upgraded_to_premium',
      distinctId: eventPremiumUpgrade.organizerId,
      properties: {
        eventId: eventPremiumUpgrade.eventId,
        premiumTierId: eventPremiumUpgrade.premiumTierId,
        paymentMethod: 'chip',
        amount: eventPremiumUpgrade.amount,
      },
    });
  } catch (error) {
    const message = parseError(error);
    log.error(`Error processing premium upgrade payment: ${message}`, {
      paymentId: data.id,
      error,
    });
  }
};

/**
 * Handle premium tier upgrade payment failed
 * @param data Payment data from webhook
 */
export const handlePremiumUpgradeFailed = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Premium upgrade payment missing ID');
    return;
  }

  try {
    // Find the premium upgrade record using the Chip payment ID as transaction ID
    const eventPremiumUpgrade = await database.eventPremiumUpgrade.findFirst({
      where: {
        transactionId: data.id,
      },
      include: {
        event: true,
        organizer: true,
        premiumTier: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!eventPremiumUpgrade) {
      log.warn(`Premium upgrade not found for failed payment ID: ${data.id}`);
      return;
    }

    // Update the premium upgrade status to failed
    await database.eventPremiumUpgrade.update({
      where: {
        id: eventPremiumUpgrade.id,
      },
      data: {
        status: 'failed',
        paymentStatus: 'pending',
      },
    });

    log.info(
      `Premium upgrade payment failed for event ${eventPremiumUpgrade.eventId}`
    );

    // Revalidate the event page
    if (eventPremiumUpgrade.event?.slug) {
      revalidatePath(`/events/${eventPremiumUpgrade.event.slug}`);
    }

    // Track the event in analytics
    analytics.capture({
      event: 'event_premium_upgrade_failed',
      distinctId: eventPremiumUpgrade.organizerId,
      properties: {
        eventId: eventPremiumUpgrade.eventId,
        premiumTierId: eventPremiumUpgrade.premiumTierId,
        paymentMethod: 'chip',
        amount: eventPremiumUpgrade.amount,
        paymentId: data.id,
      },
    });
  } catch (error) {
    const message = parseError(error);
    log.error(`Error processing failed premium upgrade payment: ${message}`, {
      paymentId: data.id,
      error,
    });
  }
};

/**
 * Handle premium tier upgrade payment cancelled
 * @param data Payment data from webhook
 */
export const handlePremiumUpgradeCancelled = async (
  data: ChipPaymentResponse
) => {
  if (!data.id) {
    log.warn('Premium upgrade payment missing ID');
    return;
  }

  try {
    // Find the premium upgrade record using the Chip payment ID as transaction ID
    const eventPremiumUpgrade = await database.eventPremiumUpgrade.findFirst({
      where: {
        transactionId: data.id,
      },
      include: {
        event: true,
        organizer: true,
        premiumTier: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!eventPremiumUpgrade) {
      log.warn(
        `Premium upgrade not found for cancelled payment ID: ${data.id}`
      );
      return;
    }

    // Update the premium upgrade status to cancelled
    await database.eventPremiumUpgrade.update({
      where: {
        id: eventPremiumUpgrade.id,
      },
      data: {
        status: 'cancelled',
        paymentStatus: 'cancelled',
      },
    });

    log.info(
      `Premium upgrade payment cancelled for event ${eventPremiumUpgrade.eventId}`
    );

    // Revalidate the event page
    if (eventPremiumUpgrade.event?.slug) {
      revalidatePath(`/events/${eventPremiumUpgrade.event.slug}`);
    }

    // Track the event in analytics
    analytics.capture({
      event: 'event_premium_upgrade_cancelled',
      distinctId: eventPremiumUpgrade.organizerId,
      properties: {
        eventId: eventPremiumUpgrade.eventId,
        premiumTierId: eventPremiumUpgrade.premiumTierId,
        paymentMethod: 'chip',
        amount: eventPremiumUpgrade.amount,
        paymentId: data.id,
      },
    });
  } catch (error) {
    const message = parseError(error);
    log.error(
      `Error processing cancelled premium upgrade payment: ${message}`,
      {
        paymentId: data.id,
        error,
      }
    );
  }
};
