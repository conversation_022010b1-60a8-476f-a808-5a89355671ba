import { analytics } from '@repo/analytics/posthog/server';
import { database } from '@repo/database';
import { CartStatus, OrderStatus } from '@repo/database/types';
import { sendOrderConfirmationEmail } from '@repo/email/utils/order-confirmation';
import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import type { ChipPaymentResponse } from '@repo/payments/chip';
import { revalidatePath } from 'next/cache';

/**
 * Get order from payment transaction ID
 * @param transactionId Payment transaction ID from webhook
 * @returns Order object if found
 */
export const getOrderFromTransactionId = async (transactionId: string) => {
  try {
    if (!transactionId) {
      log.warn('Empty transactionId provided to getOrderFromTransactionId');
      return null;
    }

    // Find order directly by transactionId
    const order = await database.order.findFirst({
      where: {
        transactionId,
        status: OrderStatus.hold,
      },
      include: {
        user: true,
        event: true,
        tickets: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    return order;
  } catch (error) {
    log.error('Error fetching order from transactionId', {
      transactionId,
      error: parseError(error),
    });
    return null;
  }
};

/**
 * Handle ticket purchase payment
 * @param data Payment data from webhook
 */
export const handleTicketPayment = async (data: ChipPaymentResponse) => {
  const order = await getOrderFromTransactionId(data.id);

  if (!order) {
    log.warn(`Order not found for payment ID: ${data.id}`);
    return;
  }

  // Convert amount from cents to actual currency
  const amount = data.purchase?.total ? data.purchase.total / 100 : 0;

  const paymentAttempt = data.transaction_data?.attempts?.at(-1);
  const paymentMethod = paymentAttempt?.payment_method || 'unknown';

  try {
    // Start a transaction to ensure all updates are atomic
    await database.$transaction(async (tx) => {
      // 1. Update order status
      await tx.order.update({
        where: {
          id: order.id,
        },
        data: {
          status: 'completed',
          paymentStatus: 'paid',
          paymentMethod: `chip-${paymentMethod}`,
        },
      });

      log.info(`Order ${order.id} status updated to completed`);

      // 2. Update ticket status to purchased
      await tx.ticket.updateMany({
        where: {
          orderId: order.id,
        },
        data: {
          status: 'purchased',
          purchaseDate: new Date(), // Set purchase date to now
        },
      });

      log.info(`Tickets for order ${order.id} updated to purchased status`);

      // 3. Update cart status if cart ID is available
      if (order.cartId) {
        // Update cart status to 'converted'
        await tx.cart.update({
          where: { id: order.cartId, status: CartStatus.active },
          data: {
            userId: order.userId, // bind user ID from order to cart
            status: CartStatus.converted,
            // Extend expiration time to avoid any issues
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          },
        });

        log.info(
          `Cart ${order.cartId} marked as converted after successful payment`
        );
      }
    });

    // 4. Calculate total tickets in this order
    const totalTickets = order.tickets?.length || 0;

    // 5. Update event ticket sales count
    if (order.event) {
      await database.event.update({
        where: {
          id: order.event.id,
        },
        data: {
          ticketsSold: {
            increment: totalTickets,
          },
        },
      });

      // Log based on event type
      if (order.event.isPremiumEvent) {
        log.info(
          `Processing premium event ticket sales for event ${order.event.id}`
        );
      } else {
        log.info(
          `Processing standard event ticket sales for event ${order.event.id}`
        );
      }
    }

    // 6. Revalidate relevant pages to update UI
    if (order.event?.slug) {
      revalidatePath(`/events/${order.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/orders');

    // 7. Send order confirmation email
    try {
      const emailResponse = await sendOrderConfirmationEmail(order.id);
      if (emailResponse.success) {
        log.info(
          `Order confirmation email sent successfully for order ${order.id}`
        );
      } else {
        log.warn(
          `Failed to send order confirmation email for order ${order.id}`
        );
      }
    } catch (error) {
      // Handle unexpected errors from the email function
      const errorMessage = parseError(error);
      log.error(`Error sending order confirmation email: ${errorMessage}`, {
        error,
        orderId: order.id,
      });
      // Continue processing - don't fail the webhook due to email issues
    }

    // 8. Track analytics
    analytics.capture({
      event: 'Payment Completed',
      distinctId: order.user.id,
      properties: {
        paymentId: data.id,
        orderId: order.id,
        eventId: order.event?.id,
        amount: amount,
        paymentMethod: `chip-${paymentMethod}`,
        currency: data.purchase?.currency || 'MYR',
        isPremiumEvent: order.event?.isPremiumEvent || false,
        ticketCount: order.tickets?.length || 0,
      },
    });

    log.info(`Payment completed for order ${order.id}, payment ID: ${data.id}`);
  } catch (error) {
    const errorMessage = parseError(error);
    log.error(`Error processing successful payment: ${errorMessage}`, {
      orderId: order.id,
      paymentId: data.id,
      error,
    });

    // Even if there's an error in processing, we should still try to update the order status
    // to avoid leaving it in a pending state
    try {
      await database.order.update({
        where: { id: order.id },
        data: {
          paymentStatus: 'paid',
          status: 'completed',
        },
      });
    } catch (secondaryError) {
      log.error(
        `Failed to update order status in error recovery: ${parseError(secondaryError)}`
      );
    }
  }
};

/**
 * Handle ticket purchase payment failed
 * @param data Payment data from webhook
 */
export const handleTicketPaymentFailed = async (data: ChipPaymentResponse) => {
  const order = await getOrderFromTransactionId(data.id);

  if (!order) {
    log.warn(`Order not found for failed payment ID: ${data.id}`);
    return;
  }

  try {
    // Start a transaction to ensure all updates are atomic
    await database.$transaction(async (tx) => {
      // 1. Update order status
      await tx.order.update({
        where: {
          id: order.id,
        },
        data: {
          paymentStatus: 'pending',
          // Keep the order status as 'hold' to allow retry
          status: 'hold',
        },
      });

      log.info(
        `Order ${order.id} status updated to pending payment after failure`
      );

      // 2. Update ticket status to pending
      await tx.ticket.updateMany({
        where: {
          orderId: order.id,
        },
        data: {
          status: 'pending',
        },
      });

      log.info(`Tickets for order ${order.id} updated to pending status`);

      // 3. Update cart status if cart ID is available
      const reference = data.reference;
      if (reference?.startsWith('Cart #')) {
        // Extract cart ID by removing the 'Cart #' prefix (6 characters)
        const cartId = reference.substring(6);

        // Keep cart as active to allow retry
        await tx.cart.update({
          where: { id: cartId },
          data: {
            userId: order.userId, // bind user ID from order to cart
            status: CartStatus.active,
            // Extend expiration time to allow retry
            expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
          },
        });

        log.info(
          `Cart ${cartId} kept active after payment failure to allow retry`
        );
      }
    });

    // 4. Revalidate relevant pages to update UI
    if (order.event?.slug) {
      revalidatePath(`/events/${order.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/orders');

    // 5. Track analytics
    const paymentAttempt = data.transaction_data?.attempts?.at(-1);
    const paymentMethod = paymentAttempt?.payment_method || 'unknown';
    const errorDetails = paymentAttempt?.error || {
      code: 'unknown',
      message: 'Unknown error',
    };

    analytics.capture({
      event: 'Payment Failed',
      distinctId: order.user.id,
      properties: {
        paymentId: data.id,
        orderId: order.id,
        eventId: order.event?.id,
        amount: data.purchase?.total ? data.purchase.total / 100 : 0,
        paymentMethod: `chip-${paymentMethod}`,
        errorCode: errorDetails.code,
        errorMessage: errorDetails.message,
        isPremiumEvent: order.event?.isPremiumEvent || false,
      },
    });

    log.warn(`Payment failed for order ${order.id}, payment ID: ${data.id}`, {
      errorCode: errorDetails.code,
      errorMessage: errorDetails.message,
    });
  } catch (error) {
    const errorMessage = parseError(error);
    log.error(`Error processing failed payment: ${errorMessage}`, {
      orderId: order.id,
      paymentId: data.id,
      error,
    });
  }
};

/**
 * Handle ticket purchase payment cancelled
 * @param data Payment data from webhook
 */
export const handleTicketPaymentCancelled = async (
  data: ChipPaymentResponse
) => {
  const order = await getOrderFromTransactionId(data.id);

  if (!order) {
    log.warn(`Order not found for canceled payment ID: ${data.id}`);
    return;
  }

  try {
    // Start a transaction to ensure all updates are atomic
    await database.$transaction(async (tx) => {
      // 1. Update order status
      await tx.order.update({
        where: {
          id: order.id,
        },
        data: {
          paymentStatus: 'cancelled',
          status: 'cancelled', // Update order status to cancelled
        },
      });

      log.info(`Order ${order.id} status updated to cancelled`);

      // 2. Update ticket status to cancelled
      await tx.ticket.updateMany({
        where: {
          orderId: order.id,
        },
        data: {
          status: 'cancelled',
        },
      });

      log.info(`Tickets for order ${order.id} updated to cancelled status`);

      // 3. Update cart status if cart ID is available
      const reference = data.reference;
      if (reference?.startsWith('Cart #')) {
        // Extract cart ID by removing the 'Cart #' prefix (6 characters)
        const cartId = reference.substring(6);

        // Mark cart as abandoned
        await tx.cart.update({
          where: { id: cartId },
          data: {
            userId: order.userId, // bind user ID from order to cart
            status: CartStatus.abandoned,
          },
        });

        log.info(
          `Cart ${cartId} marked as abandoned after payment cancellation`
        );
      }

      // 4. Restore inventory for the cancelled tickets
      if (order.tickets && order.tickets.length > 0) {
        // Group tickets by timeSlot and ticketType to update inventory efficiently
        const inventoryUpdates = new Map<string, number>();

        for (const ticket of order.tickets) {
          const key = `${ticket.timeSlotId}:${ticket.ticketTypeId}`;
          const currentCount = inventoryUpdates.get(key) || 0;
          inventoryUpdates.set(key, currentCount + 1);
        }

        // Update inventory for each timeSlot and ticketType combination
        for (const [key, count] of inventoryUpdates.entries()) {
          const [timeSlotId, ticketTypeId] = key.split(':');

          // Find the inventory record
          const inventory = await tx.inventory.findFirst({
            where: {
              timeSlotId,
              ticketTypeId,
            },
          });

          if (inventory) {
            // Restore the inventory count
            await tx.inventory.update({
              where: { id: inventory.id },
              data: {
                quantity: {
                  increment: count,
                },
              },
            });

            log.info(
              `Restored ${count} tickets to inventory for timeSlot ${timeSlotId} and ticketType ${ticketTypeId}`
            );
          }
        }
      }
    });

    // 5. Revalidate relevant pages to update UI
    if (order.event?.slug) {
      revalidatePath(`/events/${order.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/orders');

    // 6. Track analytics
    const paymentAttempt = data.transaction_data?.attempts?.at(-1);
    const paymentMethod = paymentAttempt?.payment_method || 'unknown';

    analytics.capture({
      event: 'Payment Canceled',
      distinctId: order.user.id,
      properties: {
        paymentId: data.id,
        orderId: order.id,
        eventId: order.event?.id,
        amount: data.purchase?.total ? data.purchase.total / 100 : 0,
        paymentMethod: `chip-${paymentMethod}`,
        isPremiumEvent: order.event?.isPremiumEvent || false,
        ticketCount: order.tickets?.length || 0,
      },
    });

    log.info(`Payment canceled for order ${order.id}, payment ID: ${data.id}`);
  } catch (error) {
    const errorMessage = parseError(error);
    log.error(`Error processing canceled payment: ${errorMessage}`, {
      orderId: order.id,
      paymentId: data.id,
      error,
    });

    // Even if there's an error in processing, we should still try to update the order status
    try {
      await database.order.update({
        where: { id: order.id },
        data: {
          paymentStatus: 'cancelled',
          status: 'cancelled',
        },
      });
    } catch (secondaryError) {
      log.error(
        `Failed to update order status in error recovery: ${parseError(secondaryError)}`
      );
    }
  }
};
