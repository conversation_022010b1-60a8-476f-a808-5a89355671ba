import { analytics } from '@repo/analytics/posthog/server';
import { database } from '@repo/database';
import { DonationStatus, PaymentStatus } from '@repo/database/types';
import { sendDonationConfirmationEmail } from '@repo/email/utils/donation-confirmation';
import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import type { ChipPaymentResponse } from '@repo/payments/chip';
import { revalidatePath } from 'next/cache';

/**
 * Get donation from payment transaction ID
 * @param transactionId Payment transaction ID from webhook
 * @returns Donation object if found
 */
export const getDonationFromTransactionId = async (transactionId: string) => {
  try {
    if (!transactionId) {
      log.warn('Empty transactionId provided to getDonationFromTransactionId');
      return null;
    }

    // Find donation directly by transactionId
    const donation = await database.eventDonation.findFirst({
      where: {
        transactionId,
        status: DonationStatus.pending,
        paymentStatus: PaymentStatus.pending,
      },
      include: {
        event: {
          select: {
            id: true,
            slug: true,
            title: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return donation;
  } catch (error) {
    log.error('Error fetching donation from transactionId', {
      transactionId,
      error: parseError(error),
    });
    return null;
  }
};

/**
 * Handle donation payment
 * @param data Payment data from webhook
 */
export const handleDonationPayment = async (data: ChipPaymentResponse) => {
  const donation = await getDonationFromTransactionId(data.id);

  if (!donation) {
    log.warn(`Donation not found for payment ID: ${data.id}`);
    return;
  }

  // Convert amount from cents to actual currency
  const amount = data.purchase?.total ? data.purchase.total / 100 : 0;

  const paymentAttempt = data.transaction_data?.attempts?.at(-1);
  const paymentMethod = paymentAttempt?.payment_method || 'unknown';

  try {
    // Start a transaction to ensure all updates are atomic
    await database.$transaction(async (tx) => {
      // 1. Update donation status
      await tx.eventDonation.update({
        where: {
          id: donation.id,
        },
        data: {
          status: DonationStatus.completed,
          paymentStatus: PaymentStatus.paid,
          paymentMethod: `chip-${paymentMethod}`,
          donatedAt: new Date(),
        },
      });

      log.info(`Donation ${donation.id} status updated to completed`);
    });

    // 2. Revalidate relevant pages to update UI
    if (donation.event?.slug) {
      revalidatePath(`/events/${donation.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/donations');

    // 3. Send donation confirmation email (if implemented)
    try {
      const emailResponse = await sendDonationConfirmationEmail(donation.id);
      if (emailResponse.success) {
        log.info(
          `Donation confirmation email sent successfully for donation ${donation.id}`
        );
      } else {
        log.warn(
          `Failed to send donation confirmation email for donation ${donation.id}`
        );
      }
    } catch (error) {
      // Handle unexpected errors from the email function
      const errorMessage = parseError(error);
      log.error(`Error sending donation confirmation email: ${errorMessage}`, {
        error,
        donationId: donation.id,
      });
      // Continue processing - don't fail the webhook due to email issues
    }

    // 4. Track analytics
    analytics.capture({
      event: 'Donation Completed',
      distinctId: donation.email, // Use email as distinct ID since we don't have a user ID
      properties: {
        paymentId: data.id,
        donationId: donation.id,
        eventId: donation.eventId,
        amount: amount,
        paymentMethod: `chip-${paymentMethod}`,
        currency: data.purchase?.currency || 'MYR',
        donorName: donation.name,
        donorEmail: donation.email,
      },
    });

    log.info(
      `Payment completed for donation ${donation.id}, payment ID: ${data.id}`
    );
  } catch (error) {
    const errorMessage = parseError(error);
    log.error(`Error processing successful donation payment: ${errorMessage}`, {
      donationId: donation.id,
      paymentId: data.id,
      error,
    });

    // Even if there's an error in processing, we should still try to update the donation status
    try {
      await database.eventDonation.update({
        where: { id: donation.id },
        data: {
          paymentStatus: PaymentStatus.paid,
          status: DonationStatus.completed,
        },
      });
    } catch (secondaryError) {
      log.error(
        `Failed to update donation status in error recovery: ${parseError(secondaryError)}`
      );
    }
  }
};

/**
 * Handle donation payment failed
 * @param data Payment data from webhook
 */
export const handleDonationPaymentFailed = async (
  data: ChipPaymentResponse
) => {
  const donation = await getDonationFromTransactionId(data.id);

  if (!donation) {
    log.warn(`Donation not found for payment ID: ${data.id}`);
    return;
  }

  try {
    // Update donation status to failed
    await database.eventDonation.update({
      where: {
        id: donation.id,
      },
      data: {
        status: DonationStatus.cancelled, // Using cancelled as there's no 'failed' in DonationStatus
        paymentStatus: PaymentStatus.cancelled, // Using cancelled as there's no 'failed' in PaymentStatus
      },
    });

    log.info(`Donation ${donation.id} status updated to failed`);

    // Revalidate relevant pages to update UI
    if (donation.event?.slug) {
      revalidatePath(`/events/${donation.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/donations');

    // Track analytics
    analytics.capture({
      event: 'Donation Failed',
      distinctId: donation.email,
      properties: {
        paymentId: data.id,
        donationId: donation.id,
        eventId: donation.eventId,
        amount: Number(donation.amount),
        donorName: donation.name,
        donorEmail: donation.email,
      },
    });

    log.info(
      `Payment failed for donation ${donation.id}, payment ID: ${data.id}`
    );
  } catch (error) {
    const errorMessage = parseError(error);
    log.error(`Error processing failed donation payment: ${errorMessage}`, {
      donationId: donation.id,
      paymentId: data.id,
      error,
    });
  }
};

/**
 * Handle donation payment cancelled
 * @param data Payment data from webhook
 */
export const handleDonationPaymentCancelled = async (
  data: ChipPaymentResponse
) => {
  const donation = await getDonationFromTransactionId(data.id);

  if (!donation) {
    log.warn(`Donation not found for payment ID: ${data.id}`);
    return;
  }

  try {
    // Update donation status to cancelled
    await database.eventDonation.update({
      where: {
        id: donation.id,
      },
      data: {
        status: DonationStatus.cancelled,
        paymentStatus: PaymentStatus.cancelled,
      },
    });

    log.info(`Donation ${donation.id} status updated to cancelled`);

    // Revalidate relevant pages to update UI
    if (donation.event?.slug) {
      revalidatePath(`/events/${donation.event.slug}`);
    }
    revalidatePath('/events');
    revalidatePath('/donations');

    // Track analytics
    analytics.capture({
      event: 'Donation Cancelled',
      distinctId: donation.email,
      properties: {
        paymentId: data.id,
        donationId: donation.id,
        eventId: donation.eventId,
        amount: Number(donation.amount),
        donorName: donation.name,
        donorEmail: donation.email,
      },
    });

    log.info(
      `Payment cancelled for donation ${donation.id}, payment ID: ${data.id}`
    );
  } catch (error) {
    const errorMessage = parseError(error);
    log.error(`Error processing cancelled donation payment: ${errorMessage}`, {
      donationId: donation.id,
      paymentId: data.id,
      error,
    });
  }
};
