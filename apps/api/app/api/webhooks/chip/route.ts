import { database } from '@repo/database';
import CustomPaymentKeyService from '@repo/database/payment-keys';
import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import { chip } from '@repo/payments';
import {
  type ChipClient,
  type ChipPaymentResponse,
  createChipClient,
} from '@repo/payments/chip';
import { NextResponse } from 'next/server';
import {
  getDonationFromTransactionId,
  handleDonationPayment,
  handleDonationPaymentCancelled,
  handleDonationPaymentFailed,
} from './(donation)/handler';
import {
  handlePremiumUpgradeCancelled,
  handlePremiumUpgradeFailed,
  handlePremiumUpgradePayment,
} from './(event_premium_tier)/handler';
import {
  getOrderFromTransactionId,
  handleTicketPayment,
  handleTicketPaymentCancelled,
  handleTicketPaymentFailed,
} from './(ticket)/handler';

/**
 * Handle payment completed event
 * @param data Payment data from webhook
 */
const handlePaymentCompleted = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Payment completed event missing ID');
    return;
  }

  const product = data.purchase?.products?.[0];

  // determine the handler to use
  switch (product.category) {
    // Handle event tier upgrate payment
    case 'event_premium_tier':
      await handlePremiumUpgradePayment(data);
      break;

    // Handle ticket purchase payment
    case 'ticket':
      await handleTicketPayment(data);
      break;

    // Handle event donation payment
    case 'donation':
      await handleDonationPayment(data);
      break;

    default:
      break;
  }
};

/**
 * Handle payment failed event
 * @param data Payment data from webhook
 */
const handlePaymentFailed = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Payment failed event missing ID');
    return;
  }

  const product = data.purchase?.products?.[0];

  // determine the handler to use
  switch (product.category) {
    // Handle event tier upgrate payment failed
    case 'event_premium_tier':
      await handlePremiumUpgradeFailed(data);
      break;

    // Handle ticket purchase payment failed
    case 'ticket':
      await handleTicketPaymentFailed(data);
      break;

    // Handle event donation payment failed
    case 'donation':
      await handleDonationPaymentFailed(data);
      break;

    default:
      break;
  }
};

/**
 * Handle payment canceled event
 * @param data Payment data from webhook
 */
const handlePaymentCanceled = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Payment canceled event missing ID');
    return;
  }
  log.warn('paymentcancelled');

  const product = data.purchase?.products?.[0];

  // determine the handler to use
  switch (product.category) {
    // Handle event tier upgrate payment cancelled
    case 'event_premium_tier':
      await handlePremiumUpgradeCancelled(data);
      break;

    // Handle ticket purchase payment cancelled
    case 'ticket':
      await handleTicketPaymentCancelled(data);
      break;

    case 'donation':
      await handleDonationPaymentCancelled(data);
      break;

    default:
      break;
  }
};

async function getEventIdFromTransactionId(data: ChipPaymentResponse) {
  const product = data.purchase?.products?.[0];

  switch (product?.category) {
    case 'ticket': {
      const order = await getOrderFromTransactionId(data.id);
      return order?.eventId;
    }
    case 'donation': {
      const donation = await getDonationFromTransactionId(data.id);
      return donation?.eventId;
    }
    default:
      return null;
  }
}

// checks & return the custom payment module if custom payment is enabled
async function validateCustomPaymentModule(eventId?: string | null) {
  log.info('validating custom payment module');

  if (!eventId) {
    // TODO: interrupt during this stage? or just fallback to default ChipClient
    log.error(
      'chip webhook: validateCustomPaymentModule > eventId is required'
    );
    return null;
  }

  // fetch event module
  const eventModule = await database.eventModule.findUnique({
    where: {
      eventId,
    },
    select: {
      id: true,
      customPaymentEnabled: true,
      customPaymentModule: {
        select: {
          id: true,
          chipsEnabled: true,
          stripeEnabled: true,
        },
      },
    },
  });

  if (!eventModule) {
    // TODO: interrupt during this stage? or just fallback to default ChipClient
    log.error(
      'chip webhook: validateCustomPaymentModule > Event module not found'
    );
    return null;
  }

  // further fetch custom payment module if custom payment is enabled
  if (eventModule.customPaymentEnabled) {
    if (!eventModule.customPaymentModule) {
      // TODO: interrupt during this stage? or just fallback to default ChipClient
      log.error(
        'chip webhook: validateCustomPaymentModule > Custom payment module not found'
      );
      return null;
    }

    // fetch & decrypt keys from organizer if enabled
    if (eventModule.customPaymentModule.chipsEnabled) {
      return await CustomPaymentKeyService.chipKeys(eventId);
    }
  }

  return null;
}

/**
 * Webhook handler for Chip payment gateway
 * @param request Request object
 * @returns Response object
 */
export async function POST(request: Request) {
  log.info('Chip webhook request received');

  try {
    // Get the request body
    const body = await request.text();

    // Parse into ChipPaymentResponse
    const data = JSON.parse(body) as ChipPaymentResponse;
    log.info('webhook > parsed body', data);

    // get eventId using transaction id
    const eventId = await getEventIdFromTransactionId(data);
    log.info('webhook > getEventIdFromTransactionId', { eventId });

    // fetch custom payment keys if available
    const customChipPaymentKeys = await validateCustomPaymentModule(eventId);
    const { chipSecretKey, chipBrandId, chipPublicKey } =
      customChipPaymentKeys || {};

    let chipClient: ChipClient = chip;
    // instantiate a new ChipClient if custom payment keys are available
    if (chipSecretKey && chipBrandId && chipPublicKey) {
      log.info('Creating custom payment ChipClient');

      // create ChipClient instance
      chipClient = createChipClient({
        secretKey: chipSecretKey,
        brand: chipBrandId,
        publicKey: chipPublicKey,
      });

      log.info('Created custom payment ChipClient');
    }

    // Get the signature from the headers
    const headersList = request.headers;
    const signature = headersList.get('x-signature') || '';

    // Verify the signature
    if (!signature) {
      log.warn('Missing signature in webhook request');
      return NextResponse.json({ error: 'Missing signature' }, { status: 401 });
    }

    // Verify the signature with the Chip API key
    const isValid = chipClient.verifyWebhookSignature(body, signature);

    if (!isValid) {
      log.warn('Invalid signature in webhook request');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    log.info(`Received Chip webhook with status: ${data.status}`, {
      paymentId: data.id,
      status: data.status,
    });

    // Process the webhook based on the event type or payment status
    // Handle based on payment status
    switch (data.status) {
      case 'paid':
        await handlePaymentCompleted(data);
        break;
      case 'error':
      case 'failed':
        await handlePaymentFailed(data);
        break;
      case 'canceled':
        await handlePaymentCanceled(data);
        break;
      case 'created':
      case 'pending':
      case 'viewed':
        // These statuses don't require any action
        log.info(
          `Received Chip event with status: ${data.status} for payment ID: ${data.id}`
        );
        break;
      default:
        log.info(`Unhandled webhook status: ${data.status}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    const message = parseError(error);
    log.error(`Error processing webhook: ${message}`, { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
