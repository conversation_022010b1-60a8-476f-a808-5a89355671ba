import { withOrganizer } from '@/app/lib/api';
import type { AuthResult } from '@repo/auth';
import { getOrganizerFilter } from '@repo/auth/permission-utils';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (_request: NextRequest, authResult: AuthResult) => {
  log.info('Ticket scanned request received', {
    organizerId: authResult.organizerId,
  });

  // Get organizer filter using permission utilities
  // Super admins can see all scanned tickets, organizers can only see their own
  const organizerId = getOrganizerFilter(authResult);

  // If organizerId is null, user has no access to ticket data
  if (organizerId === null) {
    return NextResponse.json(
      {
        success: true,
        data: [],
      },
      { status: 200 }
    );
  }

  try {
    // Fetch all redemptions scanned by this user, but only for events belonging to their organization
    const redemptions = await database.ticketRedemption.findMany({
      where: {
        scannedBy: authResult.user?.id,
        ticket: {
          event: {
            organizerId: organizerId ?? undefined,
          },
        },
      },
      select: {
        id: true,
        type: true,
        ticketId: true,
        ticket: {
          select: {
            id: true,
            slug: true,
            ownerName: true,
            ownerEmail: true,
            ownerPhone: true,
            ticketTypeId: true,
            ticketType: {
              select: {
                name: true,
              },
            },
          },
        },
        user: {
          select: {
            name: true,
          },
        },
        remark: true,
        scannedAt: true,
        scannedBy: true,
      },
      orderBy: {
        scannedAt: 'desc',
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: serializePrisma(redemptions),
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error fetching scanned tickets:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch scanned tickets',
      },
      { status: 500 }
    );
  }
};

export const OPTIONS = withOrganizer(handler);
export const GET = withOrganizer(handler);
