import { withNextCors } from '@/app/lib/api';
import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (
  request: NextRequest,
  context: { params: Promise<{ slug: string }> }
) => {
  log.info('Ticket verification request received');

  const { slug } = await context.params;
  const session = await auth.api.getSession({
    headers: request.headers,
  });

  if (!slug) {
    return NextResponse.json(
      {
        error: 'Ticket slug is required',
      },
      { status: 400 }
    );
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!session.session.activeOrganizationId) {
    return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
  }

  const ticket = await database.ticket.findUnique({
    where: {
      slug: slug,
      AND: {
        event: {
          organizer: {
            user: {
              members: {
                some: {
                  userId: session.user.id,
                  organizationId: session.session.activeOrganizationId,
                },
              },
            },
          },
        },
      },
    },
    include: {
      timeSlot: true,
      order: true,
      event: {
        select: {
          title: true,
          organizer: {
            select: {
              name: true,
            },
          },
        },
      },
      ticketType: {
        select: {
          name: true,
        },
      },
    },
  });

  if (!ticket) {
    return NextResponse.json(
      {
        success: false,
        error: 'Ticket not found',
      },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { success: true, data: serializePrisma(ticket) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withNextCors<{ params: Promise<{ slug: string }> }>(
  handler
);
export const GET = withNextCors<{ params: Promise<{ slug: string }> }>(handler);
