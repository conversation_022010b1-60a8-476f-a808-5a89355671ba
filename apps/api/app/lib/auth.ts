import { getCorsHeaders } from '@/app/lib/api';
import { auth } from '@repo/auth/server';
import type { Auth } from '@repo/auth/server';
import { log } from '@repo/observability/log';
import { headers } from 'next/headers';
import type { NextRequest } from 'next/server';

/**
 * Auth result interface
 */
export interface AuthResult {
  session: Auth | null;
  user: Auth['user'] | null;
  isAuthenticated: boolean;
  organizerId?: string | null;
  activeOrganizationId?: string | null;
}

/**
 * Auth error types
 */
export class AuthError extends Error {
  code: 'UNAUTHORIZED' | 'FORBIDDEN' | 'INVALID_ORIGIN' | 'SESSION_ERROR';

  constructor(
    message: string,
    code: 'UNAUTHORIZED' | 'FORBIDDEN' | 'INVALID_ORIGIN' | 'SESSION_ERROR'
  ) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
  }
}

/**
 * Retrieves and validates session from better-auth
 */
async function getSession(request?: NextRequest): Promise<Auth | null> {
  try {
    // Get session using better-auth
    const session = await auth.api.getSession({
      headers: request?.headers || (await headers()),
    });

    return session;
  } catch (error) {
    console.error('Error retrieving session:', error);
    return null;
  }
}

/**
 * Main auth function that validates origin and retrieves session
 * Use this for API routes that need authentication and origin validation
 */
export async function authenticateRequest(
  request?: NextRequest
): Promise<AuthResult> {
  // Get session
  const session = await getSession(request);

  return {
    session,
    user: session?.user || null,
    isAuthenticated: !!session?.user,
    organizerId: session?.session?.organizerId,
    activeOrganizationId: session?.session?.activeOrganizationId,
  };
}

/**
 * Auth function that requires authentication
 * Throws error if user is not authenticated
 */
export async function requireAuth(request?: NextRequest): Promise<AuthResult> {
  const authResult = await authenticateRequest(request);
  log.info('requireAuth', {
    isAuthenticated: authResult.isAuthenticated,
    organizerId: authResult.organizerId,
  });

  if (!authResult.isAuthenticated) {
    throw new AuthError('Authentication required', 'UNAUTHORIZED');
  }

  return authResult;
}

/**
 * Auth function that requires organizer role
 * Throws error if user is not an organizer
 */
export async function requireOrganizer(
  request?: NextRequest
): Promise<AuthResult> {
  const authResult = await requireAuth(request);
  log.info('requireOrganizer', {
    organizerId: authResult.organizerId,
  });

  if (!authResult.organizerId) {
    throw new AuthError('Organizer role required', 'FORBIDDEN');
  }

  return authResult;
}

/**
 * Auth function that requires super admin role
 */
export async function requireSuperAdmin(
  request?: NextRequest
): Promise<AuthResult> {
  const authResult = await requireAuth(request);
  log.info('requireSuperAdmin', {
    role: authResult.user?.role,
  });

  if (authResult.user?.role !== 'super-admin') {
    throw new AuthError('Super admin role required', 'FORBIDDEN');
  }

  return authResult;
}

/**
 * Middleware helper for API routes
 * Returns a standardized error response for auth failures with CORS headers
 */
export function handleAuthError(
  error: unknown,
  request?: NextRequest
): Response {
  const origin = request?.headers.get('origin') ?? '';
  const corsHeaders = getCorsHeaders(origin);

  if (error instanceof AuthError) {
    const statusCode = {
      UNAUTHORIZED: 401,
      FORBIDDEN: 403,
      INVALID_ORIGIN: 403,
      SESSION_ERROR: 500,
    }[error.code];

    return new Response(
      JSON.stringify({
        error: error.message,
        code: error.code,
      }),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }

  // Generic error
  return new Response(
    JSON.stringify({
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
    }),
    {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    }
  );
}
