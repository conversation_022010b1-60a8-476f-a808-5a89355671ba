import { getCorsHeaders } from '@/app/lib/api';
import { organizerAuth } from '@repo/auth/organizer-auth';
import { customerAuth } from '@repo/auth/customer-auth';
import type { OrganizerAuth } from '@repo/auth/organizer-auth';
import type { CustomerAuth } from '@repo/auth/customer-auth';
import { log } from '@repo/observability/log';
import { headers } from 'next/headers';
import type { NextRequest } from 'next/server';

/**
 * Auth result interface that can handle both organizer and customer sessions
 */
export interface AuthResult {
  session: OrganizerAuth | CustomerAuth | null;
  user: OrganizerAuth['user'] | CustomerAuth['user'] | null;
  isAuthenticated: boolean;
  organizerId?: string | null;
  activeOrganizationId?: string | null;
  sessionType?: 'organizer' | 'customer' | null;
}

/**
 * Auth error types
 */
export class AuthError extends Error {
  code: 'UNAUTHORIZED' | 'FORBIDDEN' | 'INVALID_ORIGIN' | 'SESSION_ERROR';

  constructor(
    message: string,
    code: 'UNAUTHORIZED' | 'FORBIDDEN' | 'INVALID_ORIGIN' | 'SESSION_ERROR'
  ) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
  }
}

/**
 * Retrieves and validates sessions from both auth systems
 * Returns the first valid session found, prioritizing organizer sessions
 */
async function getSession(request?: NextRequest): Promise<{
  session: OrganizerAuth | CustomerAuth | null;
  sessionType: 'organizer' | 'customer' | null;
}> {
  const requestHeaders = request?.headers || (await headers());

  try {
    // Try organizer session first
    const organizerSession = await organizerAuth.api.getSession({
      headers: requestHeaders,
    });

    if (organizerSession?.user) {
      return {
        session: organizerSession,
        sessionType: 'organizer',
      };
    }
  } catch (error) {
    console.error('Error retrieving organizer session:', error);
  }

  try {
    // Try customer session
    const customerSession = await customerAuth.api.getSession({
      headers: requestHeaders,
    });

    if (customerSession?.user) {
      return {
        session: customerSession,
        sessionType: 'customer',
      };
    }
  } catch (error) {
    console.error('Error retrieving customer session:', error);
  }

  return {
    session: null,
    sessionType: null,
  };
}

/**
 * Main auth function that validates origin and retrieves session
 * Use this for API routes that need authentication and origin validation
 */
export async function authenticateRequest(
  request?: NextRequest
): Promise<AuthResult> {
  // Get session from either auth system
  const { session, sessionType } = await getSession(request);

  return {
    session,
    user: session?.user || null,
    isAuthenticated: !!session?.user,
    organizerId:
      sessionType === 'organizer'
        ? (session as OrganizerAuth)?.session?.organizerId
        : null,
    activeOrganizationId:
      sessionType === 'organizer'
        ? (session as OrganizerAuth)?.session?.activeOrganizationId
        : null,
    sessionType,
  };
}

/**
 * Auth function that requires authentication
 * Throws error if user is not authenticated
 */
export async function requireAuth(request?: NextRequest): Promise<AuthResult> {
  const authResult = await authenticateRequest(request);
  log.info('requireAuth', {
    isAuthenticated: authResult.isAuthenticated,
    organizerId: authResult.organizerId,
  });

  if (!authResult.isAuthenticated) {
    throw new AuthError('Authentication required', 'UNAUTHORIZED');
  }

  return authResult;
}

/**
 * Auth function that requires organizer role
 * Throws error if user is not an organizer
 * Optionally validates that request is coming from organizer app
 */
export async function requireOrganizer(
  request?: NextRequest,
  validateOrigin = false
): Promise<AuthResult> {
  const authResult = await requireAuth(request);
  log.info('requireOrganizer', {
    organizerId: authResult.organizerId,
    sessionType: authResult.sessionType,
  });

  // Must be an organizer session with organizerId
  if (authResult.sessionType !== 'organizer' || !authResult.organizerId) {
    throw new AuthError('Organizer role required', 'FORBIDDEN');
  }

  // Optional: Validate that request is coming from organizer app
  if (validateOrigin && request) {
    const origin = request.headers.get('origin');
    const referer = request.headers.get('referer');

    const isFromOrganizerApp =
      origin?.includes('app.ticketcare.my') ||
      origin?.includes('localhost:3000') || // Development
      referer?.includes('app.ticketcare.my') ||
      referer?.includes('localhost:3000');

    if (!isFromOrganizerApp) {
      log.warn('Organizer endpoint accessed from non-organizer origin', {
        origin,
        referer,
        organizerId: authResult.organizerId,
      });
      // You can choose to throw an error or just log the warning
      // throw new AuthError('Invalid origin for organizer endpoint', 'FORBIDDEN');
    }
  }

  return authResult;
}

/**
 * Auth function that requires customer role
 * Throws error if user is not a customer
 * Optionally validates that request is coming from customer web app
 */
export async function requireCustomer(
  request?: NextRequest,
  validateOrigin = false
): Promise<AuthResult> {
  const authResult = await requireAuth(request);
  log.info('requireCustomer', {
    role: authResult.user?.role,
    sessionType: authResult.sessionType,
  });

  // Must be a customer session
  if (
    authResult.sessionType !== 'customer' ||
    authResult.user?.role !== 'customer'
  ) {
    throw new AuthError('Customer role required', 'FORBIDDEN');
  }

  // Optional: Validate that request is coming from customer web app
  if (validateOrigin && request) {
    const origin = request.headers.get('origin');
    const referer = request.headers.get('referer');

    const isFromCustomerApp =
      origin?.includes('www.ticketcare.my') ||
      origin?.includes('localhost:3001') || // Development
      referer?.includes('www.ticketcare.my') ||
      referer?.includes('localhost:3001');

    if (!isFromCustomerApp) {
      log.warn('Customer endpoint accessed from non-customer origin', {
        origin,
        referer,
        role: authResult.user?.role,
      });
      // You can choose to throw an error or just log the warning
      // throw new AuthError('Invalid origin for customer endpoint', 'FORBIDDEN');
    }
  }

  return authResult;
}

/**
 * Auth function that requires super admin role
 */
export async function requireSuperAdmin(
  request?: NextRequest
): Promise<AuthResult> {
  const authResult = await requireAuth(request);
  log.info('requireSuperAdmin', {
    role: authResult.user?.role,
  });

  if (authResult.user?.role !== 'super-admin') {
    throw new AuthError('Super admin role required', 'FORBIDDEN');
  }

  return authResult;
}

/**
 * Middleware helper for API routes
 * Returns a standardized error response for auth failures with CORS headers
 */
export function handleAuthError(
  error: unknown,
  request?: NextRequest
): Response {
  const origin = request?.headers.get('origin') ?? '';
  const corsHeaders = getCorsHeaders(origin);

  if (error instanceof AuthError) {
    const statusCode = {
      UNAUTHORIZED: 401,
      FORBIDDEN: 403,
      INVALID_ORIGIN: 403,
      SESSION_ERROR: 500,
    }[error.code];

    return new Response(
      JSON.stringify({
        error: error.message,
        code: error.code,
      }),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }

  // Generic error
  return new Response(
    JSON.stringify({
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
    }),
    {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    }
  );
}
