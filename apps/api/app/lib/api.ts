// Config CORS
// ========================================================
/**
 *
 * @param origin
 * @returns
 */

import {
  handleAuthError,
  requireAuth,
  requireOrganizer,
  requireCustomer,
} from '@/app/lib/auth';
import { env } from '@/env';
import type { Auth } from '@repo/auth/server';
import type {} from 'next';
import { type NextRequest, NextResponse } from 'next/server';

// --- CORS LOGIC ---
const allowedMethods = 'GET, POST, PUT, DELETE, OPTIONS';
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3002',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:3002',
  'http://localhost:19000', // Expo web
  'http://localhost:19006', // Expo web
  'https://localhost:19006', // Expo web secure
  'http://localhost:8081', // Expo web alternative
  'https://ticketcare-app.vercel.app',
  'https://ticketcare-web.vercel.app',
  'https://app.ticketcare.my',
  'https://api.ticketcare.my',
  'https://www.ticketcare.my',
  'https://ticketcare.my',
  'ticketcare://',
  'exp://',
  'exp+ticketcare://',
  env.NEXT_PUBLIC_APP_URL,
  env.NEXT_PUBLIC_WEB_URL,
];

export const getCorsHeaders = (origin: string) => {
  // Define the headers type to include the CORS headers
  const headers = {
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Allow-Methods': allowedMethods,
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept',
    'Access-Control-Allow-Origin': '',
  };

  // If no origin provided, use a safe default
  if (!origin) {
    return headers;
  }

  // Set the origin header to the requesting origin if it's allowed
  if (allowedOrigins.includes(origin)) {
    headers['Access-Control-Allow-Origin'] = origin;
  }

  // Return result
  return headers;
};

export const withCors = (handler: (req: Request) => Promise<Response>) => {
  return async (req: NextRequest) => {
    const origin = req.headers.get('origin') ?? '';
    const corsHeaders = getCorsHeaders(origin);

    if (req.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    const response = await handler(req);
    for (const [key, value] of Object.entries(corsHeaders)) {
      response.headers.set(key, value);
    }

    return response;
  };
};

export const withNextCors = <T = unknown>(
  handler: (req: NextRequest, context: T) => Promise<NextResponse>
) => {
  return async (req: NextRequest, context: T) => {
    const origin = req.headers.get('origin') ?? '';
    const corsHeaders = getCorsHeaders(origin);

    if (req.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    const response = await handler(req, context);
    for (const [key, value] of Object.entries(corsHeaders)) {
      response.headers.set(key, value);
    }

    return response;
  };
};

// --- AUTH LOGIC ---
export interface AuthResult {
  session: Auth | null;
  user: Auth['user'] | null;
  isAuthenticated: boolean;
  organizerId?: string | null;
  activeOrganizationId?: string | null;
}

/**
 * Middleware authentication wrapper with CORS support
 */
export function withAuth(
  handler: (request: NextRequest, authResult: AuthResult) => Promise<Response>
) {
  return async (request: NextRequest) => {
    const origin = request.headers.get('origin') ?? '';
    const corsHeaders = getCorsHeaders(origin);

    // Handle OPTIONS request for CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    try {
      const authResult = await requireAuth(request);
      const response = await handler(request, authResult);

      // Add CORS headers to the response
      for (const [key, value] of Object.entries(corsHeaders)) {
        response.headers.set(key, value);
      }

      return response;
    } catch (error) {
      return handleAuthError(error, request);
    }
  };
}

/**
 * Middleware organizer wrapper with CORS support
 */
// Overloads to support handlers with and without Next.js context
export function withOrganizer(
  handler: (request: NextRequest, authResult: AuthResult) => Promise<Response>
): (request: NextRequest) => Promise<Response>;
export function withOrganizer<T = unknown>(
  handler: (
    request: NextRequest,
    authResult: AuthResult,
    context: T
  ) => Promise<Response>
): (request: NextRequest, context: T) => Promise<Response>;
export function withOrganizer(handler: any) {
  return async (request: NextRequest, context?: unknown) => {
    const origin = request.headers.get('origin') ?? '';
    const corsHeaders = getCorsHeaders(origin);

    // Handle OPTIONS request for CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    try {
      const authResult = await requireOrganizer(request);
      const response: NextResponse = await (context !== undefined
        ? handler(request, authResult, context)
        : handler(request, authResult));

      // Add CORS headers to the response
      for (const [key, value] of Object.entries(corsHeaders)) {
        response.headers.set(key, value);
      }

      return response;
    } catch (error) {
      return handleAuthError(error, request);
    }
  };
}

/**
 * Middleware customer wrapper with CORS support
 */
export function withCustomer(
  handler: (request: NextRequest, authResult: AuthResult) => Promise<Response>
): (request: NextRequest) => Promise<Response>;
export function withCustomer<T = unknown>(
  handler: (
    request: NextRequest,
    authResult: AuthResult,
    context: T
  ) => Promise<Response>
): (request: NextRequest, context: T) => Promise<Response>;
export function withCustomer(handler: any) {
  return async (request: NextRequest, context?: unknown) => {
    const origin = request.headers.get('origin') ?? '';
    const corsHeaders = getCorsHeaders(origin);

    // Handle OPTIONS request for CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders,
      });
    }

    try {
      const authResult = await requireCustomer(request);
      const response: NextResponse = await (context !== undefined
        ? handler(request, authResult, context)
        : handler(request, authResult));

      // Add CORS headers to the response
      for (const [key, value] of Object.entries(corsHeaders)) {
        response.headers.set(key, value);
      }

      return response;
    } catch (error) {
      return handleAuthError(error, request);
    }
  };
}
