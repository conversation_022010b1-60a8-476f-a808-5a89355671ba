import { keys as analytics } from '@repo/analytics/keys';
import { keys as auth } from '@repo/auth/keys';
import { keys as database } from '@repo/database/keys';
import { keys as email } from '@repo/email/keys';
import { keys as core } from '@repo/next-config/keys';
import { keys as observability } from '@repo/observability/keys';
import { keys as payments } from '@repo/payments/keys';
import { keys as storage } from '@repo/storage/keys';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  extends: [
    auth(),
    analytics(),
    core(),
    database(),
    email(),
    observability(),
    payments(),
    storage(),
  ],
  server: {
    RATE_LIMIT_ENABLED: z.enum(['true', 'false']),
  },
  client: {},
  runtimeEnv: {
    RATE_LIMIT_ENABLED: process.env.RATE_LIMIT_ENABLED,
  },
});
