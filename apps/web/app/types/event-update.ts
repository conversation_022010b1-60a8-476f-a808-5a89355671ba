/**
 * Represents an event update item that can be displayed in the updates accordion
 */
export interface EventUpdate {
  /**
   * Unique identifier for the update
   */
  id: string;

  /**
   * Title of the update to be displayed in the accordion trigger
   */
  title: string;

  /**
   * Content of the update in serialized editor format
   */
  content: string;

  /**
   * Creation date of the update
   */
  createdAt: string;

  /**
   * Last update date
   */
  updatedAt: string;
}

/**
 * Type for the updates array in the Event model
 */
export type EventUpdates = EventUpdate[];
