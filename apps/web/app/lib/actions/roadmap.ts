'use server';

import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { unstable_cache } from 'next/cache';

export interface RoadmapTask {
  id: string;
  title: string;
  description?: string | null;
  status: 'planned' | 'in-progress' | 'in-review' | 'completed';
  priority: 'high' | 'medium' | 'low';
  category: 'feature' | 'bug-fix' | 'enhancement' | 'infrastructure';
  estimatedTimeline?: string | null;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Fetch all public roadmap tasks directly from the database
 */
export const getRoadmapTasks = unstable_cache(
  async (): Promise<RoadmapTask[]> => {
    log.debug('getRoadmapTasks called');

    try {
      const tasks = await database.roadmapTask.findMany({
        orderBy: [{ status: 'asc' }, { order: 'asc' }, { createdAt: 'desc' }],
        select: {
          id: true,
          title: true,
          description: true,
          status: true,
          priority: true,
          category: true,
          estimatedTimeline: true,
          order: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      log.info('Roadmap tasks fetched successfully', {
        count: tasks.length,
      });

      return tasks.map((task) => ({
        ...task,
        status: task.status as RoadmapTask['status'],
        priority: task.priority as RoadmapTask['priority'],
        category: task.category as RoadmapTask['category'],
      }));
    } catch (error) {
      log.error('Error fetching roadmap tasks', { error });

      // Return empty array on error to prevent page crashes
      return [];
    }
  },
  ['roadmap-tasks'],
  {
    revalidate: 300, // Cache for 5 minutes
    tags: ['roadmap-tasks'],
  }
);
