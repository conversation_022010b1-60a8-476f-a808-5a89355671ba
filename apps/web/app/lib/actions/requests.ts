'use server';

import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { revalidateTag } from 'next/cache';
import { z } from 'zod';

// Validation schema for public request submission
const publicRequestSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description must be less than 2000 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  name: z
    .string()
    .max(100, 'Name must be less than 100 characters')
    .optional()
    .or(z.literal('')),
});

export type PublicRequestInput = z.infer<typeof publicRequestSchema>;

export interface PublicRequest extends PublicRequestInput {
  id: string;
  status: 'pending' | 'reviewed' | 'approved' | 'rejected' | 'converted';
  priority?: 'high' | 'medium' | 'low';
  category?: 'feature' | 'bug-fix' | 'enhancement' | 'infrastructure';
  adminNotes?: string;
  convertedToTaskId?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Submit a public feature request directly to the database
 */
export async function createPublicRequest(
  input: PublicRequestInput
): Promise<{ success: boolean; message: string }> {
  log.debug('createPublicRequest called', { title: input.title });

  try {
    // Validate input data
    const validatedInput = publicRequestSchema.parse(input);

    // Clean up empty strings to null for optional fields
    const cleanedInput = {
      ...validatedInput,
      email: validatedInput.email || null,
      name: validatedInput.name || null,
    };

    // Create the request directly in the database
    const request = await database.publicRequest.create({
      data: {
        title: cleanedInput.title,
        description: cleanedInput.description,
        email: cleanedInput.email,
        name: cleanedInput.name,
        status: 'pending',
      },
      select: {
        id: true,
        title: true,
      },
    });

    log.info('Public request submitted successfully', {
      requestId: request.id,
      title: request.title,
    });

    // Revalidate any cached data that might be affected
    revalidateTag('public-requests');

    return {
      success: true,
      message:
        "Your request has been submitted successfully! We'll review it and get back to you if you provided contact information.",
    };
  } catch (error) {
    log.error('Error submitting public request', { error });

    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((e) => e.message).join(', ');
      return {
        success: false,
        message: `Validation error: ${errorMessages}`,
      };
    }

    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'An unexpected error occurred. Please try again later.',
    };
  }
}
