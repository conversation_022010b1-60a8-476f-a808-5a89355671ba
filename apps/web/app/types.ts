import type { getCart } from '@/app/[locale]/(cart)/actions';
import type { getEvent } from '@events/events/[slug]/actions';
import type { getOrganizer } from '@events/organizers/[slug]/action';

export type GetEventResponse = Awaited<ReturnType<typeof getEvent>>;
export type SerializedEvent = NonNullable<GetEventResponse>;

export type SerializedCart = NonNullable<Awaited<ReturnType<typeof getCart>>>;

export type SerializedCartItem = SerializedCart['cartItems'][number];

export type SerializedOrganizer = Awaited<ReturnType<typeof getOrganizer>>;
