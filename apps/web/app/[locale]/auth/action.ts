'use server';

import { auth } from '@repo/auth/server';

type SendOTPResponse = {
  success: boolean;
  error?: {
    message: string;
  };
};

export async function sendOTP(email: string): Promise<SendOTPResponse> {
  try {
    const response = await auth.api.sendVerificationOTP({
      body: { email, type: 'sign-in' },
    });

    if (response?.success) {
      return {
        success: true,
      };
    }

    return {
      success: false,
      error: {
        message: 'Failed to send verification code, please try again.',
      },
    };
  } catch (e) {
    // biome-ignore lint/suspicious/noExplicitAny: <unable to find official better-auth error type>
    const _error = e as unknown as any;

    if (_error.body) {
      return {
        success: false,
        error: {
          message: `Failed to send OTP: ${_error.body.message}`,
        },
      };
    }

    return {
      success: false,
      error: {
        message: 'An unknown error occurred, please try again later.',
      },
    };
  }
}

export async function signInWithOTP(
  email: string,
  otp: string
): Promise<SendOTPResponse> {
  try {
    const { token, user } = await auth.api.signInEmailOTP({
      body: { email, otp },
    });

    if (token && user) {
      return {
        success: true,
      };
    }

    return {
      success: false,
      error: {
        message: 'Failed to verify OTP, please try again.',
      },
    };
  } catch (e) {
    // biome-ignore lint/suspicious/noExplicitAny: <unable to find official better-auth error type>
    const _error = e as unknown as any;

    if (_error.body) {
      return {
        success: false,
        error: {
          message: `${_error.body.message}`,
        },
      };
    }

    return {
      success: false,
      error: {
        message: 'An unknown error occurred, please try again later.',
      },
    };
  }
}
