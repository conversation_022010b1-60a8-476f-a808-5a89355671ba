'use client';

import { sendOTP, signInWithOTP } from '@/app/[locale]/auth/action';
import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormField,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import {
  InputOTP,
  InputOTPSlot,
} from '@repo/design-system/components/ui/input-otp';
import { BackgroundPattern } from '@repo/design-system/components/untitledui/shared-assets/background-patterns';
import { ArrowLeft } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export const VerifyOtpForm = ({ email }: { email: string }) => {
  const form = useForm<{ otp: string }>();
  const { formState } = form;
  const { isSubmitting } = formState;

  const [countdown, setCountdown] = useState(60);
  const [isResending, setIsResending] = useState(false);
  const [isBusy, setIsBusy] = useState(false);

  const onSubmit = async (data: { otp: string }) => {
    if (!data.otp) {
      form.setError('otp', {
        message: 'OTP is required',
      });
      return;
    }

    try {
      setIsBusy(true);
      const { success, error } = await signInWithOTP(email, data.otp);

      // if error or not success
      if (!success || error) {
        form.setError('otp', {
          message:
            error?.message ?? 'Failed to verify OTP, please try again later.',
        });
        setIsBusy(false);
        return;
      }

      // Use window.location.href for a hard reload to refresh session data
      window.location.href = '/account/orders';
    } catch (e) {
      console.error(e);
      setIsBusy(false);
      form.setError('otp', {
        message: 'Unable to verify OTP, please try again later.',
      });
    }
  };

  const handleResendOTP = async () => {
    if (countdown > 0) return;

    setIsResending(true);
    try {
      const { success, error } = await sendOTP(email);

      if (error || !success) {
        form.setError('otp', {
          message:
            error?.message ?? 'Unable to send OTP, please try again later.',
        });
        return;
      }

      // Reset countdown after successful resend
      setCountdown(60);
    } catch (e) {
      console.error(e);
      form.setError('otp', {
        message: `Unable to send OTP. ${e}`,
      });
    } finally {
      setIsResending(false);
    }
  };

  // Start countdown timer when component mounts
  useEffect(() => {
    // Decrement countdown every second
    const timer = setInterval(() => {
      setCountdown((prevCountdown) => {
        if (prevCountdown <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prevCountdown - 1;
      });
    }, 1000);

    // Clean up timer on unmount
    return () => clearInterval(timer);
  }, []);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <section className="min-h-[60dvh] overflow-hidden bg-background px-4 py-12 md:px-8 md:pt-24">
          <div className="mx-auto flex w-full max-w-90 flex-col gap-8">
            <div className="flex flex-col items-center gap-6 text-center">
              <div className="relative w-[240px] h-[124px]">
                <div className="absolute inset-x-0 top-[50%] translate-y-[-50%] z-10">
                  <Image
                    src={darkLogo}
                    alt="Logo"
                    width={248}
                    height={124}
                    className="hidden dark:block"
                  />
                  <Image
                    src={lightLogo}
                    alt="Logo"
                    width={248}
                    height={124}
                    className="block dark:hidden"
                  />
                </div>
                <BackgroundPattern
                  pattern="square"
                  className="absolute top-[50%] left-1/2 z-0 hidden -translate-x-1/2 -translate-y-1/2 md:block opacity-20"
                />
                <BackgroundPattern
                  pattern="square"
                  size="md"
                  className="absolute top-1/2 left-1/2 z-0 -translate-x-1/2 -translate-y-1/2 md:hidden opacity-20"
                />
              </div>

              <div className="z-10 flex flex-col gap-2 md:gap-3">
                <h1 className="font-semibold text-primary">Check your email</h1>
                <p className="">
                  We sent a verification OTP to{' '}
                  <span className="font-medium">{email}</span>
                </p>
              </div>
            </div>

            <div className="flex flex-col items-center gap-6 md:gap-8">
              <FormField
                control={form.control}
                name="otp"
                render={({ field }) => (
                  <div className="space-y-2">
                    <InputOTP
                      maxLength={6}
                      onComplete={(data) => field.onChange(data)}
                      disabled={isSubmitting || isBusy}
                    >
                      {Array.from({ length: 6 }).map((_, index) => (
                        <InputOTPSlot
                          key={index}
                          index={index}
                          className="bg-background size-16 text-2xl rounded-lg border-gray-200 border-2"
                        />
                      ))}
                    </InputOTP>
                    <FormMessage className="capitalize text-center" />
                  </div>
                )}
              />
              <Button size="lg" type="submit" disabled={isSubmitting || isBusy}>
                {isSubmitting || isBusy ? 'Verifying...' : 'Verify email'}
              </Button>
            </div>

            <div className="flex flex-col items-center gap-8 text-center">
              <div className="flex gap-1 items-center">
                <p>Didn't receive the email?</p>
                <Button
                  variant="link"
                  size="sm"
                  onClick={handleResendOTP}
                  disabled={countdown > 0 || isResending}
                >
                  {isResending
                    ? 'Sending...'
                    : countdown > 0
                      ? `Resend in ${countdown}s`
                      : 'Click to resend'}
                </Button>
              </div>
              <Link href="/auth/login">
                <Button variant="link" size="sm" className="mx-auto">
                  <div className="flex items-center gap-2">
                    <ArrowLeft />
                    Back to log in
                  </div>
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </form>
    </Form>
  );
};
