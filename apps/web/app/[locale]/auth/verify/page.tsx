import { VerifyOtpForm } from '@/app/[locale]/auth/verify/components/verify-otp-form';

type VerifyOtpPageProps = {
  searchParams: Promise<{ email: string }>;
};

export default async function VerifyOtpPage({
  searchParams,
}: VerifyOtpPageProps) {
  const { email } = await searchParams;

  return (
    <div className="mx-auto w-full space-y-8 px-4 my-10">
      <VerifyOtpForm email={email} />
    </div>
  );
}
