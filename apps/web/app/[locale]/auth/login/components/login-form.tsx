'use client';

import { sendOTP } from '@/app/[locale]/auth/action';
import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { QuestionMarkCircledIcon } from '@radix-ui/react-icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { BackgroundPattern } from '@repo/design-system/components/untitledui/shared-assets/background-patterns';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// From UntitledUI @ https://www.untitledui.com/react/marketing/log-in-pages/login-simple-social-login-leading
export const LoginForm = () => {
  const router = useRouter();
  const form = useForm<{ email: string }>({
    defaultValues: {
      email: '',
    },
  });
  const { formState } = form;
  const { isSubmitting, errors } = formState;
  const [isBusy, setIsBusy] = useState(false);

  const onSubmit = async (data: { email: string }) => {
    try {
      setIsBusy(true);
      const { success, error } = await sendOTP(data.email);

      if (error || !success) {
        form.setError('email', {
          message:
            error?.message ?? 'Unable to send OTP, please try again later.',
        });
        setIsBusy(false);
        return;
      }

      router.push(`/auth/verify?email=${data.email}`);
    } catch (e) {
      console.error(e);
      setIsBusy(false);
      form.setError('email', {
        message: `Unable to send OTP. ${e}`,
      });
    }
  };

  return (
    <section className="relative min-h-[60dvh] overflow-hidden bg-background px-4 py-12 md:px-8 md:pt-32">
      <div className="mx-auto flex w-full flex-col gap-8 sm:max-w-90">
        <div className="flex flex-col items-center gap-6 text-center">
          <div className="relative w-[240px] h-[124px]">
            <div className="absolute inset-x-0 top-[50%] translate-y-[-50%] z-10">
              <Image
                src={darkLogo}
                alt="Logo"
                width={248}
                height={124}
                className="hidden dark:block"
              />
              <Image
                src={lightLogo}
                alt="Logo"
                width={248}
                height={124}
                className="block dark:hidden"
              />
            </div>
            <BackgroundPattern
              pattern="square"
              className="absolute top-[50%] left-1/2 z-0 hidden -translate-x-1/2 -translate-y-1/2 md:block opacity-20"
            />
            <BackgroundPattern
              pattern="square"
              size="md"
              className="absolute top-1/2 left-1/2 z-0 -translate-x-1/2 -translate-y-1/2 md:hidden opacity-20"
            />
          </div>

          <h1 className="z-10 font-semibold">Sign up/Log in to your account</h1>
          <h4>
            Please enter your&nbsp;
            <span className="inline-flex items-center">
              <b className="text-primary-red">Order Email</b>&nbsp;
              <span className="inline-flex">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <QuestionMarkCircledIcon className="size-[16px] inline-block text-primary-red" />
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="w-[360px] p-4">
                    <p className="space-y-2">
                      <span className="block font-medium">
                        Why use your order email?
                      </span>
                      <span className="block">
                        If you have placed an order beforehand, please use the
                        same email address that was used for your purchase. This
                        ensures all your tickets and order history remain
                        connected to your account.
                      </span>
                      <span className="block mt-2">
                        Using a different email will create a separate account
                        without access to your existing tickets.
                      </span>
                    </p>
                  </TooltipContent>
                </Tooltip>
              </span>
            </span>
            &nbsp;to receive a link to manage your tickets.
          </h4>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="z-10 flex flex-col gap-6"
          >
            <div className="flex flex-col gap-4">
              <Input
                {...form.register('email')}
                required
                type="email"
                name="email"
                placeholder="Enter your email"
                disabled={isSubmitting || isBusy}
                className="bg-background text-center"
              />

              {errors.email && (
                <FormMessage>{errors.email.message}</FormMessage>
              )}

              <Button type="submit" size="lg" disabled={isSubmitting || isBusy}>
                {isSubmitting || isBusy
                  ? 'Sending OTP...'
                  : 'Continue with email'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </section>
  );
};
