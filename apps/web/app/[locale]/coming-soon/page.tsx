import { Button } from '@repo/design-system/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function ComingSoonPage() {
  return (
    <div className="flex min-h-[70vh] flex-col items-center justify-center text-center">
      <div className="space-y-6">
        <div className="inline-block rounded-full bg-primary-red/10 p-3">
          <div className="rounded-full bg-primary-red/20 p-2">
            <div className="rounded-full bg-primary-red p-2">
              <svg
                className="h-6 w-6 text-white"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
                aria-labelledby="comingSoonIcon"
              >
                <title id="comingSoonIcon">Coming Soon Icon</title>
                <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                <path d="m9 12 2 2 4-4" />
              </svg>
            </div>
          </div>
        </div>
        <h1 className="text-4xl font-bold tracking-tighter hero-text">
          Coming Soon
        </h1>
        <p className="mx-auto max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed">
          We're working hard to bring you this feature. Check back soon for
          updates!
        </p>
        <Button asChild className="mt-4">
          <Link href="/" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Return to Home
          </Link>
        </Button>
      </div>
    </div>
  );
}
