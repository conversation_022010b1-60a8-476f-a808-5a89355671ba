'use client';

import { GlobalFooter } from '@/components/footer/global-footer';
import { GlobalHeader } from '@/components/header/global-header';
import type { ReactNode } from 'react';

type RootLayoutProperties = {
  readonly children: ReactNode;
  readonly params: Promise<{
    locale: string;
  }>;
};

const ComingSoonLayout = ({ children }: RootLayoutProperties) => {
  return (
    <>
      <GlobalHeader />
      <main className="mx-auto flex max-w-6xl flex-col px-4 pb-24">
        {children}
      </main>
      <GlobalFooter />
    </>
  );
};

export default ComingSoonLayout;
