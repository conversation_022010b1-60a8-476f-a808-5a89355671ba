'use client';

import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import tomato from '@/public/tomato.png';
import { cn } from '@repo/design-system/lib/utils';
import { useEffect, useRef, useState } from 'react';
import { philosophyData } from '../philosophy-data';
import Image from 'next/image';

export default function PhilosophyContent() {
  const [activeHeadings, setActiveHeadings] = useState<string[]>([]);
  const sectionRefs = useRef<Record<string, HTMLElement>>({});

  useEffect(() => {
    const sections = Object.keys(sectionRefs.current);

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      for (const entry of entries) {
        const id = entry.target.id;

        if (entry.isIntersecting) {
          setActiveHeadings((prev) =>
            prev.includes(id) ? prev : [...prev, id]
          );
        } else {
          setActiveHeadings((prev) => prev.filter((heading) => heading !== id));
        }
      }
    };

    const observer = new IntersectionObserver(observerCallback, {
      root: null,
      rootMargin: '-100px 0px -300px 0px',
      threshold: 0.1,
    });

    for (const sectionId of sections) {
      const element = sectionRefs.current[sectionId];
      if (element) {
        observer.observe(element);
      }
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const addSectionRef = (id: string, ref: HTMLElement | null) => {
    if (ref) {
      sectionRefs.current[id] = ref;
    }
  };

  return (
    <section>
      <h1 className="text-balance font-bold hero-text">
        <span>TicketCARE</span>
        <br />
        <span>Pricing Philosophy</span>
      </h1>
      <div className="relative mt-16 grid gap-10 lg:mt-28 lg:grid-cols-3">
        <div className="gap-6 lg:col-span-2">
          <div className="h-[320px] flex items-center justify-center bg-gray-200 dark:bg-gray-200/10 rounded-lg">
            <Image
              src={lightLogo}
              alt="Logo"
              width={445}
              height={128}
              className="block max-w-[248px] rounded-lg object-cover dark:lg:hidden"
            />
            <Image
              src={darkLogo}
              alt="Logo"
              width={445}
              height={128}
              className="hidden max-w-[248px] rounded-lg object-cover dark:lg:block"
            />
          </div>
          <div>
            <div
              className={cn(
                'prose dark:prose-invert prose-h3:mt-14 prose-h3:scroll-mt-20 prose-h3:text-lg prose-li:text-sm',
                '!w-full !max-w-full'
              )}
            >
              {philosophyData.map((item) => (
                <div key={item.id} className="mb-12">
                  <h2
                    className="text-3xl font-semibold scroll-mt-20"
                    id={item.id}
                    ref={(ref) => addSectionRef(item.id, ref)}
                  >
                    {item.subtitle}
                  </h2>
                  <div className="mt-4">{item.description}</div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Image
              src={tomato}
              alt="tomato"
              width={32}
              height={32}
              draggable={false}
            />
            <div>
              <p>Ricky</p>
              <p className="italic">Founder & CEO</p>
            </div>
          </div>
        </div>

        <nav className="sticky top-20 hidden h-fit lg:block">
          <p className="text-muted-foreground text-sm">ON THIS PAGE</p>
          <ul className="text-muted-foreground mt-1.5 text-xs">
            {philosophyData.map(
              (item) =>
                item.subtitle && (
                  <li key={item.id}>
                    <a
                      className={cn(
                        'border-border block border-l py-1 pl-2.5 transition-colors duration-200',
                        activeHeadings.includes(item.id)
                          ? 'border-primary text-primary font-medium'
                          : 'text-muted-foreground hover:text-primary'
                      )}
                      href={`#${item.id}`}
                    >
                      {item.subtitle}
                    </a>
                  </li>
                )
            )}
          </ul>
        </nav>
      </div>
    </section>
  );
}
