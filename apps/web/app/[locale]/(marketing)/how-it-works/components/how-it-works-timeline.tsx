'use client';

import svgManaging from '@/app/assets/icons/managing.svg';
import svgPromoting from '@/app/assets/icons/promoting.svg';
import svgSelling from '@/app/assets/icons/selling.svg';
import svgTicketing from '@/app/assets/icons/ticketing.svg';
import { Sparkles } from 'lucide-react';
import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import type { ReactNode } from 'react';

interface DataItem {
  icon: StaticImageData;
  title: string;
  description: ReactNode | string;
}

const DATA: DataItem[] = [
  {
    icon: svgTicketing,
    title: 'Create with Ease',
    description: (
      <ul className="list-disc pl-5 space-y-1">
        <li>Customize your event listings online</li>
        <li>Personalize your ticket shop</li>
        <li>Add multiple timeslots and ticket allocations</li>
      </ul>
    ),
  },
  {
    icon: svgPromoting,
    title: 'Promote Effectively',
    description: (
      <ul className="list-disc pl-5 space-y-1">
        <li>Access a suite of promotional tools</li>
        <li>Seamlessly embed your ticket shop app</li>
        <li>Incentivize customers with customized pricing</li>
      </ul>
    ),
  },
  {
    icon: svgSelling,
    title: 'Sell Simply',
    description: (
      <ul className="list-disc pl-5 space-y-1">
        <li>Start selling tickets online and in-house</li>
        <li>Book instantly and securely online</li>
        <li>Benefit from a single inventory that integrates</li>
      </ul>
    ),
  },
  {
    icon: svgManaging,
    title: 'Manage & Monitor',
    description: (
      <ul className="list-disc pl-5 space-y-1">
        <li>Dashboard to manage and maintain event listings</li>
        <li>Streamline front-of-house operations with tools</li>
        <li>Sales reports for analysis and accounting</li>
      </ul>
    ),
  },
];

const HowItWorksTimeline = () => {
  return (
    <section>
      <div className="container">
        <div className="grid gap-8 sm:gap-12 lg:grid-cols-2 lg:gap-16">
          {/* Left Column - Fixed Content */}
          <div className="lg:sticky lg:top-24 lg:self-start">
            <div className="max-w-lg">
              <h2 className="text-3xl font-bold tracking-tight text-primary sm:text-4xl lg:text-5xl">
                Elevating{' '}
                <span className="relative inline-block">
                  <span className="text-muted-foreground">Events</span>
                  <Sparkles className="absolute -top-2 -right-4 size-5 fill-yellow-500 stroke-none" />
                </span>
                <br />
                Simplifying Access
              </h2>
              <p className="mt-12 text-base text-muted-foreground">
                Designed to make managing your events and selling tickets
                straightforward and efficient. Everything you need to get up and
                running, from creating your event to managing sales and
                attendees, is within one intuitive system.
              </p>
            </div>
          </div>

          {/* Right Column - Scrollable Cards */}
          <div className="-mt-8 sm:-mt-12">
            {DATA.map((item, index) => (
              <div
                key={index}
                className="relative my-12 overflow-hidden rounded-lg bg-muted px-8 py-16 shadow-none sm:px-12 sm:py-24 lg:px-16 lg:py-32"
              >
                <div className="gap-4 sm:gap-6">
                  <div className="block shrink-0">
                    <Image
                      src={item.icon}
                      alt={item.title}
                      width={48}
                      height={48}
                      className="dark:invert"
                    />
                  </div>
                  <div className="absolute top-12 right-12 font-mono text-5xl">
                    0{index + 1}
                  </div>
                  <div className="mt-6">
                    <h4 className="mb-2 text-2xl font-semibold text-primary">
                      {item.title}
                    </h4>
                    {typeof item.description === 'string' ? (
                      <p className="mt-6 text-xs text-muted-foreground sm:text-base">
                        {item.description}
                      </p>
                    ) : (
                      item.description
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export { HowItWorksTimeline };
