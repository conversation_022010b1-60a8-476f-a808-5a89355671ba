import HowItWorkImage from '@/app/assets/icons/how-it-works.svg';
import { env } from '@/env';
import { Button } from '@repo/design-system/components/ui/button';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import { ArrowRightIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

const HowItWorksHero = () => {
  return (
    <section className="bg-background">
      <div className="container">
        <Card className="group relative w-full overflow-hidden rounded-4xl border border-none bg-muted shadow-none">
          <CardContent className="py-12 lg:px-18 lg:py-24">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="flex flex-col justify-center space-y-6">
                <h1 className="mt-3 text-8xl font-bold tracking-tighter text-foreground">
                  How It
                  <span className="bg-linear-to-b from-background to-foreground bg-clip-text text-transparent">
                    {' '}
                    Works
                  </span>
                </h1>

                <div className="mt-4 flex gap-2">
                  <Link href={`${env.NEXT_PUBLIC_APP_URL}/sign-up`}>
                    <Button className="h-13 w-fit rounded-full px-8 text-lg">
                      Get Started
                    </Button>
                  </Link>
                  <Link href="/features">
                    <Button
                      variant="outline"
                      className="size-13 -rotate-45 rounded-full transition-all ease-in-out hover:rotate-0"
                    >
                      <ArrowRightIcon />
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="absolute -right-70 -bottom-70 -rotate-45 transition-all ease-in-out group-hover:-rotate-0">
                <Image
                  src={HowItWorkImage}
                  className="size-150 md:size-220 dark:invert "
                  alt="How it works"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export { HowItWorksHero };
