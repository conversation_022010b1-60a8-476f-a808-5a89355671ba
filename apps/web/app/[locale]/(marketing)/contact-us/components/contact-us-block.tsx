import { FaFacebook, FaInstagram, FaYoutube, FaUsers } from 'react-icons/fa';

// Contact11 @ https://www.shadcnblocks.com/block/contact11
const ContactUsBlock = () => {
  return (
    <section>
      <div className="container">
        <h1 className="text-center text-4xl font-semibold tracking-tight sm:text-5xl">
          Contact us
        </h1>
        <p className="mt-4 text-center text-muted-foreground">
          We&apos;re here to help—reach out with any questions or feedback.
        </p>

        <div className="mt-8 flex gap-10 max-md:flex-col md:mt-12 md:divide-x">
          {/* Contact Information */}
          <div className="space-y-10 pr-10 md:gap-20">
            <div>
              <h2 className="text-lg font-semibold">Corporate office</h2>
              <p className="mt-3 font-medium tracking-tight text-muted-foreground">
                Omakase Software Sdn Bhd
                <br />
                201901001715 (1311041-W)
                <br />
                C25827778050
                <br />
                <br />
                17-1-1, Bayan <PERSON>,
                <br />
                Medan Kampung Relau,
                <br />
                11900 Bayan Lepas, Penang
                <br />
                Malaysia
              </p>
            </div>

            <div>
              <h2 className="text-lg font-semibold">Email us</h2>
              <div className="mt-3 space-y-2">
                <div>
                  <p className="text-primary">Inquiries</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="mt-3 tracking-tight text-muted-foreground hover:text-primary-red"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div>
                  <p className="text-primary">Support</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="mt-3 tracking-tight text-muted-foreground hover:text-primary-red"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-semibold">Follow us</h2>
              <div className="mt-3 flex gap-6">
                <a
                  href="https://www.facebook.com/TicketCARE/"
                  target="blank"
                  referrerPolicy="no-referrer"
                  className="text-muted-foreground hover:text-primary-red"
                >
                  <FaFacebook className="size-6" />
                </a>
                <a
                  href="https://www.facebook.com/groups/ticketcare"
                  target="blank"
                  referrerPolicy="no-referrer"
                  className="text-muted-foreground hover:text-primary-red"
                >
                  <FaUsers className="size-6" />
                </a>
                <a
                  href="https://www.instagram.com/TicketCARE.my/"
                  target="blank"
                  referrerPolicy="no-referrer"
                  className="text-muted-foreground hover:text-primary-red"
                >
                  <FaInstagram className="size-6" />
                </a>
                <a
                  href="https://www.youtube.com/@TicketCARE"
                  target="blank"
                  referrerPolicy="no-referrer"
                  className="text-muted-foreground hover:text-primary-red"
                >
                  <FaYoutube className="size-6" />
                </a>
              </div>
            </div>
          </div>

          {/* Inquiry Form */}
          <div className="flex-1">
            <iframe
              id="contact-us-47vmvw"
              title="inquiry-form"
              src="https://f.ticketcare.my/forms/contact-us-47vmvw"
              className="border-1 size-full min-h-[50dvh]"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export { ContactUsBlock };
