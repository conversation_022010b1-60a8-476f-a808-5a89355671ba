'use client';

import type { RoadmapTask } from '@/app/lib/actions/roadmap';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import type { FilterState } from '../components/roadmap-filters';

export function useRoadmapFilters(tasks: RoadmapTask[]) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current filter state from URL
  const filters: FilterState = useMemo(
    () => ({
      search: searchParams.get('search') || '',
      category: searchParams.get('category') || 'all',
      priority: searchParams.get('priority') || 'all',
    }),
    [searchParams]
  );

  // Update URL with new filter state
  const updateFilters = useCallback(
    (newFilters: FilterState) => {
      const params = new URLSearchParams();

      if (newFilters.search) {
        params.set('search', newFilters.search);
      }
      if (newFilters.category && newFilters.category !== 'all') {
        params.set('category', newFilters.category);
      }
      if (newFilters.priority && newFilters.priority !== 'all') {
        params.set('priority', newFilters.priority);
      }

      const queryString = params.toString();
      const newUrl = queryString ? `?${queryString}` : window.location.pathname;

      router.replace(newUrl, { scroll: false });
    },
    [router]
  );

  // Filter tasks based on current filter state
  const filteredTasks = useMemo(() => {
    return tasks.filter((task) => {
      // Search filter - check title and description
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const titleMatch = task.title.toLowerCase().includes(searchTerm);
        const descriptionMatch =
          task.description?.toLowerCase().includes(searchTerm) || false;

        if (!titleMatch && !descriptionMatch) {
          return false;
        }
      }

      // Category filter
      if (filters.category !== 'all' && task.category !== filters.category) {
        return false;
      }

      // Priority filter
      if (filters.priority !== 'all' && task.priority !== filters.priority) {
        return false;
      }

      return true;
    });
  }, [tasks, filters]);

  return {
    filters,
    filteredTasks,
    updateFilters,
    totalTasks: tasks.length,
    filteredCount: filteredTasks.length,
  };
}
