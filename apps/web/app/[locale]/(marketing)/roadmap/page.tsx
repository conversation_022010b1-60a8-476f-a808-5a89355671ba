import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import { RoadmapView } from './components/roadmap-view';

type RoadmapPageProps = {
  params: Promise<{
    locale: string;
  }>;
};

export async function generateMetadata({
  params,
}: RoadmapPageProps): Promise<Metadata> {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return {
    title: dictionary.web.roadmap.meta.title,
    description: dictionary.web.roadmap.meta.description,
    openGraph: {
      title: dictionary.web.roadmap.meta.title,
      description: dictionary.web.roadmap.meta.description,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: dictionary.web.roadmap.meta.title,
      description: dictionary.web.roadmap.meta.description,
    },
  };
}

export default async function RoadmapPage({ params }: RoadmapPageProps) {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return (
    <div className="container mx-auto px-4 my-10">
      {/* Hero Section */}
      <div className="mb-8 text-center sm:mb-12">
        <h1 className="mb-4 font-bold text-3xl tracking-tight sm:text-4xl lg:text-5xl">
          {dictionary.web.roadmap.hero.title}
        </h1>
        <p className="mx-auto max-w-2xl px-4 text-base text-muted-foreground sm:px-0 sm:text-lg">
          {dictionary.web.roadmap.hero.description}
        </p>
      </div>
      {/* Roadmap Content */}
      <RoadmapView dictionary={dictionary} />
    </div>
  );
}
