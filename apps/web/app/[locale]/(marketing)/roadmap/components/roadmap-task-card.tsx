'use client';

import type { RoadmapTask } from '@/app/lib/actions/roadmap';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
} from '@repo/design-system/components/ui/card';
import { KanbanCard } from '@repo/design-system/components/ui/kibo-ui/kanban';
import { cn } from '@repo/design-system/lib/utils';
import type { Dictionary } from '@repo/internationalization';

type RoadmapTaskCardProps = {
  task: RoadmapTask & { column: string; name: string };
  dictionary: Dictionary;
  isPublicView?: boolean;
};

export function RoadmapTaskCard({
  task,
  dictionary,
  isPublicView = false,
}: RoadmapTaskCardProps) {
  // Priority color mapping
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Category color mapping
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'feature':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'bug-fix':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'enhancement':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'infrastructure':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isPublicView) {
    // For public view, render as a static card without drag functionality
    return (
      <Card className="cursor-default transition-shadow hover:shadow-md touch-manipulation">
        <CardHeader className="pb-3 p-3 sm:p-4">
          <div className="flex items-start justify-between gap-2">
            <h4 className="line-clamp-2 font-medium text-sm sm:text-sm leading-tight">
              {task.title}
            </h4>
            <div className="flex flex-col gap-1 flex-shrink-0">
              <Badge
                variant="outline"
                className={cn(
                  'text-xs whitespace-nowrap',
                  getPriorityColor(task.priority)
                )}
              >
                {
                  dictionary.web.roadmap.priority[
                    task.priority as keyof typeof dictionary.web.roadmap.priority
                  ]
                }
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 p-3 sm:p-4 sm:pt-0">
          <div className="space-y-3">
            {task.description && (
              <p className="line-clamp-3 text-muted-foreground text-xs sm:text-xs">
                {task.description}
              </p>
            )}

            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
              <Badge
                variant="outline"
                className={cn('text-xs w-fit', getCategoryColor(task.category))}
              >
                {
                  dictionary.web.roadmap.category[
                    task.category as keyof typeof dictionary.web.roadmap.category
                  ]
                }
              </Badge>

              {task.estimatedTimeline && (
                <span className="text-muted-foreground text-xs">
                  {task.estimatedTimeline}
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // For admin view (future implementation), use KanbanCard with drag functionality
  return (
    <KanbanCard
      id={task.id}
      name={task.name}
      column={task.column}
      className="cursor-grab transition-shadow hover:shadow-md"
    >
      <div className="space-y-3">
        <div className="flex items-start justify-between gap-2">
          <h4 className="line-clamp-2 font-medium text-sm leading-tight">
            {task.title}
          </h4>
          <Badge
            variant="outline"
            className={cn('text-xs', getPriorityColor(task.priority))}
          >
            {
              dictionary.web.roadmap.priority[
                task.priority as keyof typeof dictionary.web.roadmap.priority
              ]
            }
          </Badge>
        </div>

        {task.description && (
          <p className="line-clamp-3 text-muted-foreground text-xs">
            {task.description}
          </p>
        )}

        <div className="flex items-center justify-between">
          <Badge
            variant="outline"
            className={cn('text-xs', getCategoryColor(task.category))}
          >
            {
              dictionary.web.roadmap.category[
                task.category as keyof typeof dictionary.web.roadmap.category
              ]
            }
          </Badge>

          {task.estimatedTimeline && (
            <span className="text-muted-foreground text-xs">
              {task.estimatedTimeline}
            </span>
          )}
        </div>
      </div>
    </KanbanCard>
  );
}
