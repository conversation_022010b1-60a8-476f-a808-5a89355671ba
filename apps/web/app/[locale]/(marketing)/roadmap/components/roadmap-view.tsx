'use client';

import { getRoadmapTasks } from '@/app/lib/actions/roadmap';
import type { RoadmapTask } from '@/app/lib/actions/roadmap';
import type { Dictionary } from '@repo/internationalization';
import { useEffect, useState } from 'react';
import { useRoadmapFilters } from '../hooks/use-roadmap-filters';
import { RequestSubmissionForm } from './request-submission-form';
import { RoadmapFilters } from './roadmap-filters';
import { RoadmapKanban } from './roadmap-kanban';

type RoadmapViewProps = {
  dictionary: Dictionary;
};

export function RoadmapView({ dictionary }: RoadmapViewProps) {
  const [tasks, setTasks] = useState<RoadmapTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { filters, filteredTasks, updateFilters, totalTasks, filteredCount } =
    useRoadmapFilters(tasks);

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true);
        const roadmapTasks = await getRoadmapTasks();
        setTasks(roadmapTasks);
      } catch (err) {
        console.error('Failed to fetch roadmap tasks:', err);
        setError('Failed to load roadmap tasks');
      } finally {
        setLoading(false);
      }
    };

    fetchTasks();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6 sm:space-y-8">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-3 sm:space-y-4">
                <div className="h-6 sm:h-8 rounded bg-muted" />
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, j) => (
                    <div key={j} className="h-20 sm:h-24 rounded bg-muted" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12 text-center">
        <p className="text-muted-foreground">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8 sm:space-y-12">
      {/* Filters */}
      <RoadmapFilters
        dictionary={dictionary}
        filters={filters}
        onFiltersChange={updateFilters}
        totalTasks={totalTasks}
        filteredCount={filteredCount}
      />

      {/* Kanban Board */}
      {filteredCount === 0 && totalTasks > 0 ? (
        <div className="py-8 sm:py-12 text-center">
          <p className="text-muted-foreground">
            {dictionary.web.roadmap.filters.results.noResults}
          </p>
        </div>
      ) : (
        <RoadmapKanban tasks={filteredTasks} dictionary={dictionary} />
      )}

      {/* Request Submission Form */}
      <div className="border-t pt-8 sm:pt-12">
        <RequestSubmissionForm dictionary={dictionary} />
      </div>
    </div>
  );
}
