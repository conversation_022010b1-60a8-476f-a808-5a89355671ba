'use client';

import type { RoadmapTask } from '@/app/lib/actions/roadmap';
import {
  KanbanBoard,
  Kanban<PERSON><PERSON>,
  Ka<PERSON><PERSON><PERSON>ead<PERSON>,
  KanbanProvider,
} from '@repo/design-system/components/ui/kibo-ui/kanban';
import type { Dictionary } from '@repo/internationalization';
import { RoadmapTaskCard } from './roadmap-task-card';

type RoadmapKanbanProps = {
  tasks: RoadmapTask[];
  dictionary: Dictionary;
};

type KanbanColumn = {
  id: string;
  name: string;
};

type KanbanTask = RoadmapTask & {
  column: string;
  name: string;
} & Record<string, unknown>;

export function RoadmapKanban({ tasks, dictionary }: RoadmapKanbanProps) {
  // Define the columns for the kanban board
  const columns: KanbanColumn[] = [
    {
      id: 'planned',
      name: dictionary.web.roadmap.columns.planned,
    },
    {
      id: 'in-progress',
      name: dictionary.web.roadmap.columns['in-progress'],
    },
    {
      id: 'in-review',
      name: dictionary.web.roadmap.columns['in-review'],
    },
    {
      id: 'completed',
      name: dictionary.web.roadmap.columns.completed,
    },
  ];

  // Transform tasks to match kanban structure
  const kanbanTasks: KanbanTask[] = tasks.map((task) => ({
    ...task,
    column: task.status,
    name: task.title,
  }));

  // Sort tasks by order within each column
  const sortedTasks = kanbanTasks.sort((a, b) => a.order - b.order);

  return (
    <div className="w-full">
      <KanbanProvider
        columns={columns}
        data={sortedTasks}
        className="min-h-[600px] grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 lg:gap-4"
        // Disable drag and drop for public view
        onDragStart={() => {}}
        onDragEnd={() => {}}
        onDragOver={() => {}}
      >
        {(column) => (
          <KanbanBoard
            id={column.id}
            className="min-h-[300px] sm:min-h-[400px] lg:min-h-[500px]"
          >
            <KanbanHeader className="border-b bg-background p-3 sm:p-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-base sm:text-sm">
                  {column.name}
                </h3>
                <span className="text-muted-foreground text-sm sm:text-xs">
                  {
                    sortedTasks.filter((task) => task.column === column.id)
                      .length
                  }
                </span>
              </div>
            </KanbanHeader>
            <KanbanCards id={column.id}>
              {(task: KanbanTask) => (
                <RoadmapTaskCard
                  key={task.id}
                  task={task}
                  dictionary={dictionary}
                  isPublicView={true}
                />
              )}
            </KanbanCards>
          </KanbanBoard>
        )}
      </KanbanProvider>
    </div>
  );
}
