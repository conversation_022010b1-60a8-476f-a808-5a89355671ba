'use client';

import {
  type PublicRequestInput,
  createPublicRequest,
} from '@/app/lib/actions/requests';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import type { Dictionary } from '@repo/internationalization';
import { useRef, useState } from 'react';
import { z } from 'zod';

type RequestSubmissionFormProps = {
  dictionary: Dictionary;
};

// Client-side validation schema
const requestSchema = z.object({
  title: z
    .string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters'),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description must be less than 2000 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  name: z
    .string()
    .max(100, 'Name must be less than 100 characters')
    .optional()
    .or(z.literal('')),
});

type RequestFormData = z.infer<typeof requestSchema>;

export function RequestSubmissionForm({
  dictionary,
}: RequestSubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastSubmissionTime, setLastSubmissionTime] = useState<number>(0);
  const { toast } = useToast();
  const formRef = useRef<HTMLFormElement>(null);

  const form = useForm<RequestFormData>({
    resolver: zodResolver(requestSchema),
    defaultValues: {
      title: '',
      description: '',
      email: '',
      name: '',
    },
  });

  // Simple client-side rate limiting (1 submission per 30 seconds)
  const isRateLimited = () => {
    const now = Date.now();
    const timeSinceLastSubmission = now - lastSubmissionTime;
    return timeSinceLastSubmission < 30000; // 30 seconds
  };

  const onSubmit = async (data: RequestFormData) => {
    if (isRateLimited()) {
      toast({
        title: 'Please wait',
        description: 'You can only submit one request every 30 seconds.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await createPublicRequest(data as PublicRequestInput);

      if (result.success) {
        toast({
          title: 'Success!',
          description: result.message,
        });

        // Reset form and update rate limiting
        form.reset();
        setLastSubmissionTime(Date.now());
      } else {
        toast({
          title: 'Error',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: 'Error',
        description: dictionary.web.roadmap.requestForm.error,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="mx-auto max-w-2xl">
      <CardHeader className="text-center p-4 sm:p-6">
        <CardTitle className="text-xl sm:text-2xl">
          {dictionary.web.roadmap.requestForm.title}
        </CardTitle>
        <CardDescription className="text-sm sm:text-base">
          {dictionary.web.roadmap.requestForm.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        <Form {...form}>
          <form
            ref={formRef}
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 sm:space-y-6"
          >
            {/* Title Field */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-sm">
                    {dictionary.web.roadmap.requestForm.fields.title}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={
                        dictionary.web.roadmap.requestForm.fields
                          .titlePlaceholder
                      }
                      className="h-10"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description Field */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-sm">
                    {dictionary.web.roadmap.requestForm.fields.description}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={
                        dictionary.web.roadmap.requestForm.fields
                          .descriptionPlaceholder
                      }
                      className="min-h-[100px] sm:min-h-[120px] resize-none"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Optional Fields */}
            <div className="grid gap-4 sm:gap-6 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium text-sm">
                      {dictionary.web.roadmap.requestForm.fields.name}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={
                          dictionary.web.roadmap.requestForm.fields
                            .namePlaceholder
                        }
                        className="h-10"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium text-sm">
                      {dictionary.web.roadmap.requestForm.fields.email}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder={
                          dictionary.web.roadmap.requestForm.fields
                            .emailPlaceholder
                        }
                        className="h-10"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-center pt-2 sm:pt-4">
              <Button
                type="submit"
                size="lg"
                disabled={isSubmitting || isRateLimited()}
                className="w-full sm:w-auto sm:min-w-[200px] h-11"
              >
                {isSubmitting
                  ? dictionary.web.roadmap.requestForm.submitting
                  : dictionary.web.roadmap.requestForm.submit}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
