'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import type { Dictionary } from '@repo/internationalization';
import { Search, X } from 'lucide-react';

export type FilterState = {
  search: string;
  category: string;
  priority: string;
};

type RoadmapFiltersProps = {
  dictionary: Dictionary;
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  totalTasks: number;
  filteredCount: number;
};

export function RoadmapFilters({
  dictionary,
  filters,
  onFiltersChange,
  totalTasks,
  filteredCount,
}: RoadmapFiltersProps) {
  // Helper to safely access dictionary properties
  const getCategory = (key: string) => {
    const categories = dictionary.web.roadmap.category as Record<
      string,
      string
    >;
    return categories[key] || key;
  };

  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, search: value });
  };

  const handleCategoryChange = (value: string) => {
    onFiltersChange({ ...filters, category: value });
  };

  const handlePriorityChange = (value: string) => {
    onFiltersChange({ ...filters, priority: value });
  };

  const clearFilters = () => {
    onFiltersChange({ search: '', category: 'all', priority: 'all' });
  };

  const hasActiveFilters =
    filters.search !== '' ||
    filters.category !== 'all' ||
    filters.priority !== 'all';

  return (
    <div className="space-y-4 sm:space-y-6 rounded-lg border bg-card p-4 sm:p-6">
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h3 className="font-semibold text-base sm:text-lg">
          {dictionary.web.roadmap.filters.title}
        </h3>
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="h-9 w-full sm:h-8 sm:w-auto"
          >
            <X className="mr-1 h-3 w-3" />
            {dictionary.web.roadmap.filters.clear}
          </Button>
        )}
      </div>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {/* Search Input */}
        <div className="space-y-2 sm:col-span-2 lg:col-span-1">
          <Label htmlFor="search" className="text-sm">
            {dictionary.web.roadmap.filters.search.label}
          </Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              id="search"
              type="text"
              placeholder={dictionary.web.roadmap.filters.search.placeholder}
              value={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-9 h-10"
            />
          </div>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <Label htmlFor="category" className="text-sm">
            {dictionary.web.roadmap.filters.category.label}
          </Label>
          <Select value={filters.category} onValueChange={handleCategoryChange}>
            <SelectTrigger id="category" className="h-10">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {dictionary.web.roadmap.filters.category.all}
              </SelectItem>
              <SelectItem value="feature">{getCategory('feature')}</SelectItem>
              <SelectItem value="bug-fix">{getCategory('bug-fix')}</SelectItem>
              <SelectItem value="enhancement">
                {getCategory('enhancement')}
              </SelectItem>
              <SelectItem value="infrastructure">
                {getCategory('infrastructure')}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Priority Filter */}
        <div className="space-y-2">
          <Label htmlFor="priority" className="text-sm">
            {dictionary.web.roadmap.filters.priority.label}
          </Label>
          <Select value={filters.priority} onValueChange={handlePriorityChange}>
            <SelectTrigger id="priority" className="h-10">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {dictionary.web.roadmap.filters.priority.all}
              </SelectItem>
              <SelectItem value="high">
                {dictionary.web.roadmap.priority.high}
              </SelectItem>
              <SelectItem value="medium">
                {dictionary.web.roadmap.priority.medium}
              </SelectItem>
              <SelectItem value="low">
                {dictionary.web.roadmap.priority.low}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Results Summary */}
      <div className="border-t pt-3 sm:pt-4">
        <p className="text-muted-foreground text-sm">
          {dictionary.web.roadmap.filters.results.showing
            .replace('{{count}}', filteredCount.toString())
            .replace('{{total}}', totalTasks.toString())}
        </p>
      </div>
    </div>
  );
}
