'use client';

import integratingImage from '@/app/assets/showcase/integrating.svg';
import managingImage from '@/app/assets/showcase/managing.svg';
import promotingImage from '@/app/assets/showcase/promoting.svg';
import reportingImage from '@/app/assets/showcase/reporting.svg';
import scanningImage from '@/app/assets/showcase/scanning.svg';
import ticketingImage from '@/app/assets/showcase/ticketing.svg';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { ArrowRight, CheckCircle2 } from 'lucide-react';
import Image from 'next/image';

const slides = [
  {
    id: 1,
    tabName: 'Ticketing',
    title: 'Sell tickets seamlessly, anytime, anywhere',
    description:
      'Maximize your sales with a flexible and global ticketing system designed for every event type, from small workshops to massive festivals.',
    features: [
      'Global box office',
      'Secure & compliant',
      'Flexible ticket types',
      'Branded experience',
    ],
    link: '/features/event-ticketing',
    image: ticketingImage,
    linkText: 'Explore Ticketing',
  },
  {
    id: 2,
    tabName: 'Promoting',
    title: 'Boost your event with powerful marketing tools',
    description:
      'Fill your seats with our suite of built-in marketing and promotional features designed to drive awareness and conversions.',
    features: [
      'Email marketing',
      'Discount & promo codes',
      'Partner programs',
      'Social media integration',
    ],
    link: '/features/event-marketing-and-promotion',
    image: promotingImage,
    linkText: 'Explore Promoting',
  },
  {
    id: 3,
    tabName: 'Managing',
    title: 'Effortlessly organize attendees and events',
    description:
      'Take control of the chaos with intuitive tools that simplify attendee management and event day operations.',
    features: [
      'Centralized dashboard',
      'Team coordination',
      'Guest list management',
      'Mobile event app',
    ],
    link: '/features/event-management',
    image: managingImage,
    linkText: 'Explore Managing',
  },
  {
    id: 4,
    tabName: 'Scanning',
    title: 'Fast, secure, and reliable check-in solutions',
    description:
      'Ensure a smooth and memorable first impression with our industry-leading check-in technology.',
    features: [
      'Offline functionality',
      'Duplicate detection',
      'High-speed scanning',
      'Real-time sync',
    ],
    link: '/features/ticket-scanning',
    image: scanningImage,
    linkText: 'Explore Scanning',
  },
  {
    id: 5,
    tabName: 'Reporting',
    title: 'Real-time insights to track your success',
    description:
      "Make data-driven decisions with comprehensive analytics and easy-to-export reports on your event's performance.",
    features: [
      'Live sales dashboard',
      'Marketing ROI reports',
      'Attendee analytics',
      'Custom export tools',
    ],
    link: '/features/reporting-analytics',
    image: reportingImage,
    linkText: 'Explore Reporting',
  },
  {
    id: 6,
    tabName: 'Integrating',
    title: 'Connect with your favorite tools & platforms',
    description:
      'We seamlessly fit into your existing workflow, connecting your event data with the software you already use and love.',
    features: [
      'CRM & marketing automation',
      'Analytics & productivity',
      'Payment processors',
      'Robust API',
    ],
    link: '/features/modules-integration',
    image: integratingImage,
    linkText: 'Explore Integrating',
  },
];

const FeatureShowcase = () => {
  return (
    <section>
      <div className="container">
        <div className="mx-auto flex max-w-3xl flex-col items-center gap-6">
          <h2 className="text-center text-3xl font-semibold lg:text-5xl">
            All-In-One Event Management
          </h2>
          <p className="text-muted-foreground text-balance text-center lg:text-xl">
            Streamline your entire event lifecycle with a single, powerful
            platform. From selling the first ticket to post-event analytics, we
            give you more time to create unforgettable experiences.
          </p>
        </div>
        <div className="mt-12">
          <Tabs
            defaultValue="1"
            className="mx-auto flex w-fit flex-col items-center gap-8 md:gap-12 max-w-full"
          >
            <TabsList className="w-full md:w-fit flex h-auto gap-x-2 p-2 overflow-x-auto justify-start md:justify-center">
              {slides.map((slide) => (
                <TabsTrigger
                  key={slide.id}
                  value={slide.id.toString()}
                  className="hover:bg-background text-sm md:text-base shrink-0"
                  onClick={(e) => {
                    e.currentTarget.scrollIntoView({
                      behavior: 'smooth',
                      block: 'nearest',
                      inline: 'center',
                    });
                  }}
                >
                  {slide.tabName}
                </TabsTrigger>
              ))}
            </TabsList>
            {slides.map((slide) => (
              <TabsContent
                value={slide.id.toString()}
                key={slide.id}
                className="max-w-5xl"
              >
                <div className="grid grid-cols-1 items-center gap-10 md:grid-cols-2">
                  <div>
                    <h2 className="mb-4 text-2xl font-semibold lg:text-4xl">
                      {slide.title}
                    </h2>
                    <p className="text-muted-foreground lg:text-xl">
                      {slide.description}
                    </p>
                    <ul className="mt-8 grid grid-cols-1 gap-2 lg:grid-cols-2">
                      {slide.features.map((feature) => (
                        <li key={feature} className="flex items-center gap-2">
                          <CheckCircle2 className="w-4" />
                          <span className="font-medium">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <Button variant="outline" size="sm" asChild>
                      <a href={slide.link} className="mt-8">
                        {slide.linkText}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                  <div className="order-first w-full aspect-[800/600] rounded-lg object-cover md:order-last relative dark:bg-foreground">
                    <Image src={slide.image} alt={slide.title} fill />
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </div>
    </section>
  );
};

export { FeatureShowcase };
