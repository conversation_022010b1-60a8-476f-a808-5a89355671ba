import checkoutflowIcon from '@/app/assets/module/checkoutflow.svg';
import complysyncIcon from '@/app/assets/module/complysync.svg';
import crowdconnectIcon from '@/app/assets/module/crowdconnect.svg';
import fankeeperIcon from '@/app/assets/module/fankeeper.svg';
import gearloungeIcon from '@/app/assets/module/gearlounge.svg';
import impactsparkIcon from '@/app/assets/module/impactspark.svg';
import innercircleIcon from '@/app/assets/module/innercircle.svg';
import passportIcon from '@/app/assets/module/passport.svg';
import reachstudioIcon from '@/app/assets/module/reachstudio.svg';
import seatsmartIcon from '@/app/assets/module/seatsmart.svg';
import { Button } from '@repo/design-system/components/ui/button';
import { cn } from '@repo/design-system/lib/utils';
import Image from 'next/image';
import Link from 'next/link';
import type { ReactNode } from 'react';

interface Integration {
  id: string;
  icon: ReactNode;
  url: string;
}

interface ModulesSummaryProps {
  heading?: string;
  description?: string;
  integrations?: Integration[][];
}

const ModulesSummary = ({
  heading = '👋 Event magic, minus the headache',
  description = 'No more ticket tantrums (and the overpriced fees)—just happy organizers & happier crowds.',
  integrations = [
    [
      {
        id: 'module-impactspark',
        icon: <Image src={impactsparkIcon} alt="ImpactSpark Module" fill />,
        url: '/modules/impactspark',
      },
      {
        id: 'module-seatsmart',
        icon: <Image src={seatsmartIcon} alt="SeatSmart Module" fill />,
        url: '/modules/seatsmart',
      },
      {
        id: 'module-checkoutflow',
        icon: <Image src={checkoutflowIcon} alt="CheckoutFlow Module" fill />,
        url: '/modules/checkoutflow',
      },
      {
        id: 'module-complysync',
        icon: <Image src={complysyncIcon} alt="ComplySync Module" fill />,
        url: '/modules/complysync',
      },
    ],
    [
      {
        id: 'module-reachstudio',
        icon: <Image src={reachstudioIcon} alt="ReachStudio Module" fill />,
        url: '/modules/reachstudio',
      },
      {
        id: 'module-crowdconnect',
        icon: <Image src={crowdconnectIcon} alt="CrowdConnect Module" fill />,
        url: '/modules/crowdconnect',
      },
      {
        id: 'module-fankeeper',
        icon: <Image src={fankeeperIcon} alt="FanKeeper Module" fill />,
        url: '/modules/fankeeper',
      },
    ],
    [
      {
        id: 'module-passport',
        icon: <Image src={passportIcon} alt="Passport Module" fill />,
        url: '/modules/passport',
      },
      {
        id: 'module-gearlounge',
        icon: <Image src={gearloungeIcon} alt="GearLounge Module" fill />,
        url: '/modules/gearlounge',
      },

      {
        id: 'module-innercircle',
        icon: <Image src={innercircleIcon} alt="InnerCircle Module" fill />,
        url: '/modules/innercircle',
      },
    ],
  ],
}: ModulesSummaryProps) => {
  return (
    <section className="relative overflow-hidden">
      {/* background image */}
      <div className="absolute inset-x-0 top-0 flex h-full w-full items-center justify-center opacity-100">
        <div className="relative h-full w-full">
          <Image
            alt="background"
            src="https://deifkwefumgah.cloudfront.net/shadcnblocks/block/patterns/square-alt-grid.svg"
            className="opacity-90 [mask-image:radial-gradient(75%_75%_at_center,white,transparent)]"
            fill
          />
        </div>
      </div>
      {/* content */}
      <div className="relative">
        <div className="md:-space-x-26 lg:container relative flex flex-col items-start md:flex-row md:items-center">
          <div className="bg-background z-20 md:-mx-4 w-full shrink-0 px-4 pt-32 md:w-1/2 md:bg-transparent md:pb-32">
            <div className="flex flex-col items-center text-center md:items-start md:text-left">
              <div className="max-w-sm">
                <h1 className="my-6 text-pretty font-bold hero-text">
                  {heading}
                </h1>
                <p className="text-muted-foreground">{description}</p>
                <Button asChild size="lg" className="mt-10">
                  <Link href="/event-organizers">Start selling tickets</Link>
                </Button>
                <Button asChild size="lg" className="mt-10" variant="link">
                  <Link href="/events">Discover events</Link>
                </Button>
              </div>
            </div>
          </div>
          <div className="z-20 w-full">
            <div className="flex flex-col gap-10 lg:gap-16 pb-8 pt-12 lg:py-32">
              {integrations.map((line, i) => (
                <div
                  key={i}
                  className={cn(
                    'gap-x-4 sm:gap-x-10 lg:gap-x-22 flex justify-center lg:justify-start',
                    i === 0 && 'md:-translate-x-11 lg:-translate-x-22',
                    i === 1 && 'md:-translate-x-6 lg:translate-x-0',
                    i === 2 && 'md:translate-x-11 lg:translate-x-22'
                  )}
                >
                  {line.map((integration) => (
                    <Link key={integration.id} href={integration.url}>
                      <div className="size-16 lg:size-22 border-background bg-background dark:bg-foreground rounded-xl border shadow-xl items-center flex justify-center">
                        <div className="bg-muted/20 dark:bg-foreground/20 size-10 lg:size-14 p-4 relative">
                          {integration.icon}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export { ModulesSummary };
