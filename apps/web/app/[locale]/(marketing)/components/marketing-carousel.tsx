'use client';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@repo/design-system/components/ui/carousel';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface feature {
  title: string;
  description: string;
  background: string;
}

const FEATURES: feature[] = [
  {
    title: 'Nonprofit & Fundraising',
    description:
      'Streamlined ticketing solutions that turn supporters into donors.',
    background:
      'https://media.360hq.my/file/carecdn/jpg-nonprofit-fundraising.jpg',
  },
  {
    title: 'Schools & Education',
    description:
      'Sell tickets for performances, proms, and fundraisers effortlessly.',
    background: 'https://media.360hq.my/file/carecdn/jpg-schools-education.jpg',
  },
  {
    title: 'Religious & Places of Worship',
    description:
      'Handle event calendars for retreats, concerts, and community services.',
    background:
      'https://media.360hq.my/file/carecdn/jpg-religious-places-of-worship.jpg',
  },
  {
    title: 'Community & Classes',
    description:
      'Effortlessly manage classes, workshops, and local gatherings.',
    background: 'https://media.360hq.my/file/carecdn/jpg-community-classes.jpg',
  },
  {
    title: 'Venues & Festivals',
    description: 'Robust features for high-volume series and marketing.',
    background: 'https://media.360hq.my/file/carecdn/jpg-venues-festivals.jpg',
  },
  {
    title: 'Arts & Culture',
    description:
      'Tools built for seasons, subscriptions, and captivating your audience.',
    background: 'https://media.360hq.my/file/carecdn/jpg-arts-culture.jpg',
  },
  {
    title: 'And Beyond',
    description:
      'Flexible options for innovative organizers in attractions, sports, tourism, and etc.',
    background: 'https://media.360hq.my/file/carecdn/jpg-and-beyond.jpg',
  },
];

const MarketingCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const goToSlide = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <section>
      <div className="container">
        <div className="flex w-full flex-col items-center justify-center gap-4">
          <h2 className="mx-auto text-center font-medium leading-none hero-text md:max-w-[60vw]">
            Powerful ticketing for organizers who care
          </h2>
        </div>
        <Carousel
          opts={{
            align: 'start',
          }}
          active={FEATURES.length > 1}
          className="w-full"
          setApi={setApi}
        >
          <CarouselContent className="-ml-8 pt-16">
            {FEATURES.map((card, index) => (
              <CarouselItem
                key={index}
                className="pl-8 md:basis-1/2 lg:basis-1/3"
              >
                <div className="p-1">
                  <Card className="border-0 shadow-none">
                    <CardContent className="flex flex-col p-0">
                      <div className="relative flex aspect-[0.935802469] w-full flex-col items-center justify-between overflow-hidden rounded-2xl bg-cover bg-center bg-no-repeat">
                        <div className="flex size-full flex-1 relative">
                          <Image src={card.background} alt="" fill />
                        </div>
                      </div>
                      <div className="flex w-full flex-col gap-1 pt-6">
                        <h3 className="text-foreground text-xl font-medium">
                          {card.title}
                        </h3>
                        <p className="text-sm">{card.description}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="mt-4 hidden items-center justify-center gap-4 md:flex">
            <CarouselPrevious className="static size-12 translate-y-0" />
            <CarouselNext className="static size-12 translate-y-0" />
          </div>
          <div className="mt-6 flex w-full items-center gap-2 md:hidden">
            {Array.from({ length: FEATURES.length }).map((_, i) => (
              <button
                type="button"
                onClick={() => goToSlide(i % FEATURES.length)}
                key={`carousel-btn-${i}`}
                className="h-4 w-full"
              >
                <div
                  className={` ${current === i + 1 ? 'bg-primary' : 'bg-muted'} my-auto h-1 w-full rounded-full`}
                />
              </button>
            ))}
          </div>
        </Carousel>
      </div>
    </section>
  );
};

export { MarketingCarousel };
