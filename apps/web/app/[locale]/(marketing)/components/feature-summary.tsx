import controlIcon from '@/app/assets/icons/control.svg';
import integrationsIcon from '@/app/assets/icons/integrations.svg';
import intelligenceIcon from '@/app/assets/icons/intelligence.svg';
import nurtureIcon from '@/app/assets/icons/nurture.svg';
import savingsIcon from '@/app/assets/icons/savings.svg';
import supportIcon from '@/app/assets/icons/support.svg';
import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Medal } from 'lucide-react';
import Image from 'next/image';

const FeatureSummary = () => {
  return (
    <section>
      <div className="container">
        <div className="mx-auto flex max-w-3xl flex-col items-center gap-4">
          <Badge
            variant="outline"
            className="flex items-center gap-1 px-2.5 py-1.5 text-sm"
          >
            <Medal className="h-auto w-4" />
            Highlights
          </Badge>
          <h2 className="text-center text-3xl font-semibold lg:text-4xl">
            Why use TicketCARE?
          </h2>
          <p className="text-center text-muted-foreground lg:text-lg">
            Your community is the mission. We're the technology and support team
            behind it.
          </p>
        </div>
        <div className="gap mt-14 grid gap-2.5 lg:grid-cols-3">
          <div className="flex flex-col gap-2.5">
            <div className="gap flex flex-col gap-3 rounded-lg border p-6 flex-1">
              <div className="flex items-center gap-2.5">
                <span className="flex size-12 shrink-0 items-center justify-center rounded-md bg-muted aspect-square dark:bg-primary">
                  <Image
                    src={savingsIcon}
                    alt="Savings"
                    width={32}
                    height={32}
                  />
                </span>
                <h3 className="font-medium">
                  Keep more of your hard-earned revenue
                </h3>
              </div>
              <p className="text-sm text-muted-foreground md:text-base">
                Simple, predictable pricing. No hidden fees, no monthly
                contracts, and no taking a cut of your payment processing. You
                connect your own payment gateway and keep 100%. We only charge a
                flat, industry-low fee per ticket.
              </p>
            </div>
            <div className="gap flex flex-col gap-3 rounded-lg border p-6 flex-1">
              <div className="flex items-center gap-2.5">
                <span className="flex size-12 shrink-0 items-center justify-center rounded-md bg-muted aspect-square dark:bg-primary">
                  <Image
                    src={integrationsIcon}
                    alt="Integrations"
                    width={32}
                    height={32}
                  />
                </span>
                <h3 className="font-medium">Do more than just sell tickets</h3>
              </div>
              <p className="text-sm text-muted-foreground md:text-base">
                Why juggle a dozen different apps? TicketCARE brings your entire
                event ecosystem—ticketing, memberships, donations, merch, and
                supporter management—into one powerful, seamless platform.
              </p>
            </div>
            <div className="gap flex flex-col gap-3 rounded-lg border p-6 flex-1">
              <div className="flex items-center gap-2.5">
                <span className="flex size-12 shrink-0 items-center justify-center rounded-md bg-muted aspect-square dark:bg-primary">
                  <Image
                    src={intelligenceIcon}
                    alt="Intelligence"
                    width={32}
                    height={32}
                  />
                </span>
                <h3 className="font-medium">
                  Intelligence that drives success
                </h3>
              </div>
              <p className="text-sm text-muted-foreground md:text-base">
                Leverage AI-powered tools to make every event a bigger hit. With
                integrated modules, we give you the insights and automation to
                make every decision smarter.
              </p>
            </div>
          </div>

          <div className="flex items-center px-16">
            <Image
              src={lightLogo}
              alt="Logo"
              width={445}
              height={128}
              className="block w-full rounded-lg object-cover dark:lg:hidden"
            />
            <Image
              src={darkLogo}
              alt="Logo"
              width={445}
              height={128}
              className="hidden w-full rounded-lg object-cover dark:lg:block"
            />
          </div>

          <div className="flex flex-col gap-2.5">
            <div className="gap flex flex-col gap-3 rounded-lg border p-6 flex-1">
              <div className="flex items-center gap-2.5">
                <span className="flex size-12 shrink-0 items-center justify-center rounded-md bg-muted aspect-square dark:bg-primary">
                  <Image
                    src={controlIcon}
                    alt="Control"
                    width={32}
                    height={32}
                  />
                </span>
                <h3 className="font-medium">You control the entire journey</h3>
              </div>
              <p className="text-sm text-muted-foreground md:text-base">
                From a customizable checkout to your own branded app experience,
                your event's identity stays front and center. You own your
                customer data—it's your audience, not ours. We never market to
                your attendees or dilute your brand.
              </p>
            </div>
            <div className="gap flex flex-col gap-3 rounded-lg border p-6 flex-1">
              <div className="flex items-center gap-2.5">
                <span className="flex size-12 shrink-0 items-center justify-center rounded-md bg-muted aspect-square dark:bg-primary">
                  <Image
                    src={nurtureIcon}
                    alt="Nurture"
                    width={32}
                    height={32}
                  />
                </span>
                <h3 className="font-medium">
                  Nurture your tribe, not just your transactions
                </h3>
              </div>
              <p className="text-sm text-muted-foreground md:text-base">
                We're built for organizers who value relationships over one-time
                sales. With modules, you can turn casual attendees into lifelong
                superfans and reliable recurring revenue, all while owning every
                piece of your customer data.
              </p>
            </div>
            <div className="gap flex flex-col gap-3 rounded-lg border p-6 flex-1">
              <div className="flex items-center gap-2.5">
                <span className="flex size-12 shrink-0 items-center justify-center rounded-md bg-muted aspect-square dark:bg-primary">
                  <Image
                    src={supportIcon}
                    alt="Support"
                    width={32}
                    height={32}
                  />
                </span>
                <h3 className="font-medium">Real help from real people</h3>
              </div>
              <p className="text-sm text-muted-foreground md:text-base">
                Your event doesn't sleep, and neither does our support. Get
                expert help from our dedicated team anytime. We're here to
                ensure you and your attendees have a flawless experience.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export { FeatureSummary };
