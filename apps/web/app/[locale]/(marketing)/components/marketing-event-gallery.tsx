'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { motion } from 'motion/react';
import Image from 'next/image';
import Link from 'next/link';

interface GalleryImage {
  src: string;
  rotation: number;
  translateY?: number;
  translateX?: number;
  zIndex?: number;
}

const galleryImages: GalleryImage[] = [
  {
    src: 'https://media.360hq.my/file/carecdn/jpg-landscape2.jpg',
    rotation: -19,
    translateX: -200,
  },
  {
    src: 'https://media.360hq.my/file/carecdn/jpg-landscape1.jpg',
    rotation: -19,
    translateY: 2,
    translateX: -104,
  },
  {
    src: 'https://media.360hq.my/file/carecdn/jpg-landscape3.jpg',
    rotation: 0,
    translateY: -32,
    zIndex: 10,
  },
  {
    src: 'https://media.360hq.my/file/carecdn/jpg-landscape4.jpg',
    rotation: 12,
    translateX: 104,
    translateY: 2,
  },
  {
    src: 'https://media.360hq.my/file/carecdn/jpg-landscape5.jpg',
    rotation: 12,
    translateX: 200,
  },
];

const MarketingEventGallery = () => {
  return (
    <section>
      <div className="container overflow-x-clip pt-16">
        <motion.div
          initial={{ opacity: 0, translateY: 100 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{
            duration: 0.7,
            type: 'spring',
            bounce: 0.3,
          }}
          className="relative flex h-42 items-center justify-center"
        >
          {galleryImages.map((image, index) => (
            <motion.div
              initial={{
                rotate: image.rotation,
                translateY: image.translateY || 0,
                translateX: 0,
                zIndex: image.zIndex || 'auto',
              }}
              animate={{
                rotate: image.rotation,
                translateY: image.translateY || 0,
                translateX: image.translateX || 0,
                zIndex: image.zIndex || 'auto',
              }}
              drag
              dragSnapToOrigin
              whileHover={{
                scale: 1.15,
                rotate: 0,
                zIndex: 999,
                translateY: -20,
              }}
              transition={{
                duration: 0.7,
                bounce: 0.3,
                type: 'spring',
                delay: 0.5,
                translateY: { duration: 0.2 },
                rotate: { duration: 0.2 },
                scale: { duration: 0.2 },
                zIndex: { duration: 0.2 },
              }}
              key={index}
              className="absolute size-42 cursor-pointer overflow-hidden rounded-3xl shadow-xl transition-all ease-out"
            >
              <div className="relative size-42">
                <Image
                  src={image.src}
                  className="pointer-events-none size-full object-cover"
                  alt={`Gallery 20 - Image ${index + 1}`}
                  fill
                />
              </div>
            </motion.div>
          ))}
        </motion.div>
        <motion.div
          initial={{ opacity: 0, translateY: -20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{
            duration: 0.3,
            delay: 0.5,
          }}
          className="mt-10 flex flex-col items-center justify-center"
        >
          <h3 className="max-w-md text-center text-2xl font-medium tracking-tight">
            From concerts to comedies, find what moves you.
          </h3>
          <Link href="/events">
            <Button className="group mt-10 flex items-center justify-center gap-2 border px-4 py-1 tracking-tight">
              Discover live events
              <ArrowRight className="size-4 -rotate-45 transition-all ease-out group-hover:rotate-0" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export { MarketingEventGallery };
