import { Endorsements } from '@/app/[locale]/(marketing)/event-organizers/components/endorsements';
import { EventOrgenizersHero } from '@/app/[locale]/(marketing)/event-organizers/components/event-organizers-hero';
import { FeaturesHighlight } from '@/app/[locale]/(marketing)/event-organizers/components/features-highlight';
import OperationIcon from '@/app/assets/module/operations.svg';
import RevenueIcon from '@/app/assets/module/revenue.svg';
import ManagingIcon from '@/app/assets/navbar/managing.svg';
import PromotingIcon from '@/app/assets/navbar/promoting.svg';
import ReportingIcon from '@/app/assets/navbar/reporting.svg';
import IntegratingImage from '@/app/assets/showcase/integrating.svg';
import ManagingImage from '@/app/assets/showcase/managing.svg';
import PromotingImage from '@/app/assets/showcase/promoting.svg';
import ReportingImage from '@/app/assets/showcase/reporting.svg';
import ScanningImage from '@/app/assets/showcase/scanning.svg';

// Advanced control & customization features
const controlFeatures = [
  {
    title:
      'Gain complete control to customize all aspects of your event listings online',
    href: '/',
  },
  {
    title:
      'Customize images to match your brand identity and integrate your logo seamlessly',
    href: '/',
  },
  {
    title:
      'Manage your operations efficiently by setting up additional users with varying account permissions',
    href: '/',
  },
  {
    title:
      'Leverage CheckoutFlow to build a branded checkout experience and connect your own payment processor',
    href: '/',
  },
];

// Revenue maximization & upselling features
const revenueFeatures = [
  {
    title:
      'Launch SeatSmart to map your venue and dynamically optimize your pricing strategy',
    href: '/',
  },
  {
    title:
      'Integrate ImpactSpark into your checkout process to add a seamless donation option',
    href: '/',
  },
  {
    title:
      'Implement Passport membership passes to unlock exclusive access and recurring revenue',
    href: '/',
  },
  {
    title:
      'Launch a dedicated merch store with GearLounge to sell exclusive items and turn fans into superfans',
    href: '/',
  },
];

// Intelligent marketing & audience engagement features
const marketingFeatures = [
  {
    title:
      'Use ReachStudio to design beautiful communications and automate your messaging to optimize every send',
    href: '/',
  },
  {
    title:
      'Harness the power of promo codes for time-limited offers to generate pre-event hype',
    href: '/',
  },
  {
    title:
      'Benefit from automatic remarketing tools to ensure your ticket sales are fully optimized without extra effort',
    href: '/',
  },
  {
    title:
      "Boost engagement with CrowdConnect's powerful in-event interactive tools",
    href: '/',
  },
];

// Data-driven insights & relationship management features
const insightsFeatures = [
  {
    title:
      'Access real-time data on sales and marketing effectiveness to make informed decisions that drive growth',
    href: '/',
  },
  {
    title:
      'Download your complete customer data to share insights with your team and craft personalized offers',
    href: '/',
  },
  {
    title:
      'FanKeeper helps you understand your supporters better so you can segment audiences to cultivate lifelong fans',
    href: '/',
  },
  {
    title:
      'Manage ambassadors, automate commissions, and track performance with InnerCircle',
    href: '/',
  },
];

// Streamlined operations & seamless experience features
const operationsFeatures = [
  {
    title:
      'Integrate your box office seamlessly into your website, matching your branding with improved conversion',
    href: '/',
  },
  {
    title:
      'Streamline front-of-house with robust solutions, from simple door lists to automated scanning for any event size',
    href: '/',
  },
  {
    title:
      'Simplify accounting with detailed sales reports that break down income after your event',
    href: '/',
  },
  {
    title:
      'ComplySync automates LHDN MyInvois compliance receipts for every transaction',
    href: '/',
  },
];

const highlightSections = [
  {
    title: 'Advanced control & customization',
    icon: ManagingIcon,
    image: IntegratingImage,
    features: controlFeatures,
  },
  {
    title: 'Revenue maximization & upselling',
    icon: RevenueIcon,
    image: ReportingImage,
    features: revenueFeatures,
  },
  {
    title: 'Intelligent marketing & audience engagement',
    icon: PromotingIcon,
    image: PromotingImage,
    features: marketingFeatures,
  },
  {
    title: 'Data-driven insights & relationship management',
    icon: ReportingIcon,
    image: ScanningImage,
    features: insightsFeatures,
  },
  {
    title: 'Streamlined operations & seamless experience',
    icon: OperationIcon,
    image: ManagingImage,
    features: operationsFeatures,
  },
];

export default function EventOrganizersPage() {
  return (
    <div className="space-y-36 my-10">
      <EventOrgenizersHero />
      {highlightSections.map((section, index) => (
        <FeaturesHighlight
          key={index}
          title={section.title}
          icon={section.icon}
          image={section.image}
          features={section.features}
        />
      ))}
      <Endorsements />
    </div>
  );
}
