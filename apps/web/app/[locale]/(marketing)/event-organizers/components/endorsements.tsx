'use client';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@repo/design-system/components/ui/carousel';
import { Separator } from '@repo/design-system/components/ui/separator';
import { cn } from '@repo/design-system/lib/utils';
import AutoScroll from 'embla-carousel-auto-scroll';
import Image from 'next/image';

const Endorsements = () => {
  const logos = [
    {
      id: 'logo-1',
      description: 'CIH Alumni',
      image: 'https://media.360hq.my/file/carecdn/png-logo-clhsalumni.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-2',
      description: 'Pure Lotus Hospice',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-purelotushospice.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-3',
      description: 'Pek Ee <PERSON>',
      image: 'https://media.360hq.my/file/carecdn/png-logo-pekeetong.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-4',
      description: '<PERSON><PERSON> Jetavana',
      image: 'https://media.360hq.my/file/carecdn/png-logo-qiyuanjetavana.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-5',
      description: 'Beow Hiang Lim',
      image: 'https://media.360hq.my/file/carecdn/png-logo-beowhianglim.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-6',
      description: 'Than Hsiang',
      image: 'https://media.360hq.my/file/carecdn/png-logo-thanhsiang.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-7',
      description: 'Malaysian Craft Council',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-malaysiancraftcouncil.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-8',
      description: 'Pun Khib Siscus Club',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-pinkhibiscusclub.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-9',
      description: 'Soroptimist International',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-soroptimistinternational.png',
      className: 'h-18 w-auto',
    },
    {
      id: 'logo-10',
      description: 'Penang Philharmonic Orchestra',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-noiseperformancehouse.png',
      className: 'h-18 w-auto',
    },
  ];

  const testimonials = [
    {
      quote:
        'We were amazed at how effortlessly the rapid-ticketing feature adapted to our needs. It felt like the platform truly understood our ambitious vision and empowered us to execute it flawlessly.',
      name: 'Arja Lee, Malaysian Craft Council',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-malaysiancraftcouncil.png',
    },
    {
      quote:
        'Manual seat assignment for offline orders is invaluable for our musical concert. It allows us to efficiently allocate tickets for VIP guests. This feature perfectly accommodates our need for personalized ticketing arrangements.',
      name: 'Venerable Kai Xun, Pek Ee Tong',
      image: 'https://media.360hq.my/file/carecdn/png-logo-pekeetong.png',
    },
    {
      quote:
        'The in-event raffle ticket tool was a fantastic addition to our Soroptimist Christmas Fair. An engaging and exciting experience for all our attendees to our fundraising.',
      name: 'Joyce Lee, Soroptimist International Club of Penang',
      image:
        'https://media.360hq.my/file/carecdn/png-logo-soroptimistinternational.png',
    },
  ];

  return (
    <section>
      <div className="container flex flex-col items-center text-center">
        <h1 className="text-foreground my-6 text-3xl font-semibold tracking-tighter lg:text-5xl">
          The ticketing platform built for your mission
        </h1>
      </div>

      <div className="relative mx-auto flex items-center justify-center pt-8 lg:max-w-5xl">
        <Carousel
          opts={{ loop: true }}
          plugins={[AutoScroll({ playOnInit: true, speed: 1 })]}
        >
          <CarouselContent className="ml-0">
            {logos.map((logo) => (
              <CarouselItem
                key={logo.id}
                className="flex basis-1/3 justify-center pl-0 sm:basis-1/4 md:basis-1/5 lg:basis-1/6"
              >
                <div className="flex shrink-0 items-center justify-center lg:mx-10">
                  <div>
                    <Image
                      src={logo.image}
                      alt={logo.description}
                      width={100}
                      height={100}
                      className={logo.className}
                    />
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
        <div className="bg-linear-to-r from-background absolute inset-y-0 left-0 w-12 to-transparent" />
        <div className="bg-linear-to-l from-background absolute inset-y-0 right-0 w-12 to-transparent" />
      </div>
      <Separator className="my-15 mx-auto max-w-5xl" />

      <div>
        <Carousel opts={{ loop: true }} className="mx-auto w-full max-w-6xl">
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
                <div
                  className={cn(
                    'flex flex-col relative border-border w-full border-r px-12 text-center md:px-8 md:text-left h-full justify-between',
                    index === 0 && 'lg:border-l'
                  )}
                  key={index}
                >
                  <h5 className="text-muted-foreground mb-14 mt-5 text-lg tracking-tight md:mb-16">
                    {testimonial.quote}
                  </h5>
                  <div className="mt-auto">
                    <p className="text-foreground text-lg font-semibold tracking-tight">
                      {testimonial.name}
                    </p>
                    <Image
                      className="mx-auto my-5 w-28 md:mx-0"
                      alt="Company logo"
                      width={160}
                      height={160}
                      src={testimonial.image}
                    />
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </section>
  );
};

export { Endorsements };
