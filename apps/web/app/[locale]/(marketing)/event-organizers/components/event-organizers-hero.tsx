'use client';

import checkoutflowIcon from '@/app/assets/module/checkoutflow.svg';
import complysyncIcon from '@/app/assets/module/complysync.svg';
import crowdconnectIcon from '@/app/assets/module/crowdconnect.svg';
import fankeeperIcon from '@/app/assets/module/fankeeper.svg';
import gearloungeIcon from '@/app/assets/module/gearlounge.svg';
import impactsparkIcon from '@/app/assets/module/impactspark.svg';
import innercircleIcon from '@/app/assets/module/innercircle.svg';
import passportIcon from '@/app/assets/module/passport.svg';
import reachstudioIcon from '@/app/assets/module/reachstudio.svg';
import seatsmartIcon from '@/app/assets/module/seatsmart.svg';
import { env } from '@/env';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@repo/design-system/components/ui/carousel';
import AutoScroll from 'embla-carousel-auto-scroll';
import Image from 'next/image';
import Link from 'next/link';

const EventOrgenizersHero = () => {
  return (
    <section>
      <div className="container">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          <div className="mx-auto">
            <div className="flex w-fit items-center gap-2 rounded-full border px-2.5 py-1.5 text-xs font-medium">
              <Badge>AI-powered</Badge>
              Success for new events
            </div>
            <h1 className="mt-10 mb-4 text-3xl font-semibold lg:text-5xl">
              Powering your success with advanced tools
            </h1>
            <p className="mx-auto text-muted-foreground lg:text-lg">
              For sophisticated organizers seeking comprehensive control,
              maximized revenue, and deep audience engagement.
            </p>
            <div className="mt-10 flex flex-col gap-2 sm:flex-row">
              <Link href={`${env.NEXT_PUBLIC_APP_URL}/sign-in`}>
                <Button size="lg" className="w-full gap-2 sm:w-auto">
                  Start for Free
                </Button>
              </Link>
              <Link href="https://cal.com/360hq/ticketcare" target="_blank">
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full gap-2 sm:w-auto"
                >
                  Schedule a Demo
                </Button>
              </Link>
            </div>
          </div>
          <div className="flex flex-col gap-8 lg:hidden">
            <Carousel
              opts={{
                loop: true,
              }}
              plugins={[
                AutoScroll({
                  speed: 0.8,
                }),
              ]}
              className="-mx-7"
            >
              <CarouselContent className="max-h-[350px]">
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={checkoutflowIcon}
                    alt="Checkout Flow"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={complysyncIcon}
                    alt="Comply Sync"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={crowdconnectIcon}
                    alt="Crowd Connect"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={fankeeperIcon}
                    alt="Fan Keeper"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={gearloungeIcon}
                    alt="Gear Lounge"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
              </CarouselContent>
            </Carousel>
            <Carousel
              opts={{
                loop: true,
              }}
              plugins={[
                AutoScroll({
                  speed: 0.8,
                  direction: 'backward',
                }),
              ]}
              className="-mx-7"
            >
              <CarouselContent className="max-h-[350px]">
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={impactsparkIcon}
                    alt="Impact Spark"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={innercircleIcon}
                    alt="Inner Circle"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={passportIcon}
                    alt="Passport"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={reachstudioIcon}
                    alt="Reach Studio"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="max-w-64 aspect-square bg-muted flex justify-center rounded-xl ml-4">
                  <Image
                    src={seatsmartIcon}
                    alt="Seat Smart"
                    width={100}
                    height={100}
                    className="dark:invert"
                  />
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
          <div className="hidden grid-cols-2 gap-8 lg:grid">
            <Carousel
              opts={{
                loop: true,
              }}
              plugins={[
                AutoScroll({
                  speed: 0.8,
                }),
              ]}
              orientation="vertical"
            >
              <CarouselContent className="max-h-[600px]">
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={checkoutflowIcon}
                    alt="Checkout Flow"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={complysyncIcon}
                    alt="Comply Sync"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={crowdconnectIcon}
                    alt="Crowd Connect"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={fankeeperIcon}
                    alt="Fan Keeper"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={gearloungeIcon}
                    alt="Gear Lounge"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
              </CarouselContent>
            </Carousel>
            <Carousel
              opts={{
                loop: true,
              }}
              plugins={[
                AutoScroll({
                  speed: 0.8,
                  direction: 'backward',
                }),
              ]}
              orientation="vertical"
            >
              <CarouselContent className="max-h-[600px]">
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={impactsparkIcon}
                    alt="Impact Spark"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={innercircleIcon}
                    alt="Inner Circle"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={passportIcon}
                    alt="Passport"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={reachstudioIcon}
                    alt="Reach Studio"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
                <CarouselItem className="aspect-square bg-muted flex justify-center rounded-xl mb-4">
                  <Image
                    src={seatsmartIcon}
                    alt="Seat Smart"
                    width={120}
                    height={120}
                    className="dark:invert"
                  />
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>
      </div>
    </section>
  );
};

export { EventOrgenizersHero };
