import { Card, CardContent } from '@repo/design-system/components/ui/card';
import { ArrowUpRight } from 'lucide-react';
import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import Link from 'next/link';

export type FeatureHighlight = {
  title: string;
  href?: string;
};

interface FeaturesHighlightProps {
  title: string;
  icon: StaticImageData;
  image: StaticImageData;
  features: FeatureHighlight[];
}

const FeaturesHighlight = ({
  title,
  icon,
  image,
  features,
}: FeaturesHighlightProps) => {
  return (
    <section className="bg-background overflow-hidden">
      <div className="container">
        <div className="relative lg:min-h-70">
          <header className="mb-8 lg:mb-16 md:max-w-lg text-center md:text-left">
            <h1 className="text-foreground mb-8 text-3xl font-bold tracking-tighter lg:text-5xl">
              {title}
            </h1>
          </header>
          <div className="-top-15 absolute -right-10 hidden transition-all ease-in-out group-hover:-rotate-0 lg:block aspect-square">
            <Image src={image} className="size-150 md:size-100" alt="" />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {features.map((feature, index) => {
            const isExternal = feature.href?.startsWith('https');

            return (
              <Link
                key={index}
                href={feature.href ?? '/'}
                className="block transition-all duration-500 ease-in-out"
                {...(isExternal
                  ? { target: '_blank', rel: 'noopener noreferrer' }
                  : {})}
              >
                <Card className="rounded-none! border-border bg-background hover:bg-linear-to-l hover:to-muted group relative border p-6 shadow-none transition-all duration-500 ease-in-out hover:from-transparent h-full">
                  <CardContent className="flex h-full flex-col justify-start p-0">
                    <div className="size-15 border-border bg-muted group-hover:bg-background flex items-center justify-center border transition-colors duration-500 ease-in-out ">
                      <Image
                        src={icon}
                        width={30}
                        height={30}
                        alt={feature.title}
                        className="transition-all duration-500 ease-in-out dark:invert"
                      />
                    </div>

                    <h3 className="text-foreground lg:pr-30 mt-4 text-lg font-semibold leading-tight tracking-tight transition-all duration-500 ease-in-out md:text-xl lg:text-2xl">
                      {feature.title}
                    </h3>

                    <div className="border-border bg-muted absolute bottom-6 right-6 flex size-12 items-center justify-center border opacity-0 transition-all duration-500 ease-in-out group-hover:opacity-100">
                      <ArrowUpRight className="text-foreground h-[27px] w-[27px] transition-all duration-500 ease-in-out group-hover:rotate-45" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export { FeaturesHighlight };
