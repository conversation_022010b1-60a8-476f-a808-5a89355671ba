import { CheckCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import { cn } from '@repo/design-system/lib/utils';

const CheckItemText = (props: {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  color?: 'primary' | 'success';
  iconStyle?: 'outlined' | 'contained' | 'filled';
  textClassName?: string;
}) => {
  const { text, color, size, iconStyle = 'contained' } = props;

  return (
    <li className="flex gap-3">
      {iconStyle === 'contained' && (
        <div
          className={cn(
            'flex shrink-0 items-center justify-center rounded-full',
            color === 'success'
              ? 'bg-success-secondary text-featured-icon-light-fg-success'
              : 'bg-brand-primary text-featured-icon-light-fg-brand',
            size === 'lg'
              ? 'size-7 md:h-8 md:w-8'
              : size === 'md'
                ? 'size-7'
                : 'size-6'
          )}
        >
          {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
          <svg
            width={size === 'lg' ? 16 : size === 'md' ? 15 : 13}
            height={size === 'lg' ? 14 : size === 'md' ? 13 : 11}
            viewBox="0 0 13 11"
            fill="none"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M11.0964 0.390037L3.93638 7.30004L2.03638 5.27004C1.68638 4.94004 1.13638 4.92004 0.736381 5.20004C0.346381 5.49004 0.236381 6.00004 0.476381 6.41004L2.72638 10.07C2.94638 10.41 3.32638 10.62 3.75638 10.62C4.16638 10.62 4.55638 10.41 4.77638 10.07C5.13638 9.60004 12.0064 1.41004 12.0064 1.41004C12.9064 0.490037 11.8164 -0.319963 11.0964 0.380037V0.390037Z"
              fill="currentColor"
            />
          </svg>
        </div>
      )}

      {iconStyle === 'filled' && (
        <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-brand-solid text-white">
          {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
          <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
            <path
              d="M1.5 4L4.5 7L10.5 1"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      )}

      {iconStyle === 'outlined' && (
        <CheckCircle
          className={cn(
            'shrink-0',
            color === 'success'
              ? 'text-fg-success-primary'
              : 'text-fg-brand-primary',
            size === 'lg'
              ? 'size-7 md:h-8 md:w-8'
              : size === 'md'
                ? 'size-7'
                : 'size-6'
          )}
        />
      )}

      <span
        className={cn(
          'text-tertiary',
          size === 'lg'
            ? 'pt-0.5 text-lg md:pt-0'
            : size === 'md'
              ? 'pt-0.5 text-md md:pt-0 md:text-lg'
              : 'text-md',
          iconStyle === 'filled' && 'text-muted-foreground',
          props.textClassName
        )}
      >
        {text}
      </span>
    </li>
  );
};

export const PricingTierCardCallout = (props: {
  title: string;
  subtitle: string;
  description?: string;
  features: string[];
  secondAction?: string;
  checkItemTextColor?: 'primary' | 'success';
  hasCallout?: boolean;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        'relative flex flex-col rounded-2xl shadow-lg ring-1 ring-secondary_alt',
        props.className
      )}
    >
      {props.hasCallout && (
        <div className="-top-6 md:-right-16 absolute right-2">
          <div className="flex text-muted-foreground">
            {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
            <svg width="60" height="46" viewBox="0 0 60 46" fill="none">
              <path
                d="M9.22056 42.4485C9.06321 43.2619 9.595 44.0488 10.4084 44.2061C11.2217 44.3635 12.0086 43.8317 12.166 43.0184L9.22056 42.4485ZM50.5841 3.7912C51.405 3.68023 51.9806 2.92474 51.8696 2.10378C51.7586 1.28282 51.0032 0.707267 50.1822 0.818242L50.5841 3.7912ZM4.78725 32.3308C4.36038 31.6208 3.43878 31.3913 2.7288 31.8182C2.01882 32.2451 1.78931 33.1667 2.21618 33.8766L4.78725 32.3308ZM8.9767 42.2098L7.69117 42.9828L7.69189 42.984L8.9767 42.2098ZM12.5932 43.2606L11.9803 41.8916L11.979 41.8921L12.5932 43.2606ZM23.5123 40.0155C24.2684 39.677 24.6069 38.7897 24.2684 38.0336C23.9299 37.2774 23.0425 36.9389 22.2864 37.2774L23.5123 40.0155ZM10.6933 42.7334C12.166 43.0184 12.1659 43.0187 12.1658 43.019C12.1658 43.0189 12.1658 43.0192 12.1658 43.0192C12.1658 43.0192 12.1658 43.0189 12.166 43.0184C12.1662 43.0173 12.1666 43.0152 12.1672 43.012C12.1684 43.0058 12.1705 42.9953 12.1735 42.9808C12.1794 42.9517 12.1887 42.9064 12.2016 42.8456C12.2274 42.7239 12.2676 42.5403 12.3233 42.3008C12.4349 41.8216 12.6088 41.1193 12.8551 40.2421C13.3481 38.4863 14.1291 36.0371 15.2773 33.2782C17.5833 27.7375 21.3236 21.0615 27.0838 16.2002L25.1489 13.9076C18.8763 19.2013 14.905 26.3651 12.5076 32.1255C11.3042 35.0171 10.4856 37.5837 9.96684 39.4311C9.7073 40.3554 9.52235 41.1015 9.40152 41.6204C9.34109 41.8799 9.29667 42.0827 9.26695 42.2227C9.25209 42.2927 9.24091 42.3471 9.23323 42.385C9.22939 42.4039 9.22643 42.4187 9.22432 42.4294C9.22327 42.4347 9.22243 42.4389 9.22181 42.4421C9.22149 42.4437 9.22123 42.4451 9.22103 42.4461C9.22092 42.4467 9.22081 42.4473 9.22075 42.4475C9.22065 42.4481 9.22056 42.4485 10.6933 42.7334ZM27.0838 16.2002C38.8964 6.23107 48.2848 4.10201 50.5841 3.7912L50.1822 0.818242C47.3237 1.20465 37.402 3.56662 25.1489 13.9076L27.0838 16.2002ZM2.21618 33.8766L7.69117 42.9828L10.2622 41.4369L4.78725 32.3308L2.21618 33.8766ZM7.69189 42.984C8.83415 44.8798 11.2204 45.5209 13.2074 44.6291L11.979 41.8921C11.2779 42.2068 10.5661 41.9412 10.2615 41.4357L7.69189 42.984ZM13.2061 44.6297L23.5123 40.0155L22.2864 37.2774L11.9803 41.8916L13.2061 44.6297Z"
                fill="currentColor"
              />
            </svg>
            <span className="-mt-2 font-semibold text-sm">Most popular!</span>
          </div>
        </div>
      )}

      <div className="flex flex-col items-center px-6 pt-10 text-center md:px-8">
        <h2 className="font-semibold text-lg text-primary md:text-xl">
          {props.subtitle}
        </h2>
        <p className="mt-4 font-semibold text-primary text-xl md:text-2xl">
          {props.title}
        </p>
        <p className="mt-1 text-md text-muted-foreground">
          {props.description}
        </p>
      </div>

      <ul className="flex flex-col gap-4 px-6 pt-8 pb-8 md:p-8 md:pb-10">
        {props.features.map((feat) => (
          <CheckItemText
            key={feat}
            text={feat}
            color={props.checkItemTextColor}
          />
        ))}
      </ul>

      <div className="mt-auto flex flex-col gap-3 px-6 pb-8 md:px-8">
        {/* <Button size="lg">Get started</Button> */}
        {props.secondAction && <Button size="lg">{props.secondAction}</Button>}
      </div>
    </div>
  );
};
