import { env } from '@/env';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON>R<PERSON>,
  BadgeCheck,
  ClipboardList,
  Database,
  GitBranch,
  LifeBuoy,
  type LucideIcon,
  PhoneCall,
  QrCode,
  Server,
  Ticket,
  Zap,
} from 'lucide-react';
import Link from 'next/link';

interface FeatureItem {
  icon: LucideIcon;
  text: string;
}

interface PricingPlan {
  name: string;
  price: string;
  description: string;
  specialDescription?: string;
  bestFor: string;
  features?: FeatureItem[];
  mostPopular?: boolean;
  isFounderPass?: boolean;
  cta: {
    text: string;
    href: string;
  };
}

const PLANS: PricingPlan[] = [
  {
    name: 'Free',
    price: 'RM 0',
    description: 'Essential ticketing to get started',
    bestFor: 'Begin your event journey without upfront costs',
    features: [
      { icon: BadgeCheck, text: 'Free events' },
      { icon: Ticket, text: 'Up to 25 tickets for paid events' },
      {
        icon: ClipboardList,
        text: 'Ticketing: Create and manage event tickets',
      },
      {
        icon: QrCode,
        text: 'Scanning: Efficiently check in attendees at your event',
      },
    ],
    cta: {
      text: 'Get started',
      href: `${env.NEXT_PUBLIC_APP_URL}/sign-up`,
    },
  },
  {
    name: 'Premium',
    mostPopular: true,
    price: 'RM 100+',
    description: 'Enhanced tools for growing events',
    bestFor: 'Step up your event management with crucial enhancements',
    features: [
      { icon: Ticket, text: 'Tiered, by the number of tickets' },
      { icon: BadgeCheck, text: 'All Free Plan features' },
      { icon: Zap, text: 'Trial features from Enterprise modules' },
      { icon: PhoneCall, text: 'Email + chat support' },
    ],
    cta: {
      text: 'Get started',
      href: `${env.NEXT_PUBLIC_APP_URL}/sign-up`,
    },
  },
  {
    name: 'Enterprise',
    price: 'RM 500',
    description: 'Complete solution for advanced operations',
    bestFor: 'Unlock the full potential for comprehensive event management',
    features: [
      { icon: Database, text: 'Unlimited events and tickets' },
      { icon: Server, text: 'Everything included, at scale' },
      { icon: GitBranch, text: 'Priority feature roadmap' },
      { icon: LifeBuoy, text: 'Dedicated account manager' },
    ],
    cta: {
      text: 'Contact Sales',
      href: 'https://cal.com/360hq/ticketcare',
    },
  },
  {
    isFounderPass: true,
    name: 'Founder Pass 1K',
    price: 'RM 400',
    bestFor: 'per account for 1,000 tickets',
    specialDescription: 'Get Founder benefits',
    description: 'Premium events and priority support',
    cta: {
      text: 'Secure Founder access',
      href: 'https://pay.chip-in.asia/ARvsq9QYTn1GfyExTJ',
    },
  },
  {
    isFounderPass: true,
    name: 'Founder Pass 5K',
    price: 'RM 1,000',
    bestFor: 'per account for 5,000 tickets',
    specialDescription: 'Invest in exclusive access',
    description: 'Premium events and priority support',
    cta: {
      text: 'Secure Founder access',
      href: 'https://pay.chip-in.asia/exvrsCI4zyQiBw4iGZ',
    },
  },
];

// Pricing32 @ https://www.shadcnblocks.com/block/pricing32
const PricingPlans = () => {
  return (
    <section>
      <div className="container">
        <div className="flex flex-col items-center justify-center gap-9.5">
          <h1 className="text-center hero-text leading-none text-foreground">
            Pricing Plans
          </h1>
          <div className="mt-3 grid w-full grid-cols-1 gap-5 lg:grid-cols-6">
            {PLANS.map((plan, index) => (
              <PlanCard
                key={index}
                plan={plan}
                className={
                  index > PLANS.length - 3 ? 'lg:col-span-3' : 'lg:col-span-2'
                }
              />
            ))}
          </div>
        </div>
        <div className="m-9.5 flex items-center justify-center">
          {/* scroll to plan comparison section */}
          <Link href="#plan-comparison">
            <Button size="lg">Compare all plans</Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

const PlanCard = ({
  plan,
  className,
}: {
  plan: PricingPlan;
  className?: string;
}) => {
  const isEnterprise = plan.name.includes('Enterprise');

  return (
    <div
      className={`relative h-full w-full rounded-lg border px-6 py-5 ${className} ${plan?.mostPopular ? 'border-primary' : 'border-muted-2'} bg-background`}
    >
      <div className="text-2xl">{plan.name}</div>
      <div className="h-[2.875rem] overflow-hidden">
        <div className="text-[2.875rem] leading-none font-semibold">
          <div>{plan.price}</div>
        </div>
      </div>
      <div className="text-xs text-muted-2-foreground">
        {!plan.isFounderPass && (
          <div>{isEnterprise ? '/ month' : '/ event'}</div>
        )}
        <div>{plan.bestFor}</div>
        {plan.isFounderPass && <div>{plan.specialDescription}</div>}
      </div>
      <div className="mt-4 mb-6 text-lg font-medium text-foreground">
        {plan.description}
      </div>
      <Button
        asChild
        className="w-full"
        variant={plan.mostPopular ? 'default' : 'outline'}
        size="lg"
      >
        <a href={plan.cta.href} target="_blank" rel="noreferrer">
          {plan.cta.text}
          <ArrowRight />
        </a>
      </Button>
      <div className="mt-6 flex flex-col gap-4">
        {plan.features?.map((feature, index) => (
          <div key={index} className="flex items-center gap-3 text-foreground">
            <feature.icon className="size-5 stroke-1" />
            {feature.text}
          </div>
        ))}
      </div>
      {plan.mostPopular && (
        <div className="absolute top-0 left-1/2 w-fit -translate-1/2 -translate-y-1/2 rounded-full bg-primary px-4 py-1 text-xs font-medium text-primary-foreground">
          Most popular
        </div>
      )}
    </div>
  );
};

export { PricingPlans };
