'use client';

import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { Button } from '@repo/design-system/components/ui/button';
import { Check } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

// Pricing27 @ https://www.shadcnblocks.com/block/pricing27
const WhitelabelCallout = () => {
  return (
    <section>
      <div className="container">
        <ul className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
          {/* Main Card */}
          <li className="md:order-1 lg:col-span-1 xl:col-span-2">
            <div className="flex h-full flex-col-reverse rounded-lg border bg-muted md:flex-row">
              <div className="flex flex-col justify-between p-6 md:p-8">
                <div>
                  <div>
                    <p className="text-lg font-semibold">
                      Whitelabel Solutions
                    </p>
                    <p className="mt-3 text-muted-foreground">
                      Deliver exceptional ticketing apps under your own brand,
                      built for you by the dedicated TicketCARE team.
                    </p>
                  </div>
                  <p className="mt-6 text-sm font-semibold text-muted-foreground">
                    Starting at
                  </p>
                  <div className="mt-2 flex items-baseline">
                    <span className="text-3xl font-semibold">RM 12,000</span>
                    <span className="ml-2 text-sm text-muted-foreground">
                      per project
                    </span>
                  </div>
                  <Link
                    href="https://cal.com/360hq/ticketcare"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button size="lg" className="mt-5 w-fit ">
                      Get a free quote
                    </Button>
                  </Link>
                </div>

                <div className="mt-8">
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <Check className="mr-2 size-4 text-primary" />
                      <span className="text-sm text-muted-foreground">
                        Work directly with the TicketCARE expert team
                      </span>
                    </li>
                    <li className="flex items-start">
                      <Check className="mr-2 size-4 text-primary" />
                      <span className="text-sm text-muted-foreground">
                        Seamless integration with your existing tools and data
                      </span>
                    </li>
                    <li className="flex items-start">
                      <Check className="mr-2 size-4 text-primary" />
                      <span className="text-sm text-muted-foreground">
                        Dedicated whitelabel support & partnership
                      </span>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="hidden p-6 md:p-8 xl:block xl:w-[800px] 2xl:w-[960px]">
                <div className="h-full rounded-lg">
                  <div className="flex items-center size-full rounded-lg object-cover px-16 bg-gray-200 dark:bg-gray-200/10">
                    <Image
                      src={lightLogo}
                      alt="Logo"
                      width={445}
                      height={128}
                      className="block w-full rounded-lg object-cover dark:lg:hidden"
                    />
                    <Image
                      src={darkLogo}
                      alt="Logo"
                      width={445}
                      height={128}
                      className="hidden w-full rounded-lg object-cover dark:lg:block"
                    />
                  </div>
                </div>
              </div>
            </div>
          </li>

          {/* Secondary Card */}
          <li className="rounded-lg border bg-card p-6 md:p-8">
            <div className="flex h-full flex-col justify-between">
              <div>
                <p className="text-lg font-semibold">Find Your Dream Team</p>
                <p className="mt-3 text-muted-foreground">
                  Browse the TicketCARE Experts Directory to discover and
                  connect with vetted event management experts, suppliers, and
                  services for your next occasion.
                </p>
                <p className="mt-6 text-sm font-semibold text-muted-foreground">
                  Starting at
                </p>
                <div className="mt-2 flex items-baseline">
                  <span className="text-3xl font-semibold">RM 0</span>
                  <span className="ml-2 text-sm text-muted-foreground">
                    per project
                  </span>
                </div>
                <Link
                  href="https://experts.ticketcare.my"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button size="lg" variant="secondary" className="mt-5 w-fit ">
                    Browse Experts
                  </Button>
                </Link>
              </div>

              <div className="mt-8">
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <Check className="mr-2 size-4 text-primary" />
                    <span className="text-sm text-muted-foreground">
                      Hire hourly or by project
                    </span>
                  </li>
                  <li className="flex items-start">
                    <Check className="mr-2 size-4 text-primary" />
                    <span className="text-sm text-muted-foreground">
                      Work with verified event professionals
                    </span>
                  </li>
                  <li className="flex items-start">
                    <Check className="mr-2 size-4 text-primary" />
                    <span className="text-sm text-muted-foreground">
                      Dedicated Support
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </section>
  );
};

export { WhitelabelCallout };
