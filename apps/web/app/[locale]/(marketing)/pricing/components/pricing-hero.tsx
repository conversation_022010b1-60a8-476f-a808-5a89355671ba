'use client';

import { LineShadowText } from '@repo/design-system/components/magicui/line-shadow-text';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';

// Hero233 @ https://www.shadcnblocks.com/block/hero233
const PricingHero = () => {
  return (
    <section className="relative h-screen w-full overflow-hidden bg-background py-32">
      <div className="relative z-20 container flex flex-col items-center justify-center gap-4 py-10 text-center md:mt-22">
        <div className="absolute -z-1 size-full max-w-3xl bg-background blur-xl" />
        <Button
          variant="secondary"
          className="group text-md my-16 flex w-fit items-center justify-center gap-3 rounded-full bg-muted/60 px-5 py-1 tracking-tight md:my-5"
        >
          <span className="size-2 rounded-full bg-foreground" />
          <span>Pricing designed with your needs in mind</span>
        </Button>
        <div className="relative flex max-w-xl items-center justify-center text-center font-medium tracking-tight hero-text">
          <h1 className="relative z-10 hero-text">
            <span className="mr-3 text-muted-foreground/50">
              A Radical Commitment to
            </span>
            <LineShadowText> Affordability </LineShadowText>
            <span>.</span>
          </h1>
        </div>

        <p className="mt-5 max-w-xl bg-background text-muted-foreground/80">
          We believe essential software should be accessible to all, so we
          bypass costly marketing to deliver powerful tools at a fraction of the
          price.
        </p>
        <div className="my-5 flex gap-4">
          <Link href="/pricing-philosophy">
            <Button
              variant="default"
              className="group text-md flex w-fit items-center justify-center gap-2 px-4 py-1 tracking-tight"
            >
              <span>Read our Philosophy</span>
              <ArrowRight className="size-4 -rotate-45 transition-all ease-out group-hover:rotate-0" />
            </Button>
          </Link>
        </div>
      </div>
      <div className="absolute top-0 size-full justify-center">
        <div className="flex md:hidden size-full">
          {Array.from({ length: 7 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: '100%' }}
              transition={{
                duration: 0.8,
                delay: i * 0.05,
                ease: 'easeOut',
              }}
              className="w-24 border-l bg-gradient-to-b to-transparent transition-all ease-in-out hover:scale-110 hover:from-black/2"
            />
          ))}
        </div>

        <div className="hidden md:flex size-full">
          {Array.from({ length: 18 }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: '100%' }}
              transition={{
                duration: 0.8,
                delay: i * 0.05,
                ease: 'easeOut',
              }}
              className="w-24 border-l bg-gradient-to-b to-transparent transition-all ease-in-out hover:scale-110 hover:from-black/2"
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export { PricingHero };
