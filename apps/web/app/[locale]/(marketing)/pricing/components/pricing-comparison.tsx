'use client';

import { env } from '@/env';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@repo/design-system/components/ui/collapsible';
import { cn } from '@repo/design-system/lib/utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import { useState } from 'react';

type PlanType = 'free' | 'premium' | 'enterprise';

interface Plan {
  name: string;
  type: PlanType;
  button: {
    text: string;
    variant: 'outline';
    href: string;
  };
  features: {
    [Category in 'usage' | 'features' | 'support']: {
      name: string;
      value: string | boolean;
    }[];
  };
}

const plans: Plan[] = [
  {
    name: 'Free',
    type: 'free',
    button: {
      text: 'Get started',
      variant: 'outline',
      href: `${env.NEXT_PUBLIC_APP_URL}/sign-up`,
    },
    features: {
      usage: [
        { name: 'Free events', value: 'Unlimited' },
        { name: 'Paid events', value: 'Up to 25 tickets' },
        { name: 'Members', value: false },
      ],
      features: [
        { name: 'Core Ticketing', value: true },
        { name: 'SeatSmart', value: false },
        { name: 'Passport', value: false },
        { name: 'ReachStudio', value: false },
        { name: 'InnerCircle', value: false },
        { name: 'FanKeeper', value: false },
        { name: 'CrowdConnect', value: false },
        { name: 'GearLounge', value: false },
        { name: 'Core Scanning', value: true },
        { name: 'Reporting & Analytics', value: true },
        { name: 'CheckoutFlow', value: false },
        { name: 'ImpactSpark', value: false },
        { name: 'ComplySync', value: false },
      ],
      support: [
        { name: 'Priority Support', value: false },
        { name: 'Account Manager', value: false },
        { name: 'Uptime SLA', value: false },
      ],
    },
  },
  {
    name: 'Premium',
    type: 'premium',
    button: {
      text: 'Get started',
      variant: 'outline',
      href: `${env.NEXT_PUBLIC_APP_URL}/sign-up`,
    },
    features: {
      usage: [
        { name: 'Free events', value: 'Unlimited' },
        { name: 'Paid events', value: 'Tiered' },
        { name: 'Members', value: '2' },
      ],
      features: [
        { name: 'Core Ticketing', value: true },
        { name: 'SeatSmart', value: true },
        { name: 'Passport', value: false },
        { name: 'ReachStudio', value: false },
        { name: 'InnerCircle', value: false },
        { name: 'FanKeeper', value: 'Limited' },
        { name: 'CrowdConnect', value: 'Limited' },
        { name: 'GearLounge', value: 'Limited' },
        { name: 'Core Scanning', value: true },
        { name: 'Reporting & Analytics', value: true },
        { name: 'CheckoutFlow', value: true },
        { name: 'ImpactSpark', value: true },
        { name: 'ComplySync', value: false },
      ],
      support: [
        { name: 'Priority Support', value: true },
        { name: 'Account Manager', value: false },
        { name: 'Uptime SLA', value: false },
      ],
    },
  },
  {
    name: 'Enterprise',
    type: 'enterprise',
    button: {
      text: 'Contact Sales',
      variant: 'outline',
      href: 'https://cal.com/360hq/ticketcare',
    },
    features: {
      usage: [
        { name: 'Free events', value: 'Unlimited' },
        { name: 'Paid events', value: 'Unlimited' },
        { name: 'Members', value: 'Unlimited' },
      ],
      features: [
        { name: 'Core Ticketing', value: true },
        { name: 'SeatSmart', value: true },
        { name: 'Passport', value: true },
        { name: 'ReachStudio', value: true },
        { name: 'InnerCircle', value: true },
        { name: 'FanKeeper', value: true },
        { name: 'CrowdConnect', value: true },
        { name: 'GearLounge', value: true },
        { name: 'Core Scanning', value: true },
        { name: 'Reporting & Analytics', value: true },
        { name: 'CheckoutFlow', value: true },
        { name: 'ImpactSpark', value: true },
        { name: 'ComplySync', value: true },
      ],
      support: [
        { name: 'Priority Support', value: true },
        { name: 'Account Manager', value: true },
        { name: 'Uptime SLA', value: true },
      ],
    },
  },
];

// Pricing30 @ https://www.shadcnblocks.com/block/pricing30
export const PricingComparison = () => {
  const [selectedPlan, setSelectedPlan] = useState(1);

  return (
    <section id="plan-comparison">
      <div className="font-medium">
        <MobilePricingTable
          selectedPlan={selectedPlan}
          onPlanChange={setSelectedPlan}
        />
        <DesktopPricingTable />
      </div>
    </section>
  );
};

const MobilePricingTable = ({
  selectedPlan,
  onPlanChange,
}: {
  selectedPlan: number;
  onPlanChange: (index: number) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const plan = plans[selectedPlan];

  return (
    <div className="md:hidden">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between border-b py-4">
          <CollapsibleTrigger className="flex items-center gap-2">
            <h3 className="text-2xl font-semibold">{plan.name}</h3>
            <ChevronsUpDown
              className={`size-5 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            />
          </CollapsibleTrigger>
          <Button variant={plan.button.variant} className="w-fit">
            {plan.button.text}
          </Button>
        </div>
        <CollapsibleContent className="flex flex-col space-y-2 p-2">
          {plans.map(
            (p, index) =>
              index !== selectedPlan && (
                <Button
                  size="lg"
                  variant="secondary"
                  key={index}
                  onClick={() => {
                    onPlanChange(index);
                    setIsOpen(false);
                  }}
                >
                  {p.name}
                </Button>
              )
          )}
        </CollapsibleContent>
      </Collapsible>

      {/* Features List */}
      <div className="mt-8">
        {Object.entries(plan.features).map(
          ([category, features], sectionIndex) => (
            <div key={sectionIndex} className="mb-8 space-y-2">
              <h3 className="mb-4 text-lg font-semibold capitalize">
                {category}
              </h3>
              {features.map((feature, featureIndex) => (
                <div
                  key={featureIndex}
                  className="grid grid-cols-2 items-center gap-8"
                >
                  <span className="border-b py-2">{feature.name}</span>
                  <div className="flex items-center gap-1 border-b py-2">
                    {typeof feature.value === 'boolean' ? (
                      feature.value ? (
                        <Check className="size-5" />
                      ) : (
                        <span className="size-5" />
                      )
                    ) : (
                      <div className="flex items-center gap-1">
                        <Check className="size-5" />
                        <span>{feature.value}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )
        )}
      </div>
    </div>
  );
};

const DesktopPricingTable = () => {
  return (
    <div className="hidden md:grid md:grid-cols-4">
      <FeaturesColumn />
      {plans.map((plan, index) => (
        <PricingColumn
          key={plan.name}
          plan={plan}
          isHighlighted={index === 1}
        />
      ))}
    </div>
  );
};

const FeaturesColumn = () => (
  <div>
    <div className="h-[140px]" /> {/* Spacer for plan header alignment */}
    {Object.entries(plans[0].features).map(([category, features], index) => (
      <div key={index}>
        <h3 className="flex h-20 items-center text-lg font-semibold capitalize">
          {category}
        </h3>
        {features.map((feature, featureIndex) => (
          <div key={featureIndex} className="py-4 min-h-[57px]">
            {feature.name}
          </div>
        ))}
      </div>
    ))}
  </div>
);

const PricingColumn = ({
  plan,
  isHighlighted,
}: {
  plan: Plan;
  isHighlighted: boolean;
}) => {
  const columnClass = cn('px-6', isHighlighted && 'bg-muted border rounded-xl');

  return (
    <div className={columnClass}>
      {/* Plan Header */}
      <div className="py-8">
        <h3 className="mb-3 text-2xl font-semibold">{plan.name}</h3>
        <Button variant={plan.button.variant} asChild>
          <a href={plan.button.href}>{plan.button.text}</a>
        </Button>
      </div>

      {/* Features */}
      {Object.entries(plan.features).map(([, features], sectionIndex) => (
        <div key={sectionIndex}>
          <div className="flex h-20 items-center" />
          {features.map((feature, featureIndex) => (
            <div
              key={featureIndex}
              className="flex items-center gap-1 border-b py-4 min-h-[57px]"
            >
              {typeof feature.value === 'boolean' ? (
                feature.value ? (
                  <Check className="size-5" />
                ) : (
                  <span className="size-5" />
                )
              ) : (
                <div className="flex items-center gap-1">
                  <Check className="size-4" />
                  <span>{feature.value}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};
