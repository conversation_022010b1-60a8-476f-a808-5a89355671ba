'use server';

import { log } from '@repo/observability/log';
import { cookies } from 'next/headers';

// Cookie options
const COOKIE_OPTIONS = {
  path: '/',
  maxAge: 30 * 24 * 60 * 60, // 30 days
  // secure: process.env.NODE_ENV === 'production', // Recommended for production
  // httpOnly: true, // Recommended if client JS doesn't need direct access
  // sameSite: 'lax', // Recommended
};

/**
 * Server action to set cartId cookie
 * Use this from server components
 */
export async function setCartIdCookie(cartId: string) {
  if (!cartId) {
    return;
  }

  const cookieStore = await cookies();

  cookieStore.set('cartId', cartId, COOKIE_OPTIONS);

  log.debug('Set cartId cookie in server action', { cartId });
}

/**
 * Server action to delete cartId cookie
 * Use this from server components
 */
export async function deleteCartIdCookie() {
  const cookieStore = await cookies();
  cookieStore.delete('cartId');
  log.debug('Deleted cartId cookie in server action');
}

/**
 * Server action to get cartId cookie
 * Use this from client components
 */
export async function getCartIdCookie(): Promise<string | undefined> {
  const cookieStore = await cookies();
  const cartId = cookieStore.get('cartId')?.value;
  return cartId;
}
