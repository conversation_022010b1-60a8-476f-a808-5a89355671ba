'use server';

import { database, serializePrisma } from '@repo/database';
import { type Cart, CartStatus } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

const createCartSchema = z.object({
  eventId: z.string(),
  timeSlotId: z.string(),
  ticketTypeId: z.string(),
  quantity: z.number().min(1), // Ensure quantity is at least 1
});

export type CreateCartInput = z.infer<typeof createCartSchema>;

// Create cart or add item to existing cart
export async function createCart(
  input: CreateCartInput,
  existingCartId?: string // Optional: ID of the cart to add to
) {
  log.debug('createCart called', { input, existingCartId });

  try {
    const { eventId, timeSlotId, ticketTypeId, quantity } =
      createCartSchema.parse(input);
    const startTime = Date.now();

    let cart: Cart | null = null;
    let resetCartCookie = false;

    // Use a transaction to ensure atomicity and reduce round trips
    const cartId = await database.$transaction(async (tx) => {
      // 1. Use existingCartId if provided
      const findCartStart = Date.now();
      if (existingCartId) {
        log.debug('Using existingCartId', { existingCartId });
        const existingCartResponse = await tx.cart.findUnique({
          where: {
            id: existingCartId,
            status: CartStatus.idle, // Ensure it's an idle cart
            expiresAt: { gt: new Date() }, // Ensure it hasn't expired
          },
        });

        // if cart found from existingCartId, check if eventId matches
        if (existingCartResponse) {
          if (
            existingCartResponse.eventId &&
            existingCartResponse.eventId !== eventId
          ) {
            // flag to reset cart cookie
            resetCartCookie = true;
            // create new cart if cart eventId is different from input eventId
            log.warn('Provided existingCartId does not belong to same event', {
              existingCartId,
              cartEventId: existingCartResponse.eventId,
              requestedEventId: eventId,
            });
            // Fallback: Try finding/creating a new cart below
          } else if (existingCartResponse.eventId) {
            log.debug('assigning existing cart', {
              existingCartId,
              eventId,
            });
            cart = existingCartResponse;
          } else {
            log.debug('binding eventId to existing cart', {
              existingCartId,
              eventId,
            });
            // bind eventId if existing cart eventId is null
            cart = await tx.cart.update({
              where: {
                id: existingCartId,
                status: CartStatus.idle, // Ensure it's an idle cart
                expiresAt: { gt: new Date() }, // Ensure it hasn't expired
              },
              data: { eventId },
            });
          }
        } else {
          // flag to reset cart cookie
          resetCartCookie = true;
          log.warn('Provided existingCartId not found or invalid', {
            existingCartId,
            eventId,
          });
          // Fallback: Try finding/creating a new cart below
        }
      }
      const findCartEnd = Date.now();
      log.debug(`Find cart by ID took ${findCartEnd - findCartStart}ms`);

      // 2. If no valid cart from existingCartId, create new cart
      if (!cart) {
        // Create new cart if none found
        log.debug('No existing cart found, creating new cart');
        const createCartStart = Date.now();
        cart = await tx.cart.create({
          data: {
            eventId: eventId,
            status: CartStatus.idle, // initialize cart as idle
            expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
          },
        });
        log.info('New cart created', { cartId: cart.id, eventId });
        const createCartEnd = Date.now();
        log.debug(`Create cart took ${createCartEnd - createCartStart}ms`);
      }

      // 3. Ensure cart expiry is extended
      const expiryStart = Date.now();
      if (cart) {
        const fifteenMinutesFromNow = new Date(Date.now() + 15 * 60 * 1000);
        // Only update if the new expiry is later than the current one
        if (!cart.expiresAt || cart.expiresAt < fifteenMinutesFromNow) {
          cart = await tx.cart.update({
            where: { id: cart.id },
            data: {
              expiresAt: fifteenMinutesFromNow, // Extend expiry
            },
          });
          log.debug('Cart expiry extended', {
            cartId: cart.id,
            expiresAt: cart.expiresAt,
          });
        }
      } else {
        // This case should ideally not happen after the find/create logic
        log.error('Failed to find or create a cart.');
        throw new Error('Could not establish a cart session.');
      }
      const expiryEnd = Date.now();
      log.debug(`Extend expiry took ${expiryEnd - expiryStart}ms`);

      // 4. Look for existing cartItem using input OR create new cartItem
      const itemStart = Date.now();
      const existingCartItem = await tx.cartItem.findFirst({
        where: {
          cartId: cart.id,
          timeSlotId: timeSlotId,
          ticketTypeId: ticketTypeId,
        },
      });

      if (existingCartItem) {
        // Update quantity if item exists
        await tx.cartItem.update({
          where: { id: existingCartItem.id },
          data: { quantity: existingCartItem.quantity + quantity }, // Add to existing quantity
        });
        log.debug('Updated existing cart item quantity', {
          itemId: existingCartItem.id,
          newQuantity: existingCartItem.quantity + quantity,
        });
      } else {
        // Create new cart item
        const newItem = await tx.cartItem.create({
          data: {
            cartId: cart.id,
            timeSlotId: timeSlotId,
            ticketTypeId: ticketTypeId,
            quantity,
          },
        });
        log.debug('Created new cart item', { itemId: newItem.id, quantity });
      }
      const itemEnd = Date.now();
      log.debug(`Add/update cart item took ${itemEnd - itemStart}ms`);

      return cart.id;
    });

    // Fetch the complete cart data to return to the client
    // This eliminates the need for a separate getCart call
    const startFetchTime = Date.now();
    const fetchStart = Date.now();
    const completeCart = await database.cart.findUnique({
      where: {
        id: cartId,
        status: CartStatus.idle,
      },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            slug: true,
            heroImageUrl: true,
            ticketSalesMode: true,
            organizer: {
              select: {
                name: true,
                slug: true,
              },
            },
            checkoutFormQuestion: true,
            eventModule: {
              select: {
                customPaymentEnabled: true,
                customPaymentModule: {
                  select: {
                    chipsEnabled: true,
                  },
                },
              },
            },
          },
        },
        cartItems: {
          orderBy: { createdAt: 'asc' },
          include: {
            // Only select the fields we actually need
            ticketType: {
              select: {
                id: true,
                name: true,
                price: true,
                description: true,
              },
            },
            timeSlot: {
              select: {
                id: true,
                startTime: true,
                endTime: true,
                doorsOpen: true,
              },
            },
          },
        },
      },
    });
    const endFetchTime = Date.now();
    log.debug(
      `Fetched complete cart data in createCart - took ${endFetchTime - startFetchTime}ms`
    );
    const fetchEnd = Date.now();
    log.debug(`Fetch complete cart took ${fetchEnd - fetchStart}ms`);

    // Revalidate paths outside the transaction
    revalidatePath('/events/[slug]'); // Revalidate event page potentially showing cart info
    const totalTime = Date.now() - startTime;
    log.info(`createCart completed in ${totalTime}ms`, { cartId });

    log.info('createCart successful', { cartId });

    // Non-critical flow to clean up expired & idle carts
    log.info('starting cart cleanup');
    await cleanUpExpiredIdleCart();
    log.info('cart cleanup ended');

    return {
      cartId,
      cart: completeCart ? serializePrisma(completeCart) : null,
      resetCartCookie, // flag to incidate if cart cookie needs to be reset
    };
  } catch (error) {
    log.error('Failed to create/update cart', { error });

    if (error instanceof z.ZodError) {
      throw new Error(
        `Invalid input: ${error.errors.map((e) => e.message).join(', ')}`
      );
    }

    // Rethrow a generic error or handle specific Prisma errors if needed
    throw new Error('Failed to add item to cart');
  }
}

// Get cart with all related data
export async function getCart(cartId: string, isCheckoutPage?: boolean) {
  const startTime = Date.now();
  log.debug(`getCart called [${new Date().toISOString()}]`, { cartId });

  try {
    // Use raw SQL query for better performance
    // This avoids the overhead of Prisma's relational queries
    // and only fetches exactly what we need
    const queryStart = Date.now();

    // First, get the cart itself
    // fetch specified status otherwise default to idle
    const cart = await database.cart.findUnique({
      where: isCheckoutPage
        ? {
            id: cartId,
            // only allow checkout page to fetch idle or active cart
            OR: [{ status: CartStatus.idle }, { status: CartStatus.active }],
          }
        : {
            id: cartId,
            status: CartStatus.idle,
          },
      select: {
        id: true,
        userId: true,
        status: true,
        eventId: true,
        event: {
          select: {
            id: true,
            title: true,
            slug: true,
            heroImageUrl: true,
            ticketSalesMode: true,
            organizer: {
              select: {
                name: true,
                slug: true,
              },
            },
            checkoutFormQuestion: true,
            eventModule: {
              select: {
                customPaymentEnabled: true,
                customPaymentModule: {
                  select: {
                    chipsEnabled: true,
                  },
                },
              },
            },
          },
        },
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!cart) {
      log.warn(`Cart not found [${new Date().toISOString()}]`, { cartId });
      return null;
    }

    // Explicitly check for expiration after fetching
    if (cart.expiresAt < new Date()) {
      log.warn(`Cart has expired [${new Date().toISOString()}]`, {
        cartId,
        expiresAt: cart.expiresAt,
      });

      // Mark as expired asynchronously without waiting for completion
      database.cart
        .update({
          where: { id: cartId },
          data: { status: CartStatus.expired },
        })
        .catch((err) => {
          log.error('Error marking cart as expired', { error: err, cartId });
        });

      // Still return the cart data for the current request
      return serializePrisma({
        ...cart,
        cartItems: [],
      });
    }

    // Then, get the cart items with their related data in a separate query
    // This is more efficient than a deep nested include
    const cartItems = await database.cartItem.findMany({
      where: {
        cartId: cartId,
      },
      orderBy: { createdAt: 'asc' },
      select: {
        id: true,
        cartId: true,
        timeSlotId: true,
        ticketTypeId: true,
        quantity: true,
        createdAt: true,
        updatedAt: true,
        ticketType: {
          select: {
            id: true,
            name: true,
            price: true,
            description: true,
          },
        },
        timeSlot: {
          select: {
            id: true,
            startTime: true,
            endTime: true,
            doorsOpen: true,
          },
        },
      },
    });

    const queryEnd = Date.now();
    log.debug(`Database queries completed in ${queryEnd - queryStart}ms`);

    // Combine the cart with its items
    const fullCart = {
      ...cart,
      cartItems,
    };

    const endTime = Date.now();
    log.debug(
      `Cart fetched successfully [${new Date().toISOString()}] - took ${endTime - startTime}ms`,
      { cartId, itemCount: cartItems.length }
    );
    return serializePrisma(fullCart);
  } catch (error) {
    const endTime = Date.now();
    log.error(
      `Error fetching cart [${new Date().toISOString()}] - after ${endTime - startTime}ms`,
      { error, cartId }
    );
    return null;
  }
}

// Remove cart item
export async function removeCartItem(itemId: string) {
  log.debug('removeCartItem called', { itemId });

  try {
    // Optional: Find the item first to get the cartId for revalidation
    const item = await database.cartItem.findUnique({
      where: { id: itemId },
      select: { cartId: true },
    });
    if (!item) {
      log.warn('Cart item not found for removal', { itemId });
      throw new Error('Item not found');
    }

    await database.cartItem.delete({
      where: { id: itemId },
    });

    // Check if the cart is now empty
    const remainingItems = await database.cartItem.count({
      where: { cartId: item.cartId },
    });
    if (remainingItems === 0) {
      log.info('Cart is empty after removing item, marking as abandoned', {
        cartId: item.cartId,
      });
      await database.cart.update({
        where: { id: item.cartId },
        data: { status: CartStatus.abandoned }, // Or delete the cart if preferred
      });
    }

    revalidatePath('/events/[slug]'); // Revalidate event page potentially showing cart info
    log.info('Cart item removed successfully', { itemId });

    return { success: true };
  } catch (error) {
    log.error('Error removing cart item', { error });

    throw new Error('Failed to remove item from cart');
  }
}

// Update cart item quantity
const updateQuantitySchema = z.object({
  quantity: z.number().min(1), // Quantity must be at least 1
});

export async function updateCartItemQuantity(itemId: string, quantity: number) {
  log.debug('updateCartItemQuantity called', { itemId, quantity });
  try {
    updateQuantitySchema.parse({ quantity });

    await database.cartItem.update({
      where: { id: itemId },
      data: { quantity },
    });

    revalidatePath('/events/[slug]'); // Revalidate event page potentially showing cart info
    log.info('Cart item quantity updated', { itemId, quantity });
    return { success: true };
  } catch (error) {
    log.error('Error updating cart item quantity', { error });
    if (error instanceof z.ZodError) {
      throw new Error(
        `Invalid quantity: ${error.errors.map((e) => e.message).join(', ')}`
      );
    }
    throw new Error('Failed to update item quantity');
  }
}

// Clear cart (marks as abandoned and removes items)
export async function clearCart(cartId: string) {
  log.debug('clearCartAction called', { cartId });
  try {
    // Delete all cart items associated with the cart
    await database.cartItem.deleteMany({
      where: {
        cartId: cartId,
      },
    });

    // Update the cart status to abandoned
    await database.cart.update({
      where: {
        id: cartId,
      },
      data: {
        status: CartStatus.abandoned,
        // Optionally set expiresAt to now if needed
        // expiresAt: new Date()
      },
    });

    revalidatePath('/events/[slug]'); // Revalidate event page potentially showing cart info

    log.info('Cart cleared successfully', { cartId });
    return { success: true };
  } catch (error) {
    log.error('Error clearing cart', { error });
    throw new Error('Failed to clear cart');
  }
}

// Create a new empty cart
export async function createEmptyCart() {
  log.debug('createEmptyCart server action called');
  try {
    // Create a new empty cart in the database
    const result = await database.cart.create({
      data: {
        status: CartStatus.idle,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
      },
    });

    // Update cartId in cookie
    if (result.id) {
      log.info('New empty cart created', { cartId: result.id });
    } else {
      log.error('Failed to create new empty cart');
      // Clear the potentially bad cookie
      throw new Error('Failed to create a new cart');
    }

    // Return the serialized cart with empty cartItems array
    return {
      cartId: result.id,
      cart: serializePrisma({
        ...result,
        event: null,
        cartItems: [],
      }),
    };
  } catch (error) {
    log.error('Error creating new empty cart', { error });
    throw new Error('Failed to create a new cart');
  }
}

// Utility function to clean up expired cart in IDLE status
export async function cleanUpExpiredIdleCart() {
  try {
    const result = await database.cart.deleteMany({
      where: {
        OR: [{ status: CartStatus.expired, expiresAt: { lt: new Date() } }],
      },
    });
    log.info('Expired carts cleaned up', { count: result.count });
  } catch (error) {
    log.error('Error cleaning up expired carts', { error });
    throw new Error('Failed to clean up expired carts');
  }
}
