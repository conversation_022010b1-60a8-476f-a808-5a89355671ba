import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ead<PERSON>,
} from '@repo/design-system/components/ui/card';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';

export function CartLoadingSkeleton() {
  return (
    <div className="animate-pulse space-y-6 px-4 py-6 md:px-6 md:py-12">
      {/* Title Skeleton */}
      <Skeleton className="h-9 w-48" />

      {/* Desktop Table Skeleton */}
      <div className="hidden rounded-md border md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50%]">
                <Skeleton className="h-5 w-20" />
              </TableHead>
              <TableHead className="text-right">
                <Skeleton className="ml-auto h-5 w-16" />
              </TableHead>
              <TableHead className="text-right">
                <Skeleton className="ml-auto h-5 w-16" />
              </TableHead>
              <TableHead className="text-right">
                <Skeleton className="ml-auto h-5 w-16" />
              </TableHead>
              <TableHead>
                <Skeleton className="ml-auto h-5 w-8" />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[1, 2].map(
              (
                i // Render 1-2 skeleton rows
              ) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="mb-1 h-5 w-3/4" />
                    <Skeleton className="mb-1 h-4 w-1/2" />
                    <Skeleton className="h-4 w-1/3" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="ml-auto h-5 w-12" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="ml-auto h-5 w-8" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="ml-auto h-5 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="ml-auto h-8 w-8 rounded-md" />
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card Skeleton */}
      <div className="grid gap-4 md:hidden">
        {[1].map(
          (
            i // Render 1-2 skeleton cards
          ) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="mb-1 h-6 w-3/4" />
                <Skeleton className="mb-2 h-4 w-1/2" />
                <Skeleton className="h-4 w-1/3" />
              </CardHeader>
              <CardContent className="grid gap-2">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-12" />
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-8" />
                </div>
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-12 font-medium" />
                  <Skeleton className="h-5 w-16 font-medium" />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Skeleton className="h-8 w-8 rounded-md" />
              </CardFooter>
            </Card>
          )
        )}
      </div>

      {/* Summary Skeleton */}
      <div className="flex flex-col items-end gap-4">
        <div className="grid w-full max-w-sm gap-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-5 w-20" />
            <Skeleton className="h-5 w-24" />
          </div>
          {/* Add skeleton for fees if needed */}
          <div className="flex items-center justify-between border-t pt-2">
            <Skeleton className="h-6 w-16 font-semibold" />
            <Skeleton className="h-6 w-28 font-semibold" />
          </div>
        </div>
        <Skeleton className="h-11 w-full max-w-sm" />{' '}
        {/* Checkout Button Skeleton */}
      </div>
    </div>
  );
}
