'use client';

import CartDrawerContent from '@/app/[locale]/(cart)/components/cart-drawer-content';
import { useCart } from '@/app/[locale]/(events)/_lib/cart-context';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@repo/design-system/components/ui/sheet';
import { formatCurrency } from '@repo/design-system/lib/format';
import { ShoppingCart } from 'lucide-react';
import Link from 'next/link';

export function CartButton() {
  const { cart, totalItems, totalAmount, isLoading } = useCart();

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button size="icon" variant="ghost" className="relative">
          <ShoppingCart />
          {totalItems > 0 && (
            <Badge
              variant="default"
              className="absolute top-0 right-0 flex h-4 w-4 items-center justify-center rounded-full p-0 text-xs"
            >
              {totalItems}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent
        side="right"
        className="flex max-h-vh w-dvw flex-col md:w-[400px]"
      >
        <SheetHeader className="px-4 py-4">
          <SheetTitle>Cart for {cart?.event?.title}</SheetTitle>
          <SheetDescription>by {cart?.event?.organizer?.name}</SheetDescription>
        </SheetHeader>
        <div className="flex-1 overflow-y-auto">
          <CartDrawerContent />
        </div>
        <SheetFooter className="space-y-2 border-t px-4 py-4">
          <div className="flex items-center justify-between">
            <span className="label">Subtotal</span>
            <span>{formatCurrency(totalAmount)}</span>
          </div>
          <div className="flex items-center justify-between font-medium">
            <span className="label">Total</span>
            <span>{formatCurrency(totalAmount)}</span>
          </div>
          <SheetClose asChild>
            <Button className="w-full" disabled={isLoading}>
              <Link href={`/checkout/${cart?.id}`}>Checkout</Link>
            </Button>
          </SheetClose>
          <SheetClose asChild>
            <Button variant="outline">Cancel</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
