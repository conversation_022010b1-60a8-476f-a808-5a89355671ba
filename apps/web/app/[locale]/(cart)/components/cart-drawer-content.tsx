'use client';

import { CartLoadingSkeleton } from '@/app/[locale]/(cart)/components/loading-skeletons';
import { useCart } from '@/app/[locale]/(events)/_lib/cart-context';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  formatCurrency,
  formatDate,
  formatTime,
} from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';
import { ShoppingCart, Trash2Icon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function CartDrawerContent() {
  const router = useRouter();
  const cartContext = useCart();
  const {
    cart,
    totalItems,
    removeFromCart,
    totalAmount,
    isLoading,
    createNewCart,
  } = cartContext;

  log.debug('CartPage loaded', { cart, totalItems, totalAmount, isLoading });

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeFromCart(itemId);
    } catch (error) {
      console.error('Error removing item:', error);
      // Add user feedback (e.g., toast notification)
    }
  };

  if (isLoading && !cart) {
    return <CartLoadingSkeleton />;
  }

  // Show error only if loading is finished and cart is still not available
  if (!isLoading && !cart) {
    return (
      <div className="container px-4 py-12 text-center">
        <div className="flex flex-col items-center justify-center py-12">
          <ShoppingCart className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2">Cart not available</h2>
          <p className="mb-4 text-muted-foreground">
            Your cart may have expired or is no longer available.
          </p>
          <div className="flex flex-col gap-4 sm:flex-row">
            <Button
              onClick={() => {
                // Create new cart optimistically without waiting
                createNewCart().catch((error) => {
                  console.error('Background cart creation failed:', error);
                  // No need to show error to user as they're already redirected
                });
                // Redirect immediately without waiting for cart creation
                router.push('/events');
              }}
            >
              Browse Events
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If cart is loaded but empty
  if (cart && totalItems === 0) {
    return (
      <div className="container px-4 py-12 text-center">
        <div className="flex flex-col items-center justify-center py-12">
          <ShoppingCart className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2">Your cart is empty</h2>
          <p className="mb-4 text-muted-foreground">
            Looks like you haven't added any tickets to your cart yet.
          </p>
          <Button onClick={() => router.push('/events')}>Browse Events</Button>
        </div>
      </div>
    );
  }

  // Cart is loaded and has items (or is updating)
  return (
    <div
      className={cn(
        'space-y-6',
        isLoading && 'opacity-75 transition-opacity duration-300' // Add visual feedback during update
      )}
    >
      {totalItems === 0 ? (
        <div className="flex flex-col items-center justify-center px-4 py-12 text-center">
          <ShoppingCart className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2">Your cart is empty</h2>
          <p className="mb-4 text-muted-foreground">
            Looks like you haven't added any tickets to your cart yet.
          </p>
          <Button onClick={() => router.push('/events')}>Browse Events</Button>
        </div>
      ) : (
        <div className="space-y-4 px-4">
          {cart?.cartItems.map((item) => (
            <Card key={item.id}>
              <CardHeader className="pb-2">
                <CardTitle>
                  <h3>{item.ticketType.name}</h3>
                </CardTitle>
                <CardDescription className="flex flex-col gap-1">
                  <span className="body-default">
                    {formatDate(item.timeSlot.startTime, 'dd-MM-yyyy')}
                    {' • '}
                    {formatTime(item.timeSlot.startTime)}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-2">
                  <div>
                    <div className="caption-small">Price</div>
                    <div>{formatCurrency(item.ticketType.price)}</div>
                  </div>
                  <div className="text-center">
                    <div className="caption-small">Quantity</div>
                    <div>{item.quantity}</div>
                  </div>
                  <div className="text-right">
                    <div className="caption-small">Total</div>
                    <div className="font-medium">
                      {formatCurrency(item.ticketType.price * item.quantity)}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end pt-0">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 border-destructive px-2 text-destructive hover:bg-destructive/10"
                  disabled={isLoading}
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <Trash2Icon className="mr-1 h-4 w-4" />
                  {isLoading ? 'Removing...' : 'Remove'}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
