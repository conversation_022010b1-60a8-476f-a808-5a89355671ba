'use server';

import { database, serializePrisma } from '@repo/database';
import type { Prisma } from '@repo/database/types';

// Common event selection fields
const eventSelectFields = {
  id: true,
  status: true,
  slug: true,
  title: true,
  description: true,
  startTime: true,
  endTime: true,
  category: true,
  eventType: true,
  venueName: true,
  venueAddress: true,
  terms: true,
  updates: true,
  venue: {
    select: {
      name: true,
      address: true,
    },
  },
  organizer: {
    select: {
      logo: true,
      name: true,
      slug: true,
    },
  },
  heroImageUrl: true,
  carouselImageUrls: true,
};

// get event hero slides
export async function getEventHeroSlides(isPast?: boolean) {
  try {
    const events = await database.event.findMany({
      where: {
        status: {
          in: ['published', 'sold_out'],
        },
        visibility: 'public',
        startTime: isPast
          ? {
              lt: new Date(),
            }
          : {
              gte: new Date(),
            },
      },
      select: {
        id: true,
        slug: true,
        title: true,
        eventType: true,
        heroImageUrl: true,
        carouselImageUrls: true,
      },
      orderBy: {
        startTime: 'asc',
      },
      take: 6,
    });

    return serializePrisma(events);
  } catch (error) {
    console.error('Error fetching event hero slides:', error);
    return [];
  }
}

export async function getEvents(params?: {
  keyword?: string;
  category?: string;
  sortBy?: string;
  isPast?: boolean;
}) {
  try {
    // Build where clause with filters
    const whereClause: Prisma.EventWhereInput = {
      status: {
        in: ['published', 'sold_out'],
      },
      visibility: 'public',
      startTime: params?.isPast
        ? {
            lt: new Date(),
          }
        : {
            gte: new Date(),
          },
    };

    // Add keyword search if provided
    if (params?.keyword) {
      whereClause.title = {
        contains: params.keyword,
        mode: 'insensitive',
      };
    }

    // Add category filter if provided and not 'everything'
    if (params?.category && params.category !== 'everything') {
      whereClause.category = {
        has: params.category,
      };
    }

    // Determine sort order
    let orderBy: Prisma.EventOrderByWithRelationInput = {};
    if (params?.sortBy) {
      switch (params.sortBy) {
        case 'popularity':
          orderBy = { ticketsSold: 'desc' };
          break;
        case 'name-asc':
          orderBy = { title: 'asc' };
          break;
        case 'name-desc':
          orderBy = { title: 'desc' };
          break;
        default:
          orderBy = { startTime: 'asc' };
      }
    }

    const events = await database.event.findMany({
      where: whereClause,
      select: {
        ...eventSelectFields,
        // fetch earliest time slot
        eventDates: {
          select: {
            date: true,
            timeSlots: {
              select: {
                startTime: true,
                endTime: true,
              },
              orderBy: {
                startTime: 'asc',
              },
              take: 1, // Only take the first time slot
            },
          },
          orderBy: {
            date: 'asc', // Sort dates in ascending order
          },
          take: 1, // Only take the first date
        },
      },
      orderBy: orderBy,
    });

    return serializePrisma(events);
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    return [];
  }
}
