'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@repo/design-system/components/ui/carousel';
import { cn } from '@repo/design-system/lib/utils';
import Autoplay from 'embla-carousel-autoplay';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

const EventHero = ({
  posters,
  isPast,
}: {
  posters: {
    src: string | null;
    alt: string;
    name: string;
    slug: string;
    eventType?: string;
  }[];
  isPast?: boolean;
}) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap());
    api.on('select', () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  if (!posters || posters.length === 0) {
    return null;
  }

  return (
    <section>
      <Carousel
        setApi={setApi}
        active={posters.length > 1}
        opts={{
          slidesToScroll: 1,
          loop: true,
        }}
        plugins={[
          Autoplay({
            delay: 5000,
            stopOnInteraction: false,
            stopOnMouseEnter: true,
          }),
        ]}
      >
        <CarouselContent className="ml-0 flex">
          {posters.map((poster, index) => (
            <CarouselItem
              key={index}
              className={cn(
                'px-4',
                posters.length <= 1 ? ' basis-[100%] px-0' : 'basis-[85%]'
              )}
            >
              <div
                key={index}
                className="relative flex aspect-[1200/800] w-full flex-col items-end justify-between overflow-clip rounded-2xl"
              >
                <Image
                  src={poster.src ?? '/placeholder.png'}
                  alt={poster.alt}
                  fill
                  draggable={false}
                  className="pointer-events-none object-cover"
                />
                <div className="absolute bottom-6 left-6 z-10 flex w-full justify-between">
                  <Link href={`/events/${poster.slug}`}>
                    <Button
                      variant="outline"
                      className="group flex w-fit items-center justify-center gap-2 rounded-full px-4 py-1 text-md tracking-tight"
                    >
                      {isPast
                        ? 'View Event'
                        : poster.eventType === 'ngo'
                          ? 'Reserve Seats'
                          : 'Buy Tickets'}
                      <ArrowRight className="-rotate-45 size-4 transition-all ease-out group-hover:ml-3 group-hover:rotate-0" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>

        {/* Navigation Dots */}
        <div className="mt-4 flex justify-center gap-2">
          {posters.map((_, index) => (
            <Button
              key={index}
              onClick={() => api?.scrollTo(index)}
              className={cn(
                'h-3 w-3 rounded-full p-0 transition-all',
                current === index ? 'w-4 bg-primary' : 'bg-muted-foreground/50'
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </Carousel>
    </section>
  );
};

export { EventHero };
