import type { SerializedEvent } from '@/app/types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardHeader,
} from '@repo/design-system/components/ui/card';
import { formatDate, formatTime } from '@repo/design-system/lib/format';
import { Calendar, MapPin } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

type SimpleEventDate = {
  date: string;
  timeSlots?: Array<{
    startTime: string;
    endTime: string;
  }>;
};

export type EventCardProps = Pick<
  SerializedEvent,
  | 'status'
  | 'slug'
  | 'title'
  | 'startTime'
  | 'category'
  | 'heroImageUrl'
  | 'carouselImageUrls'
  | 'venue'
  | 'venueName'
  | 'venueAddress'
> & {
  eventDates?: SimpleEventDate[];
};

export default function EventCard({ event }: { event: EventCardProps }) {
  const imageUrl = event.carouselImageUrls?.[0] ?? event.heroImageUrl;
  const firstEventDate = event.eventDates?.[0];
  const firstTimeSlot = firstEventDate?.timeSlots?.[0];

  return (
    <Link href={`/events/${encodeURIComponent(event.slug || '')}`}>
      <Card className="h-[456px] w-full gap-4 overflow-clip p-0 transition-shadow hover:shadow-xl">
        <CardHeader className="relative size-full max-h-[200px]">
          {imageUrl ? (
            <Image
              src={imageUrl}
              alt="Event Image"
              fill
              className="object-cover"
            />
          ) : (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-muted p-4 text-center">
              <InfoCircledIcon className="size-6 text-muted-foreground" />
              <p className="caption">Click to view details</p>
            </div>
          )}
          <Badge className="absolute top-2 left-2 flex flex-col gap-1">
            {formatDate(new Date(event.startTime || ''))}
          </Badge>
          {event.status === 'sold_out' && (
            <Badge
              className="absolute bottom-2 right-2 flex flex-col gap-1"
              variant="destructive"
            >
              Sold Out
            </Badge>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="w-full space-y-2">
            <div className="flex flex-wrap gap-1">
              {event.category?.map((category: string) => (
                <Badge key={category} variant="secondary">
                  {category}
                </Badge>
              ))}
            </div>

            <div className="h-[3.2rem]">
              <h2 className="line-clamp-2">{event.title}</h2>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <Calendar className="size-5 min-w-5" />
              <div className="flex items-center gap-1 text-sm font-medium">
                {formatDate(new Date(event.startTime ?? ''))}
                <div className="h-1 w-1 rounded-full bg-muted-foreground" />
                {formatTime(
                  new Date(firstTimeSlot?.startTime ?? event.startTime)
                )}
              </div>
            </div>

            <div className="flex items-start gap-2">
              <MapPin className="size-5 min-w-5" />
              <div className="space-y-1">
                <p>{event.venue?.name || event.venueName}</p>
                <p>{event.venueAddress}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
