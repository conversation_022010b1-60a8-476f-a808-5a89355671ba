import EventCard, {
  type EventCardProps,
} from '@/app/[locale]/(events)/events/components/event-card';
import EventFilters from '@/app/[locale]/(events)/events/components/event-filter';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

const EventList = ({
  events,
  isPast = false,
}: { events: EventCardProps[]; isPast?: boolean }) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <h1>Discover events in</h1>
        <Select defaultValue="malaysia">
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Select a country" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="malaysia">Malaysia</SelectItem>
          </SelectContent>
        </Select>
        <div className="hidden sm:block flex-1" />
        <Link
          href={`/events?past=${!isPast}`}
          className="hidden sm:flex items-center gap-1 hover:border-primary hover:border-b"
          scroll={false}
        >
          {isPast ? 'Upcoming' : 'Past'} Events
          <ArrowRight size={14} />
        </Link>
      </div>

      <EventFilters />

      <Link
        href={`/events?past=${!isPast}`}
        className="flex w-fit sm:hidden items-center gap-1 justify-self-end underline"
        scroll={false}
      >
        {isPast ? 'Upcoming' : 'Past'} Events
        <ArrowRight size={14} />
      </Link>

      {events.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-20">
          <h2 className="font-semibold text-xl">No events found</h2>
          <p className="mt-2 text-muted-foreground">
            Check back later for more awesome events
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {events.map((event) => (
            <EventCard
              key={event.slug}
              event={event satisfies EventCardProps}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default EventList;
