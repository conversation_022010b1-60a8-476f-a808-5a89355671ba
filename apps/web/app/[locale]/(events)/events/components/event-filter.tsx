'use client';

import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { Search } from 'lucide-react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const EventFilters = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [keyword, setKeyword] = useState(searchParams.get('keyword') || '');
  const [location, setLocation] = useState(
    searchParams.get('location') || 'everywhere'
  );
  const [category, setCategory] = useState(
    searchParams.get('category') || 'everything'
  );
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'date');

  // Debounce function for keyword search
  const [debouncedKeyword, setDebouncedKeyword] = useState(keyword);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedKeyword(keyword);
    }, 500);

    return () => clearTimeout(timer);
  }, [keyword]);

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams(searchParams);

    if (debouncedKeyword) {
      params.set('keyword', debouncedKeyword);
    } else {
      params.delete('keyword');
    }

    if (location !== 'everywhere') {
      params.set('location', location);
    } else {
      params.delete('location');
    }

    if (category !== 'everything') {
      params.set('category', category);
    } else {
      params.delete('category');
    }

    if (sortBy !== 'date') {
      params.set('sortBy', sortBy);
    } else {
      params.delete('sortBy');
    }

    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  }, [
    debouncedKeyword,
    location,
    category,
    sortBy,
    pathname,
    router,
    searchParams,
  ]);

  return (
    <div className="flex flex-col md:flex-row flex-wrap items-center gap-3">
      <div className="relative flex-1 w-full">
        <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search events..."
          className="pl-9"
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
        />
      </div>
      <div className="flex flex-1 items-center gap-2 w-full">
        {/* disable location for now */}
        <Select value={location} onValueChange={setLocation} disabled>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Location" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="everywhere">Everywhere</SelectItem>
            <SelectItem value="johor">Johor</SelectItem>
            <SelectItem value="kedah">Kedah</SelectItem>
            <SelectItem value="kelantan">Kelantan</SelectItem>
            <SelectItem value="kuala-lumpur">Kuala Lumpur</SelectItem>
            <SelectItem value="labuan">Labuan</SelectItem>
            <SelectItem value="malacca">Malacca</SelectItem>
            <SelectItem value="negeri-sembilan">Negeri Sembilan</SelectItem>
            <SelectItem value="pahang">Pahang</SelectItem>
            <SelectItem value="penang">Penang</SelectItem>
            <SelectItem value="perak">Perak</SelectItem>
            <SelectItem value="perlis">Perlis</SelectItem>
            <SelectItem value="putrajaya">Putrajaya</SelectItem>
            <SelectItem value="sabah">Sabah</SelectItem>
            <SelectItem value="sarawak">Sarawak</SelectItem>
            <SelectItem value="selangor">Selangor</SelectItem>
            <SelectItem value="terengganu">Terengganu</SelectItem>
          </SelectContent>
        </Select>

        <Select value={category} onValueChange={setCategory}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="everything">Everything</SelectItem>
            <SelectItem value="arts">Arts</SelectItem>
            <SelectItem value="auction">Auction</SelectItem>
            <SelectItem value="charity">Charity</SelectItem>
            <SelectItem value="competition">Competition</SelectItem>
            <SelectItem value="concert">Concert</SelectItem>
            <SelectItem value="conference">Conference</SelectItem>
            <SelectItem value="corporate">Corporate</SelectItem>
            <SelectItem value="exhibition">Exhibition</SelectItem>
            <SelectItem value="family">Family</SelectItem>
            <SelectItem value="fashion">Fashion</SelectItem>
            <SelectItem value="festival">Festival</SelectItem>
            <SelectItem value="meeting">Meeting</SelectItem>
            <SelectItem value="religious">Religious</SelectItem>
            <SelectItem value="social">Social</SelectItem>
            <SelectItem value="sports">Sports</SelectItem>
            <SelectItem value="spiritual">Spiritual</SelectItem>
            <SelectItem value="wellness">Wellness</SelectItem>
            <SelectItem value="workshop">Workshop</SelectItem>
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date">Latest</SelectItem>
            {/* <SelectItem value="popularity" disabled>Most Popular</SelectItem> */}
            <SelectItem value="name-asc">Name (A to Z)</SelectItem>
            <SelectItem value="name-desc">Name (Z to A)</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default EventFilters;
