import { EventHero } from '@/app/[locale]/(events)/events/components/event-hero';
import EventList from '@/app/[locale]/(events)/events/components/event-list';
import { getEventHeroSlides, getEvents } from './action';

export async function generateMetadata({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const awaitedParams = await searchParams;
  const isPast = awaitedParams.past === 'true';

  return {
    title: `${isPast ? 'Past' : 'Upcoming'} Events | TicketCARE`,
    description: `Browse ${isPast ? 'past' : 'upcoming'} events`,
  };
}

export default async function EventsPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const awaitedSearchParams = await searchParams;
  const keyword =
    typeof awaitedSearchParams.keyword === 'string'
      ? awaitedSearchParams.keyword
      : undefined;
  const category =
    typeof awaitedSearchParams.category === 'string'
      ? awaitedSearchParams.category
      : undefined;
  const sortBy =
    typeof awaitedSearchParams.sortBy === 'string'
      ? awaitedSearchParams.sortBy
      : undefined;
  const isPast = awaitedSearchParams.past === 'true';

  const eventHeroSlides = await getEventHeroSlides(isPast);
  const events = await getEvents({ keyword, category, sortBy, isPast });

  const eventPosters = eventHeroSlides.map((event) => ({
    src: event.carouselImageUrls?.[0] || event.heroImageUrl,
    alt: event.title,
    name: event.title,
    slug: event.slug,
    eventType: event.eventType,
  }));

  return (
    <div className="mx-auto w-full space-y-8 px-4 my-10">
      <EventHero posters={eventPosters} isPast={isPast} />
      <EventList events={events} isPast={isPast} />
    </div>
  );
}
