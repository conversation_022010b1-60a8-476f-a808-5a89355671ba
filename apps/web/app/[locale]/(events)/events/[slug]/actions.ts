'use server';

import { cleanUpExpiredCart, database, serializePrisma } from '@repo/database';
import { log } from '@repo/observability/log';
import { revalidatePath } from 'next/cache';

export async function refreshInventory(slug: string) {
  try {
    const result = await cleanUpExpiredCart();
    log.info('Manual inventory refresh completed', result);

    // Revalidate the current event page to refresh inventory data
    revalidatePath(`/events/${slug}`);

    return {
      success: true,
      data: { ...result },
    };
  } catch (error) {
    log.error('Manual inventory refresh failed', { error });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function getEvent(slug: string) {
  try {
    const event = await database.event.findUnique({
      where: {
        slug: decodeURIComponent(slug),
        status: {
          in: ['published', 'sold_out'],
        },
        visibility: {
          in: ['public', 'unlisted'],
        },
      },
      select: {
        id: true,
        status: true,
        slug: true,
        title: true,
        description: true,
        category: true,
        eventType: true,
        startTime: true,
        endTime: true,
        eventDates: {
          select: {
            id: true,
            date: true,
            timeSlots: {
              select: {
                id: true,
                startTime: true,
                endTime: true,
                doorsOpen: true,
                inventory: {
                  where: {
                    ticketType: {
                      visibility: {
                        in: ['public'],
                      },
                    },
                  },
                  select: {
                    id: true,
                    quantity: true,
                    ticketType: {
                      select: {
                        id: true,
                        name: true,
                        description: true,
                        price: true,
                        maxPerOrder: true,
                        minPerOrder: true,
                        saleStartTime: true,
                        saleEndTime: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        venueName: true,
        venueAddress: true,
        venue: {
          select: {
            name: true,
            address: true,
          },
        },
        organizer: {
          select: {
            logo: true,
            name: true,
            slug: true,
            description: true,
          },
        },
        heroImageUrl: true,
        carouselImageUrls: true,
        terms: true,
        updates: true,
        isPremiumEvent: true,
        eventModule: {
          select: {
            donationEnabled: true,
            donationModule: {
              select: {
                id: true,
                minAmount: true,
                description: true,
              },
            },
            customPaymentEnabled: true,
            customPaymentModule: {
              select: {
                id: true,
                chipsEnabled: true,
                stripeEnabled: true,
              },
            },
          },
        },
      },
    });

    if (!event) {
      return null;
    }

    return serializePrisma(event);
  } catch (_) {
    return null;
  }
}
