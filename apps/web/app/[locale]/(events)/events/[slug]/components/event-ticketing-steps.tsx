import type { TicketTypeProps } from '@/app/[locale]/(events)/events/[slug]/components/event-ticketing';
import type { SerializedEvent } from '@/app/types';
import { Alert } from '@repo/design-system/components/ui/alert';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  type Control,
  useController,
} from '@repo/design-system/components/ui/form';
import {
  ToggleGroup,
  ToggleGroupItem,
} from '@repo/design-system/components/ui/toggle-group';
import { formatCurrency } from '@repo/design-system/lib/format';
import { formatDate, formatTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { AlertCircle, Minus, Plus } from 'lucide-react';
import type { TicketFormValues } from './event-tabs';

interface StepProps {
  event: SerializedEvent;
}

interface DateStepProps extends StepProps {
  control: Control<TicketFormValues>;
}

export function DateStep({ control, event }: DateStepProps) {
  const { field } = useController({
    name: 'date',
    control,
  });

  return (
    <>
      <h2>Select Date</h2>
      {event.eventDates && (
        <div className="overflow-x-auto">
          <ToggleGroup
            type="single"
            value={field.value ?? undefined}
            onValueChange={field.onChange}
            className="flex h-[120px] min-w-max gap-4"
          >
            {event.eventDates.map((eventDate) => {
              const date = new Date(eventDate.date);
              return (
                <ToggleGroupItem
                  key={eventDate.id}
                  value={eventDate.id.toString()}
                  aria-label={date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    day: 'numeric',
                    month: 'long',
                  })}
                  className="flex aspect-square h-full flex-col gap-1 border p-4 data-[state=on]:border-red-500 data-[state=on]:bg-background"
                >
                  <span className="body-default">
                    {formatDate(date, 'EEEE')}
                  </span>
                  <span className="font-bold text-2xl">{date.getDate()}</span>
                  <span className="body-default">
                    {formatDate(date, 'MMMM')}
                  </span>
                </ToggleGroupItem>
              );
            })}
          </ToggleGroup>
        </div>
      )}
    </>
  );
}

interface TimeStepProps extends StepProps {
  control: Control<TicketFormValues>;
}

export function TimeStep({ control, event }: TimeStepProps) {
  const { field: dateField } = useController({
    name: 'date',
    control,
  });

  const { field: timeField } = useController({
    name: 'timeSlotId',
    control,
  });

  const { field: ticketTypeIdField } = useController({
    name: 'ticketTypeId',
    control,
  });

  const selectedDate = dateField.value;
  const timeSlots = event.eventDates
    .find((ed) => ed.id.toString() === selectedDate)
    ?.timeSlots.sort((a, b) => {
      return new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
    });

  if (!selectedDate) {
    return (
      <>
        <h2>Select Time (MYT)</h2>
        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <p>Please select a date first</p>
        </Alert>
      </>
    );
  }

  return (
    <>
      <h2>Select Time (MYT)</h2>
      <div className="overflow-x-auto">
        <ToggleGroup
          type="single"
          value={timeField.value}
          onValueChange={(value) => {
            timeField.onChange(value);
            // Reset ticket type when time changes
            ticketTypeIdField.onChange('');
          }}
          className="flex min-w-max gap-4"
        >
          {timeSlots?.map((timeSlot) => {
            const startTime = new Date(timeSlot.startTime);
            return (
              <ToggleGroupItem
                key={timeSlot.id}
                value={timeSlot.id.toString()}
                aria-label={startTime.toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit',
                })}
                className="flex aspect-square h-full flex-col gap-1 border p-4 data-[state=on]:border-red-500 data-[state=on]:bg-background"
              >
                <span className="font-bold text-xl">
                  {formatTime(startTime, false)}
                </span>
              </ToggleGroupItem>
            );
          })}
        </ToggleGroup>
      </div>
    </>
  );
}

interface TicketTypeStepProps extends StepProps {
  control: Control<TicketFormValues>;
  availableTicketTypes: Record<string, TicketTypeProps>;
}

export function TicketTypeStep({
  control,
  event,
  availableTicketTypes,
}: TicketTypeStepProps) {
  const { field: timeField } = useController({
    name: 'timeSlotId',
    control,
  });

  const { field: ticketField } = useController({
    name: 'ticketTypeId',
    control,
  });

  if (!timeField.value) {
    return (
      <>
        <h2>Select Ticket Type</h2>
        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <p>Please select a time slot first</p>
        </Alert>
      </>
    );
  }

  const ticketTypes = Object.entries(availableTicketTypes);

  if (ticketTypes.length === 0) {
    return (
      <>
        <h2>Select Ticket Type</h2>
        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <p>No tickets available for this time slot</p>
        </Alert>
      </>
    );
  }

  return (
    <>
      <h2>Select Ticket Type</h2>
      <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
        {ticketTypes.map(([id, ticket]) => {
          const currentDate = new Date();
          const salesStartDate = new Date(ticket.saleStartTime);
          const salesEndDate = new Date(ticket.saleEndTime);
          const salesPeriodActive =
            salesStartDate <= currentDate && salesEndDate >= currentDate;
          const isAvailable = ticket.availableQuantity > 0;
          const eventSoldOut = event.status === 'sold_out';

          // Component to display ticket status
          const TicketStatusDisplay = () => {
            if (!isAvailable || eventSoldOut) {
              return <Badge variant="destructive">Sold Out</Badge>;
            }

            if (!salesPeriodActive) {
              if (salesStartDate > currentDate) {
                return (
                  <div className="caption flex flex-col">
                    <p>Sales starts on</p>
                    <b>{formatDate(salesStartDate)}</b>
                    <b>{formatTime(salesStartDate)}</b>
                  </div>
                );
              }

              return (
                <div className="caption flex flex-col">
                  <p>Sales ended on</p>
                  <b>{formatDate(salesEndDate)}</b>
                  <b>{formatTime(salesEndDate)}</b>
                </div>
              );
            }

            return (
              <div className="caption flex flex-col">
                {ticket.availableQuantity} tickets available
              </div>
            );
          };

          return (
            <button
              key={id}
              type="button"
              onClick={() => ticketField.onChange(id)}
              className={cn(
                'flex min-h-[280px] cursor-pointer flex-col rounded-2xl border p-6',
                { 'border-red-500': ticketField.value === id },
                {
                  'opacity-50':
                    !salesPeriodActive || !isAvailable || eventSoldOut,
                },
                {
                  'cursor-not-allowed':
                    !salesPeriodActive || !isAvailable || eventSoldOut,
                }
              )}
              disabled={!salesPeriodActive || !isAvailable || eventSoldOut}
            >
              <div className="flex-grow space-y-2">
                <h3 className="text-secondary-foreground leading-tight">
                  {ticket.name}
                </h3>
                <p className="caption min-h-[100px] leading-tight">
                  {ticket.description}
                </p>
              </div>

              <div className="mt-auto">
                <div
                  className={cn('relative border-t border-dashed', {
                    'border-red-500': ticketField.value === id,
                  })}
                >
                  <div
                    className={cn(
                      '-left-6 -ml-[1px] -mt-[10px] absolute h-6 w-3 rounded-tr-full rounded-br-full border-t border-r border-b bg-background',
                      { 'border-red-500': ticketField.value === id }
                    )}
                  />
                  <div
                    className={cn(
                      '-right-6 -mr-[1px] -mt-[10px] absolute h-6 w-3 rounded-tl-full rounded-bl-full border-t border-b border-l bg-background',
                      { 'border-red-500': ticketField.value === id }
                    )}
                  />
                </div>
                <div className="pt-4">
                  <h3 className="mb-2 text-secondary-foreground">
                    {formatCurrency(ticket.price)}
                    <span className="caption ml-1">/pax</span>
                  </h3>
                  {/* Ticket status message */}
                  <TicketStatusDisplay />
                </div>
              </div>
            </button>
          );
        })}
      </div>
    </>
  );
}

interface QuantityStepProps {
  control: Control<TicketFormValues>;
  min?: number;
  max?: number;
}

export function QuantityStep({ control, min, max }: QuantityStepProps) {
  const { field: quantityField } = useController({
    name: 'quantity',
    control,
  });

  const { field: ticketTypeField } = useController({
    name: 'ticketTypeId',
    control,
  });

  if (!ticketTypeField.value) {
    return (
      <>
        <h2>Select Quantity</h2>
        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <p>Please select a ticket type first</p>
        </Alert>
      </>
    );
  }

  return (
    <>
      <h2>Select Quantity</h2>
      <div className="flex flex-col items-center gap-4 py-4">
        <div className="flex items-center gap-4">
          <Button
            type="button"
            size="icon"
            onClick={() => quantityField.onChange(quantityField.value - 1)}
            className="rounded-full"
            variant="outline"
            disabled={!min || quantityField.value <= min}
          >
            <Minus />
          </Button>
          <h3 className="w-8 text-center">{quantityField.value}</h3>
          <Button
            type="button"
            size="icon"
            onClick={() => quantityField.onChange(quantityField.value + 1)}
            className="rounded-full"
            variant="outline"
            disabled={!max || quantityField.value >= max}
          >
            <Plus />
          </Button>
        </div>

        {min && quantityField.value <= min && (
          <p className="mt-2 flex items-center text-amber-600">
            <AlertCircle className="mr-1 h-4 w-4" />
            Minimum quantity reached
          </p>
        )}

        {max && quantityField.value >= max && (
          <p className="mt-2 flex items-center text-amber-600">
            <AlertCircle className="mr-1 h-4 w-4" />
            Maximum quantity reached ({max} per order)
          </p>
        )}
      </div>
    </>
  );
}
