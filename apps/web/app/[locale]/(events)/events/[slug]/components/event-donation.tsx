'use client';

import type { SerializedEvent } from '@/app/types';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { Label } from '@repo/design-system/components/ui/label';
import { cn } from '@repo/design-system/lib/utils';
import { useSearchParams } from 'next/navigation';
import { type FocusEvent, useEffect, useState } from 'react';

interface EventDonationProps {
  event: SerializedEvent;
}

export function EventDonation({ event }: EventDonationProps) {
  const donationModule = event.eventModule?.donationModule;
  const minAmount = donationModule?.minAmount
    ? Number(donationModule.minAmount)
    : 10;
  const description = JSON.stringify(donationModule?.description);

  return (
    <div className="space-y-6">
      {donationModule?.description && (
        <div>
          <ReadOnlyEditor
            editorSerializedState={parseEditorContent(description)}
            innerClassName="p-0"
          />
        </div>
      )}

      <div className="space-y-4">
        <Label>Donation Amount</Label>
        <DonationAmountSelector minAmount={minAmount} />
      </div>
    </div>
  );
}

interface DonationAmountSelectorProps {
  minAmount: number;
}

function DonationAmountSelector({ minAmount }: DonationAmountSelectorProps) {
  const searchParams = useSearchParams();

  // Get initial values from URL or defaults
  const initialAmount = searchParams.get('amount') || '';
  const isCustomInitially =
    searchParams.has('amount') &&
    searchParams.get('amount') !== minAmount.toString();

  // State with proper initialization
  const [customAmount, setCustomAmount] = useState<string>(initialAmount);
  const [selectedOption, setSelectedOption] = useState<'single' | 'custom'>(
    isCustomInitially ? 'custom' : 'single'
  );

  // Handle option change with URL update
  const handleOptionChange = (option: 'single' | 'custom') => {
    setSelectedOption(option);

    if (option === 'single') {
      setCustomAmount('');

      // Update URL - remove amount parameter
      const url = new URL(window.location.href);
      url.searchParams.delete('amount');
      window.history.replaceState(null, '', url);
    }
  };

  // Handle custom amount change with URL update
  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);

    // Only update URL if we have a valid amount
    if (value && !Number.isNaN(Number(value))) {
      const url = new URL(window.location.href);
      url.searchParams.set('amount', value);
      window.history.replaceState(null, '', url);
    }
  };

  // Handle blur event to ensure minimum amount
  const handleAmountBlur = (e: FocusEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (value < minAmount) {
      const minAmountStr = minAmount.toString();
      setCustomAmount(minAmountStr);

      // Update URL with minimum amount
      const url = new URL(window.location.href);
      url.searchParams.set('amount', minAmountStr);
      window.history.replaceState(null, '', url);
    }
  };

  // Set default amount to minimum value on component mount
  useEffect(() => {
    // Only set default if no amount is in URL and we're on single donation option
    if (selectedOption === 'single' && !searchParams.has('amount')) {
      const minAmountStr = minAmount.toString();

      // Update URL with minimum amount
      const url = new URL(window.location.href);
      url.searchParams.set('amount', minAmountStr);
      window.history.replaceState(null, '', url);
    }
  }, [minAmount, searchParams, selectedOption]);

  return (
    <div className="grid grid-cols-2 gap-4">
      {/* Single Donation Option */}
      <button
        type="button"
        className={cn(
          'rounded-md border p-4',
          selectedOption === 'single'
            ? 'border-primary bg-primary/5'
            : 'border-input'
        )}
        onClick={() => handleOptionChange('single')}
        onKeyDown={(e) => e.key === 'Enter' && handleOptionChange('single')}
      >
        <div className="flex flex-col items-center">
          <span className="font-medium text-sm">Single Donation</span>
          <div className="mt-2 flex items-baseline">
            <span className="font-bold text-xl">RM{minAmount}</span>
            <span className="ml-1 text-muted-foreground text-xs">
              / one time
            </span>
          </div>
        </div>
      </button>

      {/* Enter Amount Option */}
      <button
        type="button"
        className={cn(
          'rounded-md border p-4',
          selectedOption === 'custom'
            ? 'border-primary bg-primary/5'
            : 'border-input'
        )}
        onClick={() => handleOptionChange('custom')}
        onKeyDown={(e) => e.key === 'Enter' && handleOptionChange('custom')}
      >
        <div className="flex flex-col items-center">
          <span className="font-medium text-sm">Enter Amount</span>
          <div className="mt-2 flex items-baseline">
            {selectedOption === 'custom' ? (
              <div className="flex items-baseline">
                <span className="mr-1 font-bold text-xl">RM</span>
                <input
                  type="number"
                  min={minAmount}
                  value={customAmount}
                  onChange={(e) => handleCustomAmountChange(e.target.value)}
                  onBlur={handleAmountBlur}
                  className="w-16 border-input border-b bg-transparent font-bold text-xl focus:outline-none"
                  placeholder=""
                  autoFocus
                />
              </div>
            ) : (
              <span className="font-bold text-xl">RM</span>
            )}
            <span className="ml-1 text-muted-foreground text-xs">
              / one time
            </span>
          </div>
        </div>
      </button>
    </div>
  );
}
