'use client';
import { Button } from '@repo/design-system/components/ui/button';
import { getMobileOperatingSystem } from '@repo/design-system/lib/utils';
import { MapIcon } from 'lucide-react';

interface MapButtonProps {
  // venue: SerializedEvent['venue'];
  venueName: string;
  venueAddress: string;
}

export function MapButton({ venueName, venueAddress }: MapButtonProps) {
  const getMapUrl = (): string => {
    const os = getMobileOperatingSystem();
    // Combine venue name and address for more accurate location search
    const query = encodeURIComponent(`${venueName}, ${venueAddress}`);

    switch (os) {
      case 'iOS':
        return `maps://maps.apple.com/?q=${query}`;
      case 'Android':
        return `geo:0,0?q=${query}`;
      default:
        return `https://www.google.com/maps/search/?api=1&query=${query}`;
    }
  };

  const handleMapClick = () => {
    const os = getMobileOperatingSystem();
    const mapUrl = getMapUrl();

    if (os === 'Android') {
      // For Android, try to open in native maps app first
      window.location.href = mapUrl;
    } else {
      // For iOS and desktop, open in new tab
      window.open(mapUrl, '_blank');
    }
  };

  // Only show map button if address is available
  if (!venueAddress) {
    return null;
  }

  return (
    <Button variant="default" onClick={handleMapClick} title="Open in Maps">
      See on Map
      <MapIcon className="h-4 w-4" />
    </Button>
  );
}
