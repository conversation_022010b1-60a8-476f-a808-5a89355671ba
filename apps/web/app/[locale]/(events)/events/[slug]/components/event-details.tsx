import { isExpired } from '@/app/[locale]/(events)/_lib/utils';
import type { SerializedEvent } from '@/app/types';
import {} from '@repo/design-system/components/ui/accordion';
import {
  formatDate,
  formatDateRange,
  formatTime,
} from '@repo/design-system/lib/format';
import { CalendarDays, MapPin } from 'lucide-react';
import AddToCalendarButton from './add-to-calendar-button';
import { MapButton } from './map-button';

interface EventDetailsProps {
  event: SerializedEvent;
}

export function EventDetails({ event }: EventDetailsProps) {
  return (
    <div className="space-y-4">
      <h1>{event.title}</h1>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex items-start gap-4 md:w-1/2">
          <div className="flex items-center justify-center rounded-full bg-emerald-100 p-3">
            <CalendarDays size={20} className="text-emerald-600" />
          </div>
          {event.eventDates && event.eventDates.length > 0 && (
            <div className="flex flex-1 flex-col space-y-1">
              <div className="body-large hover:no-underline">
                {formatDateRange(
                  new Date(event.eventDates[0].date),
                  new Date(event.eventDates.at(-1)?.date ?? '')
                )}
              </div>
              {!isExpired(event) &&
                event.eventDates.map((date) => (
                  <div key={date.id} className="flex items-start flex-wrap">
                    <div className="w-32 shrink-0 pb-1">
                      <span className="caption">
                        {formatDate(new Date(date.date), 'EEE, MMM d')}
                      </span>
                    </div>
                    <div className="flex w-full flex-wrap gap-3">
                      {date.timeSlots.map((slot) => (
                        <AddToCalendarButton
                          key={slot.id}
                          event={event}
                          timeSlot={slot}
                        >
                          <span className="caption">
                            {formatTime(new Date(slot.startTime))}
                          </span>
                        </AddToCalendarButton>
                      ))}
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>

        <div className="flex items-start gap-4 md:w-1/2">
          <div className="flex items-center justify-center rounded-full bg-emerald-100 p-3">
            <MapPin size={20} className="text-emerald-600" />
          </div>
          <div className="flex flex-1 flex-col space-y-1">
            <div className="space-y-2">
              <p className="body-large">{event.venueName}</p>
              {/* <p className="text-md">{event.venue?.name}</p>
                <p className="text-secondary-foreground text-sm">
                  {event.venue?.address}
                </p> */}
            </div>
            {!isExpired(event) && (
              <MapButton
                venueName={event.venueName ?? ''}
                venueAddress={event.venueAddress ?? ''}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
