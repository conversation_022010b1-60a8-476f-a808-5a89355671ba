'use client';

import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@repo/design-system/components/ui/carousel';
import Image from 'next/image';
import { useEffect, useState } from 'react';

export const EventCarousel = ({ imageUrls }: { imageUrls: string[] }) => {
  const [carousel, setCarousel] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!carousel) {
      return;
    }

    setCurrent(carousel.selectedScrollSnap() + 1);
    carousel.on('select', () => {
      setCurrent(carousel.selectedScrollSnap() + 1);
    });
  }, [carousel]);

  return (
    <div className="relative overflow-clip rounded-md">
      <Carousel setApi={setCarousel} active={imageUrls.length > 1}>
        <CarouselContent className="-ml-0 rounded-md bg-muted">
          {imageUrls.length > 0 ? (
            imageUrls.map((url, index) => (
              <CarouselItem key={index} className="pl-0">
                <div className="relative aspect-[1200/800]">
                  <Image
                    src={url}
                    alt="Event Image"
                    fill
                    className="object-cover"
                    draggable={false}
                  />
                </div>
              </CarouselItem>
            ))
          ) : (
            <CarouselItem className="relative">
              <div className="absolute inset-0 flex flex-col items-center justify-center p-4 text-center">
                <p className="font-medium text-muted-foreground text-sm">
                  Event images coming soon
                </p>
                <p className="mt-1 text-muted-foreground text-xs">
                  Scroll down for event details
                </p>
              </div>
            </CarouselItem>
          )}
        </CarouselContent>
      </Carousel>

      {/* pagination dots */}
      <div className="-translate-x-1/2 absolute bottom-4 left-1/2 flex gap-2">
        {imageUrls.length > 1 &&
          imageUrls.map((_, index) => (
            <span
              key={index}
              className={`h-2 w-2 rounded-full bg-white ${current === index + 1 ? '' : 'opacity-50'}`}
            />
          ))}
      </div>
    </div>
  );
};
