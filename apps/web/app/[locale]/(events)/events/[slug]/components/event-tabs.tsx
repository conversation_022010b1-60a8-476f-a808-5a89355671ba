'use client';

import { useCart } from '@/app/[locale]/(events)/_lib/cart-context';
import { isExpired } from '@/app/[locale]/(events)/_lib/utils';
import { EventDonation } from '@/app/[locale]/(events)/events/[slug]/components/event-donation';
import type { SerializedEvent } from '@/app/types';
import type { EventUpdate } from '@/app/types/event-update';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@repo/design-system/components/ui/alert';
import { FormProvider, useForm } from '@repo/design-system/components/ui/form';
import { Navigation } from '@repo/design-system/components/ui/lunar/navigation-tab';
import { cn, slugify } from '@repo/design-system/lib/utils';
import { AlertCircleIcon, CalendarOffIcon } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import type React from 'react';
import { useRef } from 'react';
import { useEventListener } from 'usehooks-ts';
import { z } from 'zod';
import { EventAbout } from './event-about';
import { EventFooter } from './event-footer';
import { EventTicketing } from './event-ticketing';
import { EventUpdates } from './event-updates';

export const ticketFormSchema = z.object({
  date: z.string().min(1, 'Please select a date'),
  timeSlotId: z.string().min(1, 'Please select a time slot'),
  ticketTypeId: z.string().min(1, 'Please select a ticket type'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
});

export type TicketFormValues = z.infer<typeof ticketFormSchema>;

interface EventTabsProps {
  event: SerializedEvent;
}

export function EventTabs({ event }: EventTabsProps) {
  const searchParams = useSearchParams();
  const currentTab = (searchParams.get('tab') as string) || 'about';
  const { addToCart, isMutating } = useCart();

  // Build dynamic items array based on event conditions
  const items = ['About'];
  if (!isExpired(event)) {
    items.push(event.eventType === 'ngo' ? 'Registration' : 'Ticketing');
  }
  items.push('Updates');
  if (event.isPremiumEvent && event.eventModule?.donationEnabled) {
    items.push('Donation');
  }

  // Create refs for tab triggers
  const tabRefs = useRef<Record<string, HTMLElement | null>>({
    about: null,
    pricing: null,
    terms: null,
  });

  // Listen for tab change events from the footer component using useEventListener
  useEventListener('tab-change' as keyof WindowEventMap, (e: Event) => {
    const customEvent = e as CustomEvent;
    if (customEvent.detail?.tab) {
      // Programmatically change the tab
      const tabValue = customEvent.detail.tab;
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tabValue);
      window.history.replaceState(null, '', url);

      // Use the ref instead of document.querySelector
      const tabTrigger = tabRefs.current[tabValue];
      if (tabTrigger) {
        tabTrigger.click();
      }
    }
  });

  function navigate() {
    // the callback is fired once the animation is completed
    // to allow smooth transition
  }

  function handleTabChange(tabLabel: string) {
    const value = slugify(tabLabel).toLowerCase();
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);

    // Remove amount parameter if tab is not donation
    if (value !== 'donation') {
      url.searchParams.delete('amount');
    }

    window.history.replaceState(null, '', url);
  }

  const form = useForm<TicketFormValues>({
    resolver: zodResolver(ticketFormSchema),
    defaultValues: {
      date: '',
      timeSlotId: '',
      ticketTypeId: '',
      quantity: 1,
    },
    mode: 'onChange',
  });

  const onSubmit = async (data: TicketFormValues) => {
    // Prevent multiple submissions
    if (isMutating) {
      return;
    }

    try {
      await addToCart({
        eventId: event.id,
        timeSlotId: data.timeSlotId,
        ticketTypeId: data.ticketTypeId,
        quantity: data.quantity,
      });
    } catch (error) {
      console.error('Error creating cart:', error);
    }
  };

  return (
    <FormProvider {...form}>
      {isExpired(event) && (
        <Alert variant="destructive">
          <CalendarOffIcon className="h-4 w-4" />
          <AlertTitle>
            We've wrapped up an amazing event—thank you for being part of it!
          </AlertTitle>
          <AlertDescription className="flex">
            Missed this one? Stay in the loop—follow
            <Link
              href={`/organizers/${event?.organizer?.slug}`}
              className="font-bold hover:underline"
            >
              {event?.organizer.name}
            </Link>
            for updates!
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="w-full">
          <div className="sticky top-16 right-0 left-0 z-10 bg-background">
            <Navigation
              as="nav"
              className="relative mx-auto rounded-2xl bg-foreground/5 max-w-full"
            >
              {({ ready, size, position, duration }) => (
                <div className="relative py-1 overflow-x-auto no-scrollbar">
                  {/* indicator bar */}
                  <div
                    style={
                      {
                        width: size,
                        transform: `translateX(${position})`,
                        transitionDuration: duration,
                      } as React.CSSProperties
                    }
                    className={cn(
                      { hidden: !ready },
                      'absolute bottom-0 left-0 h-[2px] rounded-full bg-primary-red transition-[width,transform]'
                    )}
                  />

                  <Navigation.List
                    as="ul"
                    className="relative flex items-center gap-4 w-full justify-between sm:justify-center px-4"
                  >
                    {items.map((item, index) => (
                      <Navigation.Item
                        key={index}
                        as="li"
                        onActivated={navigate}
                        active={slugify(item).toLowerCase() === currentTab}
                        className="shrink-0 sm:flex-1 min-w-[90px]"
                        onClick={(e: React.MouseEvent) => {
                          e.currentTarget.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'center',
                          });
                        }}
                      >
                        {({ setActive, isActive }) => (
                          <button
                            type="button"
                            onClick={() => {
                              setActive();
                              handleTabChange(item);
                            }}
                            className={cn(
                              isActive
                                ? 'font-medium text-foreground'
                                : 'text-foreground/60 hover:text-foreground',
                              'inline-block w-full py-2 px-0 md:px-4 text-md transition'
                            )}
                            ref={(el) => {
                              if (el) {
                                tabRefs.current[slugify(item).toLowerCase()] =
                                  el;
                              }
                            }}
                          >
                            {item}
                          </button>
                        )}
                      </Navigation.Item>
                    ))}
                  </Navigation.List>
                </div>
              )}
            </Navigation>
          </div>

          <div className="mt-6">
            {currentTab === 'about' && (
              <div className="min-h-[calc(100vh-69px-64px)]">
                <EventAbout event={event} />
              </div>
            )}
            {(currentTab === 'ticketing' || currentTab === 'registration') &&
              !isExpired(event) && (
                <div className="min-h-[calc(100vh-69px-64px)] space-y-6">
                  {event.status === 'sold_out' && (
                    <Alert variant="destructive">
                      <AlertCircleIcon className="h-4 w-4" />
                      <AlertTitle>This event is sold out!</AlertTitle>
                      <AlertDescription className="flex flex-wrap gap-y-0">
                        All tickets have been claimed. Follow
                        <Link
                          href={`/organizers/${event?.organizer?.slug}`}
                          className="font-bold hover:underline"
                        >
                          {event?.organizer.name}
                        </Link>
                        for future events and updates.
                      </AlertDescription>
                    </Alert>
                  )}
                  <EventTicketing event={event} />
                </div>
              )}
            {currentTab === 'updates' && (
              <div className="min-h-[calc(100vh-69px-64px)]">
                <EventUpdates updates={event.updates as EventUpdate[] | null} />
              </div>
            )}
            {currentTab === 'donation' &&
              event.isPremiumEvent &&
              event.eventModule?.donationEnabled && (
                <div className="min-h-[calc(100vh-69px-64px)]">
                  <EventDonation event={event} />
                </div>
              )}
          </div>
        </div>
        <EventFooter event={event} />
      </form>
    </FormProvider>
  );
}
