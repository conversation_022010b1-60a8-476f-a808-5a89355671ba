'use client';

import { CartButton } from '@/app/[locale]/(cart)/components/cart-button';
import { useCart } from '@/app/[locale]/(events)/_lib/cart-context';
import { isExpired } from '@/app/[locale]/(events)/_lib/utils';
import type { SerializedEvent } from '@/app/types';
import { Button } from '@repo/design-system/components/ui/button';
import { useFormContext } from '@repo/design-system/components/ui/form';
import { ArrowRight, Heart, PlusIcon, Share2 } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import type { TicketFormValues } from './event-tabs';

interface EventFooterProps {
  event: SerializedEvent;
}

export function EventFooter({ event }: EventFooterProps) {
  const cartContext = useCart();
  const { cart, totalItems } = cartContext;
  const { formState, handleSubmit } = useFormContext<TicketFormValues>();
  const searchParams = useSearchParams();
  const currentTab = (searchParams.get('tab') as string) || 'about';

  const handleBuyTicket = () => {
    switch (currentTab) {
      case 'ticketing':
      case 'registration': {
        handleSubmit(() => {
          // TODO: callback
        });
        break;
      }

      case 'donation': {
        // Navigate to the donation page with the amount from URL
        const amount = searchParams.get('amount');
        const donationUrl = `/events/${event.slug}/donate${amount ? `?amount=${amount}` : ''}`;
        window.location.href = donationUrl;
        break;
      }

      default: {
        // Update the URL in the address bar without triggering a navigation
        const url = new URL(window.location.href);
        const tabName =
          event.eventType === 'ngo' ? 'registration' : 'ticketing';
        url.searchParams.set('tab', tabName);
        window.history.replaceState(null, '', url.toString());

        // Find and click the tab trigger
        const customEvent = new CustomEvent('tab-change', {
          detail: { tab: tabName },
        });
        window.dispatchEvent(customEvent);
      }
    }
  };

  const getButtonText = () => {
    switch (currentTab) {
      case 'ticketing':
        return 'Add to Cart';
      // case 'registration':
      case 'donation':
        return 'Donate Now';
      default:
        return event.eventType === 'ngo' ? 'Reserve Seats' : 'Buy Ticket';
    }
  };
  const buttonText = getButtonText();

  return (
    <div className="fixed right-0 bottom-0 left-0 z-50 border-t bg-background px-6 py-4">
      <div className="mx-auto flex max-w-4xl items-center justify-between">
        <div className="item-center flex space-x-4">
          <Button size="icon" variant="ghost">
            <Share2 />
          </Button>
          <Button size="icon" variant="ghost">
            <Heart />
          </Button>
        </div>
        <div className="flex items-center gap-4">
          {cart && totalItems > 0 && (
            <div className="flex items-center">
              {/* TODO: show checkout here? should be either or instead of both buttons */}
              <Link
                href={`/checkout/${cart.id}`}
                className="text-sm hover:underline"
              >
                <Button variant="ghost">Checkout</Button>
              </Link>
              <CartButton />
            </div>
          )}
          {!isExpired(event) && (
            <Button
              onClick={handleBuyTicket}
              disabled={
                ((currentTab === 'ticketing' ||
                  currentTab === 'registration') &&
                  !formState.isValid) ||
                formState.isLoading ||
                formState.isSubmitting
              }
            >
              {formState.isLoading || formState.isSubmitting
                ? 'Adding...'
                : buttonText}
              {currentTab === 'ticketing' || currentTab === 'registration' ? (
                <PlusIcon />
              ) : (
                <ArrowRight />
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
