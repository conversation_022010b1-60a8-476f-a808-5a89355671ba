import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/design-system/components/ui/accordion';
import type { FC } from 'react';

export const EventTerms: FC<{ terms: string | null }> = ({ terms }) => {
  return (
    <div className="space-y-6">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="terms">
          <AccordionTrigger className="font-semibold hover:no-underline">
            <h2>Terms</h2>
          </AccordionTrigger>
          {terms && (
            <AccordionContent>
              <ReadOnlyEditor
                editorSerializedState={parseEditorContent(terms)}
                innerClassName="p-0"
              />
            </AccordionContent>
          )}
        </AccordionItem>
      </Accordion>
    </div>
  );
};
