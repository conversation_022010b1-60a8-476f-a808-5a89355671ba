import type { EventUpdate } from '@/app/types/event-update';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/design-system/components/ui/accordion';
import { Alert, AlertTitle } from '@repo/design-system/components/ui/alert';
import { AlertCircleIcon } from 'lucide-react';
import type { FC } from 'react';

interface EventUpdatesProps {
  updates?: EventUpdate[] | null;
}

export const EventUpdates: FC<EventUpdatesProps> = ({ updates }) => {
  // Check if we have dynamic updates
  const updatesArray = Array.isArray(updates) ? (updates as EventUpdate[]) : [];
  const hasUpdates = updatesArray.length > 0;

  // If no updates, show a message
  if (!hasUpdates) {
    return (
      <div className="space-y-6">
        <Alert variant="default">
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle> No updates available for this event</AlertTitle>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Accordion type="multiple" className="w-full">
        {/* Render dynamic updates if available */}
        {hasUpdates &&
          updatesArray.map((update, index) => (
            <AccordionItem key={update.id} value={update.id}>
              <AccordionTrigger
                className={`font-semibold hover:no-underline ${index === 0 ? 'pt-0' : ''}`}
              >
                {update.title}
              </AccordionTrigger>
              <AccordionContent>
                <ReadOnlyEditor
                  editorSerializedState={parseEditorContent(update.content)}
                  innerClassName="p-0"
                />
              </AccordionContent>
            </AccordionItem>
          ))}
      </Accordion>
    </div>
  );
};
