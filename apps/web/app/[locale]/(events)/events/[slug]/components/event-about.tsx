import { EventTerms } from '@/app/[locale]/(events)/events/[slug]/components/event-terms';
import type { SerializedEvent } from '@/app/types';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@repo/design-system/components/ui/avatar';
import { Separator } from '@repo/design-system/components/ui/separator';
import Link from 'next/link';

interface EventAboutProps {
  event: SerializedEvent;
}

export function EventAbout({ event }: EventAboutProps) {
  return (
    <div className="space-y-6">
      <div>
        <ReadOnlyEditor
          editorSerializedState={parseEditorContent(event.description)}
          innerClassName="p-0"
        />
      </div>
      <Separator />
      <EventTerms terms={event.terms} />
      <Separator />
      <div className="flex items-center justify-between">
        <div className="flex flex-col-reverse items-start gap-2 md:flex-row md:items-center">
          <div>
            <Link href={`/organizers/${event.organizer?.slug}`}>
              <p className="font-medium">{event.organizer?.name}</p>
            </Link>
            <p className="text-muted-foreground text-sm">
              {event.organizer?.description}
            </p>
          </div>
          <Avatar className="size-[128px] md:size-[100px]">
            <Link href={`/organizers/${event.organizer?.slug}`}>
              <AvatarImage src={event.organizer?.logo ?? ''} />
            </Link>
            <AvatarFallback>{event.organizer?.name?.charAt(0)}</AvatarFallback>
          </Avatar>
        </div>
        {/* <Button>Follow</Button> */}
      </div>
      <Separator />
    </div>
  );
}
