'use client';

import type { SerializedEvent } from '@/app/types';
import { Button } from '@repo/design-system/components/ui/button';
import { FormField, FormItem } from '@repo/design-system/components/ui/form';
import { useFormContext } from '@repo/design-system/components/ui/form';
import { Separator } from '@repo/design-system/components/ui/separator';
import { toast } from '@repo/design-system/components/ui/sonner';
import { RefreshCcw } from 'lucide-react';
import { useEffect, useState } from 'react';
import { refreshInventory } from '../actions';
import type { TicketFormValues } from './event-tabs';
import {
  DateStep,
  QuantityStep,
  TicketTypeStep,
  TimeStep,
} from './event-ticketing-steps';

interface EventTicketingProps {
  event: SerializedEvent;
}

export interface TicketTypeProps {
  id: string;
  name: string;
  description: string | null;
  price: number;
  availableQuantity: number;
  maxPerOrder: number;
  minPerOrder: number;
  saleStartTime: string;
  saleEndTime: string;
}

export function EventTicketing({ event }: EventTicketingProps) {
  // const router = useRouter();
  const form = useFormContext<TicketFormValues>();
  const { watch, reset, setValue } = form;
  const formValues = watch();
  const dateValue = watch('date');
  const timeSlotId = watch('timeSlotId');
  const ticketTypeId = watch('ticketTypeId');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Reset lower step values when higher step values change
  // When date changes, reset timeSlotId, ticketTypeId, and quantity
  useEffect(() => {
    if (dateValue) {
      setValue('timeSlotId', '');
      setValue('ticketTypeId', '');
      setValue('quantity', 1);
    }
  }, [dateValue, setValue]);

  // When timeSlotId changes, reset ticketTypeId and quantity
  useEffect(() => {
    if (timeSlotId) {
      setValue('ticketTypeId', '');
      setValue('quantity', 1);
    }
  }, [timeSlotId, setValue]);

  // Find selected time slot's inventory
  const selectedTimeSlot = timeSlotId
    ? event.eventDates
        ?.flatMap((ed) => ed.timeSlots)
        .find((ts) => ts.id === timeSlotId)
    : null;

  // Map inventory to ticket types
  const availableTicketTypes =
    selectedTimeSlot?.inventory.reduce(
      (acc, inv) => {
        acc[inv.ticketType.id] = {
          ...inv.ticketType,
          availableQuantity: inv.quantity,
        };
        return acc;
      },
      {} as Record<string, TicketTypeProps>
    ) ?? {};

  // Function to refresh inventory by cleaning up expired carts
  const handleRefreshInventory = async () => {
    setIsRefreshing(true);
    try {
      const result = await refreshInventory(event.slug);

      if (result.success) {
        toast.success('Inventory Refreshed');
        // Reset the form to force a refetch of available inventory
        reset({
          date: formValues.date,
          timeSlotId: '',
          ticketTypeId: '',
          quantity: 1,
        });
      } else {
        toast.error('Failed to refresh inventory');
      }
    } catch (error) {
      toast.error('Failed to refresh inventory. Please try again.');
      console.error('Error refreshing inventory:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-start justify-between">
        <h2>
          {event.eventType === 'ngo' ? 'Select Donation' : 'Select Tickets'}
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshInventory}
          disabled={isRefreshing}
        >
          <RefreshCcw
            className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
          />
          {isRefreshing
            ? 'Refreshing...'
            : event.eventType === 'ngo'
              ? 'Refresh Donations'
              : 'Refresh Tickets'}
        </Button>
      </div>
      <FormField
        control={form.control}
        name="date"
        render={() => (
          <FormItem>
            <DateStep control={form.control} event={event} />
          </FormItem>
        )}
      />
      <Separator />
      <FormField
        control={form.control}
        name="timeSlotId"
        render={() => (
          <FormItem>
            <TimeStep control={form.control} event={event} />
          </FormItem>
        )}
      />
      <Separator />
      <FormField
        control={form.control}
        name="ticketTypeId"
        render={() => (
          <FormItem>
            <TicketTypeStep
              control={form.control}
              event={event}
              availableTicketTypes={availableTicketTypes}
            />
          </FormItem>
        )}
      />
      <Separator />
      <FormField
        control={form.control}
        name="quantity"
        render={() => (
          <FormItem>
            <QuantityStep
              control={form.control}
              min={availableTicketTypes[ticketTypeId]?.minPerOrder}
              max={availableTicketTypes[ticketTypeId]?.maxPerOrder}
            />
          </FormItem>
        )}
      />
    </div>
  );
}
