'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useInterval } from 'usehooks-ts';
import { checkDonationStatus } from './action';

type PaymentStatus = 'success' | 'failure' | 'cancel' | 'pending';

// Confirmation dialog component for abandoning donation process
interface AbandonDonationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  eventSlug: string;
}

function AbandonDonationDialog({
  open,
  onOpenChange,
  eventSlug,
}: AbandonDonationDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Leave donation process?</AlertDialogTitle>
          <AlertDialogDescription>
            Your donation has not been completed. You will need to start the
            donation process again if you want to donate to this event.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Link href={`/events/${eventSlug}`}>Continue</Link>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Success component
function SuccessContent({
  donationId,
  eventSlug,
  initialPaymentState,
}: {
  donationId: string | null;
  eventSlug: string;
  initialPaymentState: string;
}) {
  const [paymentStatus, setPaymentStatus] =
    useState<string>(initialPaymentState);
  const [pollingCount, setPollingCount] = useState(0);

  // Determine if we should poll based on donation ID and payment state
  const shouldPoll =
    donationId &&
    initialPaymentState !== 'confirmed' &&
    initialPaymentState !== 'failed' &&
    pollingCount < 10;

  // Function to check donation status
  const checkStatus = async () => {
    if (!donationId) {
      return;
    }

    try {
      // Check the current donation status
      const result = await checkDonationStatus(donationId);

      // If the payment is success (completed and paid)
      if (result.status === 'completed' && result.paymentStatus === 'paid') {
        // Payment is confirmed - update UI
        setPaymentStatus('confirmed');
      }
      // If the payment has failed or been cancelled
      else if (
        result.status === 'cancelled' ||
        result.paymentStatus === 'cancelled'
      ) {
        // Payment failed - update UI
        setPaymentStatus('failed');
      }

      // Increment polling count
      setPollingCount((prev: number) => prev + 1);
    } catch (error) {
      console.error('Error checking donation status:', error);
      // Increment polling count even on error to avoid infinite polling
      setPollingCount((prev: number) => prev + 1);
    }
  };

  // Set up polling interval
  useInterval(
    checkStatus,
    // Poll every 2 seconds if we should poll, otherwise stop polling
    shouldPoll ? 2000 : null
  );

  // If payment is still processing, show loading state
  if (paymentStatus === 'processing') {
    return (
      <>
        <CardHeader className="flex flex-col items-center space-y-2 text-center">
          <div className="loading loading-spinner loading-lg text-primary" />
          <CardTitle className="text-2xl">Processing Payment</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground">
            We're processing your donation. This may take a moment. Please don't
            close this page.
          </p>
          {pollingCount > 0 && (
            <p className="mt-2 text-muted-foreground text-sm">
              Checking payment status... ({pollingCount}/10)
            </p>
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button variant="outline" asChild className="w-full">
            <Link href={`/events/${eventSlug}`}>Return to Event</Link>
          </Button>
        </CardFooter>
      </>
    );
  }

  // If payment failed, show failure content
  if (paymentStatus === 'failed') {
    return <FailureContent eventSlug={eventSlug} />;
  }

  // Otherwise, show success content
  return (
    <>
      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <CheckCircle className="h-16 w-16 text-green-500" />
        <CardTitle className="text-2xl">Donation Successful</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          Your donation has been completed successfully. Thank you for your
          generosity!
        </p>
        {donationId && (
          <div className="mt-4 rounded-md bg-muted p-4">
            <p className="font-medium">Donation ID: {donationId}</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button variant="outline" asChild className="w-full">
          <Link href={`/events/${eventSlug}`}>Return to Event</Link>
        </Button>
      </CardFooter>
    </>
  );
}

// Failure component
function FailureContent({ eventSlug }: { eventSlug: string }) {
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <AbandonDonationDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        eventSlug={eventSlug}
      />

      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <XCircle className="h-16 w-16 text-red-500" />
        <CardTitle className="text-2xl">Donation Failed</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          We couldn't process your donation. Please try again or use a different
          payment method.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/events/${eventSlug}/donate`}>Try Again</Link>
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => setShowDialog(true)}
        >
          Return to Event
        </Button>
      </CardFooter>
    </>
  );
}

// Cancel component
function CancelContent({ eventSlug }: { eventSlug: string }) {
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <AbandonDonationDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        eventSlug={eventSlug}
      />

      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <AlertCircle className="h-16 w-16 text-amber-500" />
        <CardTitle className="text-2xl">Donation Cancelled</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          Your donation was cancelled. You can try again if you'd like to
          complete your donation.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/events/${eventSlug}/donate`}>Try Again</Link>
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => setShowDialog(true)}
        >
          Return to Event
        </Button>
      </CardFooter>
    </>
  );
}

interface ConfirmationClientProps {
  donationId: string | null;
  eventSlug: string;
  initialStatus: string;
  initialPaymentState: string;
}

export function ConfirmationClient({
  donationId,
  eventSlug,
  initialStatus,
  initialPaymentState,
}: ConfirmationClientProps) {
  const searchParams = useSearchParams();
  // Use the server-provided status as fallback if client-side params are not available
  const status = (searchParams.get('status') as PaymentStatus) || initialStatus;

  return (
    <div className="container flex w-full justify-center py-12">
      <Card className="max-w-md">
        {status === 'success' && (
          <SuccessContent
            donationId={donationId}
            eventSlug={eventSlug}
            initialPaymentState={initialPaymentState}
          />
        )}
        {status === 'failure' && <FailureContent eventSlug={eventSlug} />}
        {status === 'cancel' && <CancelContent eventSlug={eventSlug} />}
      </Card>
    </div>
  );
}
