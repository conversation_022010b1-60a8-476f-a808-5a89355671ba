import { getDonationInfo } from './action';
import { ConfirmationClient } from './client';

export default async function DonationConfirmationPage({
  params,
  searchParams,
}: {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Get the event slug from params
  const { slug } = await params;

  // Get the donation ID from search params
  const resolvedSearchParams = await searchParams;
  const donationId =
    typeof resolvedSearchParams.donationId === 'string'
      ? resolvedSearchParams.donationId
      : null;

  // Pre-fetch donation information on the server side
  const donationInfo = await getDonationInfo(donationId, resolvedSearchParams);

  // Pass the donation info to the client component
  return (
    <ConfirmationClient
      donationId={donationId}
      eventSlug={slug}
      initialStatus={donationInfo.status}
      initialPaymentState={donationInfo.initialPaymentState}
    />
  );
}
