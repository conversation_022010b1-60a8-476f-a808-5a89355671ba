'use server';

import { database } from '@repo/database';
import { DonationStatus, PaymentStatus } from '@repo/database/types';
import { log } from '@repo/observability/log';

/**
 * Check the current status of a donation
 * This is used to poll for payment confirmation from the webhook
 */
export async function checkDonationStatus(donationId: string | null) {
  if (!donationId) {
    return { status: 'unknown', paymentStatus: 'unknown' };
  }

  try {
    const donation = await database.eventDonation.findUnique({
      where: { id: donationId },
      select: {
        status: true,
        paymentStatus: true,
      },
    });

    if (!donation) {
      log.warn(`Donation not found when checking status: ${donationId}`);
      return { status: 'not_found', paymentStatus: 'unknown' };
    }

    return {
      status: donation.status,
      paymentStatus: donation.paymentStatus,
    };
  } catch (error) {
    log.error('Error checking donation status', { error, donationId });
    return { status: 'error', paymentStatus: 'error' };
  }
}

/**
 * Get donation information from donationId and search params
 * This is used to pre-fetch donation status on the server side
 */
export async function getDonationInfo(
  donationId: string | null,
  searchParams: { [key: string]: string | string[] | undefined }
) {
  try {
    // Get status from search params
    const status =
      typeof searchParams.status === 'string' ? searchParams.status : 'pending';

    // If we don't have a donationId, return early
    if (!donationId) {
      return {
        donationId,
        status,
        donationStatus: null,
        paymentStatus: null,
        initialPaymentState: 'processing',
        eventSlug: null,
      };
    }

    // Get the donation with event information
    const donation = await database.eventDonation.findUnique({
      where: { id: donationId },
      include: {
        event: {
          select: {
            slug: true,
          },
        },
      },
    });

    if (!donation) {
      log.warn(`Donation not found: ${donationId}`);
      return {
        donationId,
        status,
        donationStatus: null,
        paymentStatus: null,
        initialPaymentState: 'processing',
        eventSlug: null,
      };
    }

    // Check donation status
    const donationStatus = await checkDonationStatus(donationId);

    // Determine the initial payment state based on the donation status
    let initialPaymentState = 'processing';

    if (
      donationStatus.status === DonationStatus.completed &&
      donationStatus.paymentStatus === PaymentStatus.paid
    ) {
      initialPaymentState = 'confirmed';
    } else if (
      donationStatus.status === DonationStatus.cancelled ||
      donationStatus.paymentStatus === PaymentStatus.cancelled
    ) {
      initialPaymentState = 'failed';
    }

    return {
      donationId,
      status,
      donationStatus: donationStatus.status,
      paymentStatus: donationStatus.paymentStatus,
      initialPaymentState,
      eventSlug: donation.event?.slug || null,
    };
  } catch (error) {
    log.error('Error getting donation info', { error, donationId });
    return {
      donationId,
      status: 'error',
      donationStatus: null,
      paymentStatus: null,
      initialPaymentState: 'processing',
      eventSlug: null,
    };
  }
}
