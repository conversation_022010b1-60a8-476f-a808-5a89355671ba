'use server';

import { DONATE_CHIP_PAYMENT_METHODS } from '@/app/[locale]/(checkout)/checkout/utils/constant';
import type { DonationFormValues } from '@/app/[locale]/(events)/events/[slug]/donate/components/donation-form';
import { database } from '@repo/database';
import CustomPaymentKeyService from '@repo/database/payment-keys';
import { DonationStatus, PaymentStatus } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { chip } from '@repo/payments';
import { type ChipClient, createChipClient } from '@repo/payments/chip';

/**
 * Step 1: Validate donation form data
 * Checks if the donation amount meets minimum requirements and validates other form fields
 */
async function validateDonationData(
  formData: DonationFormValues,
  donationModuleId: string
) {
  // Get donation module to check minimum amount
  const donationModule = await database.donationModule.findUnique({
    where: { id: donationModuleId },
  });

  if (!donationModule) {
    throw new Error('Donation module not found');
  }

  const minAmount = donationModule.minAmount
    ? Number(donationModule.minAmount)
    : 1;

  // Validate minimum donation amount
  if (formData.amount < minAmount) {
    throw new Error(`Minimum donation amount is ${minAmount}`);
  }

  // Return validated data and module
  return {
    formData,
    donationModule,
  };
}

/**
 * Step 2: Create donation record with pending status
 * Creates a donation record in the database with initial pending status
 */
async function createDonationRecord(
  formData: DonationFormValues,
  eventId: string,
  donationModuleId: string
) {
  const donation = await database.eventDonation.create({
    data: {
      donationModuleId,
      eventId,
      name: formData.fullName,
      email: formData.email,
      phone: formData.phone,
      companyName: formData.companyName,
      companyLogo: formData.companyLogo,
      message: formData.message,
      amount: formData.amount,
      paymentMethod: formData.paymentMethod,
      status: DonationStatus.pending,
      paymentStatus: PaymentStatus.pending,
    },
  });

  log.info('Donation record created', { donationId: donation.id });
  return donation;
}

/**
 * Step 3: Process payment with Chip
 * Initiates payment processing with the Chip payment gateway
 */
async function processChipPayment(
  chipClient: ChipClient,
  donation: Awaited<ReturnType<typeof createDonationRecord>>,
  eventSlug: string,
  formPaymentMethod: string
) {
  const selectedChipPaymentMethod = DONATE_CHIP_PAYMENT_METHODS[formPaymentMethod];

  if (!selectedChipPaymentMethod) {
    throw new Error('Invalid payment method');
  }

  const donationAmount = Number(donation.amount);
  // Prepare product information for Chip
  const product = {
    name: 'Event Donation',
    price: donationAmount, // dont charge processing fee for donation
    quantity: '1',
    description: `Event donation for ${eventSlug}`,
    category: 'donation',
  };

  try {
    // Create payment with Chip
    const payment = await chipClient.createPayment({
      amount: Number(donation.amount),
      currency: 'MYR',
      products: [product],
      email: donation.email,
      fullName: donation.name,
      phone: donation.phone ?? '',
      successUrl: `${process.env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`,
      failureUrl: `${process.env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`,
      successRedirectUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/events/${eventSlug}/donate/confirmation?status=success&donationId=${donation.id}`,
      failureRedirectUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/events/${eventSlug}/donate/confirmation?status=failure`,
      cancelRedirectUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/events/${eventSlug}/donate/confirmation?status=cancel`,
      reference: `Event Donation #${donation.id}`,
      notes: `RM ${donationAmount} Event Donation from ${donation.name}`,
      paymentMethods: selectedChipPaymentMethod.methods || [],
    });

    log.info('Chip payment created successfully', {
      paymentId: payment.id,
      donationId: donation.id,
      checkoutUrl: payment.checkout_url,
    });

    return payment;
  } catch (error) {
    log.error('Failed to process payment with Chip', {
      error,
      donationId: donation.id,
    });
    throw new Error('Payment processing failed');
  }
}

/**
 * Step 4: Update donation record with payment information
 * Updates the donation record with the payment ID and checkout URL
 */
async function updateDonationWithPaymentInfo(
  donation: Awaited<ReturnType<typeof createDonationRecord>>,
  payment: Awaited<ReturnType<typeof processChipPayment>>
) {
  await database.eventDonation.update({
    where: { id: donation.id },
    data: {
      transactionId: payment.id,
    },
  });

  log.info('Donation updated with payment info', {
    donationId: donation.id,
    paymentId: payment.id,
  });

  return { donation, payment };
}

// checks & return the custom payment module if custom payment is enabled
async function validateCustomPaymentModule(eventId?: string | null) {
  if (!eventId) {
    throw new Error('EventId is required');
  }

  // fetch event module
  const eventModule = await database.eventModule.findUnique({
    where: {
      eventId,
    },
    select: {
      id: true,
      customPaymentEnabled: true,
      customPaymentModule: {
        select: {
          id: true,
          chipsEnabled: true,
          stripeEnabled: true,
        },
      },
    },
  });

  if (!eventModule) {
    throw new Error('Event module not found');
  }

  // further fetch custom payment module if custom payment is enabled
  if (eventModule.customPaymentEnabled) {
    if (!eventModule.customPaymentModule) {
      throw new Error('Custom payment module not found');
    }

    return eventModule.customPaymentModule;
  }

  return null;
}

/**
 * Main donation submission function that orchestrates the entire donation process
 */
export async function submitDonation(
  formData: DonationFormValues,
  eventId: string,
  eventSlug: string,
  donationModuleId: string
) {
  try {
    // Step 1: Validate donation data
    await validateDonationData(formData, donationModuleId);

    // Step 2: Create donation record with pending status
    const donation = await createDonationRecord(
      formData,
      eventId,
      donationModuleId
    );

    // Step 7: check if event is using custom payment
    let chipClient: ChipClient = chip;

    const customPaymentModule = await validateCustomPaymentModule(eventId);

    // if chip custom payment is enabled
    if (customPaymentModule?.chipsEnabled) {
      // fetch keys of respective payment option from organizer
      const { chipSecretKey, chipBrandId, chipPublicKey } =
        await CustomPaymentKeyService.chipKeys(eventId);

      // create new chip client with the custom keys
      if (chipSecretKey && chipBrandId && chipPublicKey) {
        const customChipClient = createChipClient({
          secretKey: chipSecretKey,
          brand: chipBrandId,
          publicKey: chipPublicKey,
        });

        // override default chip client
        chipClient = customChipClient;
      }
    }

    // Step 3: Process payment with Chip
    const payment = await processChipPayment(
      chipClient,
      donation,
      eventSlug,
      formData.paymentMethod
    );

    // Step 4: Update donation record with payment information
    await updateDonationWithPaymentInfo(donation, payment);

    // Step 5: Return success response with redirect URL
    return {
      success: true,
      donationId: donation.id,
      redirectUrl: payment.checkout_url,
    };
  } catch (error) {
    log.error('Error submitting donation', { error });
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to process donation',
    };
  }
}

/**
 * Server action to handle company logo upload
 */
export async function uploadCompanyLogo(formData: FormData) {
  try {
    // Extract the file and access level from the form data
    const file = formData.get('file') as File;
    const access = (formData.get('access') as string) || 'public';

    if (!file) {
      return { success: false, error: 'Please select a file to upload' };
    }

    // Create a new FormData object for the API request
    const apiFormData = new FormData();
    apiFormData.append('file', file);
    apiFormData.append('access', access);

    // Create the API URL
    const apiUrl = `${process.env.NEXT_PUBLIC_API_URL}/api/upload`;

    // Set up headers with the secure upload key
    const headers = new Headers();
    const uploadKey = process.env.CUSTOM_B2_UPLOAD_KEY || '';
    headers.set('x-api-upload-key', uploadKey);

    // Make the API request
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers,
      body: apiFormData,
    });

    // Get the response data
    const data = await response.json();

    // Return the API response
    return data;
  } catch (error) {
    log.error('Error uploading company logo', { error });
    return {
      success: false,
      error: 'Something went wrong with your upload',
    };
  }
}
