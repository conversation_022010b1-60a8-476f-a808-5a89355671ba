import { DonationForm } from '@/app/[locale]/(events)/events/[slug]/donate/components/donation-form';
import type { ReactElement } from 'react';
import { getEvent } from '../actions';
import { getPaymentMethods } from '@/app/[locale]/(checkout)/checkout/utils/constant';
import CustomPaymentKeyService from '@repo/database/payment-keys';

type PageProps = {
  params: Promise<{ locale: string; slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export default async function DonatePage({
  params,
  searchParams,
}: PageProps): Promise<ReactElement> {
  const { slug } = await params;
  const resolvedSearchParam = await searchParams;
  const amount = resolvedSearchParam.amount as string | undefined;

  if (!slug) {
    return <div className="p-4">Event not found</div>;
  }

  // Fetch the event to get donation module details
  const event = await getEvent(slug);

  if (
    !event ||
    !event.eventModule?.donationEnabled ||
    !event.eventModule.donationModule
  ) {
    return <div className="p-4">Donations are not enabled for this event</div>;
  }

  // Server-side: determine available payment methods securely
  let paymentMethods = await getPaymentMethods(false);
  try {
    const hasByok =
      event?.eventModule?.customPaymentEnabled &&
      event.eventModule.customPaymentModule?.chipsEnabled;
    if (hasByok && event?.id) {
      const { chipSecretKey, chipBrandId } =
        await CustomPaymentKeyService.chipKeys(event.id);
      paymentMethods = await getPaymentMethods(
        true,
        chipSecretKey,
        chipBrandId
      );
    }
  } catch {
    paymentMethods = await getPaymentMethods(false);
  }

  return (
    <div className="container mx-auto max-w-4xl my-10">
      <h1 className="mb-6">Donate to {event.title}</h1>
      <DonationForm
        event={event}
        initialAmount={amount ? Number.parseFloat(amount) : undefined}
        paymentMethods={paymentMethods}
      />
    </div>
  );
}
