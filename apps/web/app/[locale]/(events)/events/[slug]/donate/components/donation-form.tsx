'use client';

import type { SerializedEvent } from '@/app/types';
import { PaymentMethodPicker } from '@/components/payment-method-picker';
import type { ChipPaymentMethod } from '@/app/[locale]/(checkout)/checkout/utils/constant';
import { zodResolver } from '@hookform/resolvers/zod';
import { Upload } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { Separator } from '@repo/design-system/components/ui/separator';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import Image from 'next/image';
import Link from 'next/link';
import { type ChangeEvent, useState } from 'react';
import * as z from 'zod';
import { submitDonation, uploadCompanyLogo } from '../action';

interface DonationFormProps {
  event: SerializedEvent;
  initialAmount?: number;
  paymentMethods: Record<string, ChipPaymentMethod>;
}

const donationFormSchema = z.object({
  fullName: z.string().min(1, 'Please enter your full name'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  companyName: z.string().optional(),
  companyLogo: z.string().optional(),
  message: z.string().optional(),
  amount: z.number().min(1, 'Minimum donation amount is 1'),
  paymentMethod: z.enum(['fpx', 'card', 'duitnow', 'eWallet'], {
    required_error: 'Please select a payment method',
    invalid_type_error: 'Please select a valid payment method',
  }),
});

export type DonationFormValues = z.infer<typeof donationFormSchema>;

export function DonationForm({
  event,
  initialAmount,
  paymentMethods,
}: DonationFormProps) {
  const donationModule = event.eventModule?.donationModule;
  const minAmount = donationModule?.minAmount
    ? Number(donationModule.minAmount)
    : 1;

  // Use the initialAmount from props or fall back to minAmount
  const donationAmount = initialAmount || minAmount;

  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const form = useForm<DonationFormValues>({
    resolver: zodResolver(donationFormSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      companyName: '',
      companyLogo: '',
      message: '',
      amount: donationAmount,
      paymentMethod:
        (Object.keys(
          paymentMethods
        )[0] as DonationFormValues['paymentMethod']) ?? 'fpx',
    },
  });

  // Function to handle company logo upload
  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(10); // Start with some initial progress

      const formData = new FormData();
      formData.append('file', file);
      formData.append('access', 'public');

      // Set progress to indicate server processing
      setUploadProgress(50);

      // Use the server action to upload the file
      const response = await uploadCompanyLogo(formData);

      // Handle the response
      if (response.success) {
        setPreviewUrl(response.url);
        form.setValue('companyLogo', response.url);
        toast.success('Logo uploaded successfully');
      } else {
        toast.error(response.error || 'Failed to upload logo');
      }
    } catch (_) {
      toast.error('Failed to upload logo');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Function to handle image URL input
  const handleImageUrlChange = (url: string) => {
    if (url) {
      setPreviewUrl(url);
      form.setValue('companyLogo', url);
    }
  };

  const onSubmit = async (data: DonationFormValues) => {
    if (data.amount < minAmount) {
      form.setError('amount', {
        message: `Minimum donation amount is ${minAmount}`,
      });
      return;
    }

    if (!donationModule) {
      toast.error('Failed to process your donation, please try again later');
      return;
    }

    toast.loading('Processing your donation...', { id: 'donation' });

    try {
      const result = await submitDonation(
        data,
        event.id,
        event.slug,
        donationModule.id
      );

      if (result?.success && result.redirectUrl) {
        toast.success('Donation submitted successfully', { id: 'donation' });
        window.location.href = result.redirectUrl;
        return;
      }
      toast.error('Failed to process your donation', { id: 'donation' });
    } catch (error) {
      console.error('Donation error:', error);
      toast.error('Failed to process your donation', { id: 'donation' });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Donation Summary */}
        <div className="space-y-4">
          <h2 className="font-semibold text-xl">Donation Summary</h2>

          <div className="rounded-lg border p-4">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">{event.title}</h3>
                  <p className="text-muted-foreground text-sm">
                    One-time donation
                  </p>
                </div>
                <div className="text-right">
                  <div className="flex items-center">
                    <span className="mr-2 font-semibold">RM</span>
                    <span className="font-semibold text-lg">
                      {donationAmount}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Separator />

        <PaymentMethodPicker paymentMethods={paymentMethods} />
        <Separator />

        {/* Donor Information */}
        <div className="space-y-4">
          <h2 className="font-semibold text-xl">Donor Information</h2>

          <div className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your full name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email *</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your phone number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="companyName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter your company name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="companyLogo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Logo (Optional)</FormLabel>
                  <Tabs defaultValue="url" className="w-full">
                    <TabsList className="w-full">
                      <TabsTrigger value="url" className="w-full">
                        Logo URL
                      </TabsTrigger>
                      <TabsTrigger value="file" className="w-full">
                        Upload Logo
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="url">
                      <div className="flex flex-col gap-4 ">
                        {previewUrl && (
                          <div className="w-full justify-items-center overflow-hidden rounded-md">
                            <Image
                              src={previewUrl}
                              alt="Company logo preview"
                              width={400}
                              height={400}
                              className="object-contain"
                            />
                          </div>
                        )}

                        <div className="flex flex-col gap-2">
                          <Input
                            placeholder="https://example.com/logo.jpg"
                            {...field}
                            disabled={isUploading}
                          />
                          <Button
                            type="button"
                            variant="secondary"
                            onClick={() =>
                              handleImageUrlChange(field.value || '')
                            }
                            disabled={!field.value || isUploading}
                            className="mt-2 w-full sm:w-auto"
                          >
                            Preview Logo
                          </Button>
                        </div>
                        <FormDescription>
                          Enter the URL of your company logo. We accept JPG,
                          PNG, and SVG formats up to 5MB in size.
                          <br />
                          For best results, use an image that's 400x400px or
                          smaller.
                        </FormDescription>
                      </div>
                    </TabsContent>

                    <TabsContent value="file">
                      <div className="flex flex-col gap-4">
                        {previewUrl && (
                          <div className="w-full justify-items-center overflow-hidden rounded-md">
                            <Image
                              src={previewUrl}
                              alt="Company logo preview"
                              width={400}
                              height={400}
                              className="object-contain"
                            />
                          </div>
                        )}

                        <div className="flex flex-col gap-2">
                          <div className="flex h-10 w-full items-center justify-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                            {isUploading ? (
                              <div className="flex items-center gap-2">
                                <span className="loading loading-spinner loading-sm" />
                                <span>{uploadProgress}% Uploading...</span>
                              </div>
                            ) : (
                              <Label
                                htmlFor="logo-upload"
                                className="flex w-full cursor-pointer items-center justify-center gap-2"
                              >
                                <Upload className="h-4 w-4" />
                                <span>Upload Logo</span>
                              </Label>
                            )}
                          </div>
                          <Input
                            id="logo-upload"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleImageUpload}
                            disabled={isUploading}
                          />
                          <input type="hidden" {...field} />
                        </div>
                        <FormDescription>
                          Enter the URL of your company logo. We accept JPG,
                          PNG, and SVG formats up to 5MB in size.
                          <br />
                          For best results, use an image that's 400x400px or
                          smaller.
                        </FormDescription>
                      </div>
                    </TabsContent>
                  </Tabs>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add a message with your donation"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Separator />

        <div className="flex items-center justify-between pt-4">
          <Link
            href={`/events/${event.slug}`}
            className="text-muted-foreground text-sm hover:underline"
          >
            Return to event
          </Link>

          <Button
            type="submit"
            size="sm"
            disabled={form.formState.isSubmitting}
          >
            {form.formState.isSubmitting ? 'Processing...' : 'Donate Now'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
