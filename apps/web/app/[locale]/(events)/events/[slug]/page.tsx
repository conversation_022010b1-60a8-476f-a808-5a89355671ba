import { Separator } from '@repo/design-system/components/ui/separator';
import { redirect } from 'next/navigation';
import type { Metadata } from 'next/types';
import type { ReactElement } from 'react';
import { getEvent } from './actions';
import { EventCarousel } from './components/event-carousel';
import { EventDetails } from './components/event-details';
import { EventTabs } from './components/event-tabs';

type PageProps = {
  readonly params: Promise<{
    slug: string;
    locale: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const slug = (await params).slug;
  const event = await getEvent(slug);

  // Use the first carousel image if available, or fallback to site default
  const imageUrl = event?.carouselImageUrls?.[0] || '/images/default-event.jpg';
  const siteUrl =
    process.env.NEXT_PUBLIC_WEB_URL ||
    process.env.NEXT_PUBLIC_SITE_URL ||
    'https://ticketcare-web.vercel.app/';
  const ogImageUrl = new URL(
    imageUrl.startsWith('http') ? imageUrl : `${siteUrl}${imageUrl}`
  );

  const canonicalUrl = `${siteUrl}/events/${slug}`;

  return {
    title: `${event?.title} by ${event?.organizer.name} | TicketCARE`,
    description: `Get your ${event?.title} tickets by ${event?.organizer.name}! Seamless checkout on TicketCARE.`,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: `${event?.title} | TicketCARE`,
      description: `Get your ${event?.title} tickets by ${event?.organizer.name}! Seamless checkout on TicketCARE.`,
      url: canonicalUrl,
      siteName: 'TicketCARE',
      images: [
        {
          url: ogImageUrl.toString(),
          width: 1200,
          height: 630,
          alt: event?.title || 'Event image',
        },
      ],
    },
  };
}

export default async function EventPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { slug } = await params;

  const event = await getEvent(slug);

  if (!event) {
    redirect('/');
  }

  return (
    <div className="w-full space-y-8 my-10">
      <EventCarousel imageUrls={event.carouselImageUrls} />
      <div className="space-y-6 p-4">
        <EventDetails event={event} />
        <Separator />
        <EventTabs event={event} />
      </div>
    </div>
  );
}
