import EventCard, {
  type EventCardProps,
} from '@/app/[locale]/(events)/events/components/event-card';
import type { SerializedOrganizer } from '@/app/types';
import { Button } from '@repo/design-system/components/ui/button';
import { ArrowDownAZ, ArrowUpAZ } from 'lucide-react';
import { useState } from 'react';

type SortDirection = 'asc' | 'desc';

interface OrganizerPastEventsProps {
  organizer: SerializedOrganizer;
}

export function OrganizerPastEvents({ organizer }: OrganizerPastEventsProps) {
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Get only past events
  const currentDate = new Date();

  const pastEvents = organizer.events.filter((event) => {
    const endDate = new Date(event.endTime);
    return endDate < currentDate;
  });

  // Sort past events by date
  const sortedEvents = [...pastEvents].sort((a, b) => {
    const dateA = new Date(a.endTime);
    const dateB = new Date(b.endTime);
    return sortDirection === 'asc'
      ? dateA.getTime() - dateB.getTime()
      : dateB.getTime() - dateA.getTime();
  });

  const toggleSortDirection = () => {
    setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="font-semibold text-md">
          Past Events ({pastEvents.length})
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={toggleSortDirection}
          className="flex items-center gap-1"
          title={
            sortDirection === 'asc' ? 'Sort Newest First' : 'Sort Oldest First'
          }
        >
          {sortDirection === 'asc' ? (
            <>
              <ArrowUpAZ className="h-4 w-4" />
              <span className="hidden sm:inline">Oldest First</span>
            </>
          ) : (
            <>
              <ArrowDownAZ className="h-4 w-4" />
              <span className="hidden sm:inline">Newest First</span>
            </>
          )}
        </Button>
      </div>
      {pastEvents.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {sortedEvents.map((event) => (
            <EventCard
              key={event.slug}
              event={event satisfies EventCardProps}
            />
          ))}
        </div>
      ) : (
        <p className="text-muted-foreground">No past events</p>
      )}
    </div>
  );
}
