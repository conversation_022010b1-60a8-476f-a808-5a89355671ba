import EventCard, {
  type EventCardProps,
} from '@/app/[locale]/(events)/events/components/event-card';
import type { SerializedOrganizer } from '@/app/types';
import { Button } from '@repo/design-system/components/ui/button';
import { ArrowDownAZ, ArrowUpAZ } from 'lucide-react';
import { useState } from 'react';

type SortDirection = 'asc' | 'desc';

interface OrganizerLiveEventsProps {
  organizer: SerializedOrganizer;
}

export function OrganizerLiveEvents({ organizer }: OrganizerLiveEventsProps) {
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Separate events into ongoing and past based on end date
  const currentDate = new Date();

  const ongoingEvents = organizer.events.filter((event) => {
    const endDate = new Date(event.endTime);
    return endDate >= currentDate;
  });

  // Sort ongoing events by date
  const sortedEvents = [...ongoingEvents].sort((a, b) => {
    const dateA = new Date(a.startTime);
    const dateB = new Date(b.startTime);
    return sortDirection === 'asc'
      ? dateA.getTime() - dateB.getTime()
      : dateB.getTime() - dateA.getTime();
  });

  const toggleSortDirection = () => {
    setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="font-semibold text-md">
          Live Events ({ongoingEvents.length})
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={toggleSortDirection}
          className="flex items-center gap-1"
          title={
            sortDirection === 'asc' ? 'Sort Newest First' : 'Sort Oldest First'
          }
        >
          {sortDirection === 'asc' ? (
            <>
              <ArrowUpAZ className="h-4 w-4" />
              <span className="hidden sm:inline">Oldest First</span>
            </>
          ) : (
            <>
              <ArrowDownAZ className="h-4 w-4" />
              <span className="hidden sm:inline">Newest First</span>
            </>
          )}
        </Button>
      </div>
      {ongoingEvents.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {sortedEvents.map((event) => (
            <EventCard
              key={event.slug}
              event={event satisfies EventCardProps}
            />
          ))}
        </div>
      ) : (
        <p className="text-muted-foreground">No live events</p>
      )}
    </div>
  );
}
