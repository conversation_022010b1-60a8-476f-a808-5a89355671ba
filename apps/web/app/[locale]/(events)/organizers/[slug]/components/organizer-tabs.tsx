'use client';

import type { SerializedOrganizer } from '@/app/types';
import { Navigation } from '@repo/design-system/components/ui/lunar/navigation-tab';
import { cn, slugify } from '@repo/design-system/lib/utils';
import { useSearchParams } from 'next/navigation';
import type React from 'react';
import { OrganizerAbout } from './organizer-about';
import { OrganizerLiveEvents } from './organizer-live-events';
import { OrganizerPastEvents } from './organizer-past-events';

interface OrganizerTabsProps {
  organizer: SerializedOrganizer;
}

export function OrganizerTabs({ organizer }: OrganizerTabsProps) {
  const searchParams = useSearchParams();
  const currentTab = (searchParams.get('tab') as string) || 'live-events';
  const items = ['Live Events', 'Past Events', 'About Us'];

  function navigate() {
    // the callback is fired once the animation is completed
    // to allow smooth transition
  }

  function handleTabChange(tabLabel: string) {
    const value = slugify(tabLabel).toLowerCase();
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    window.history.replaceState(null, '', url);
  }

  return (
    <div className="w-full">
      <div className="sticky top-16 right-0 left-0 z-10 bg-background">
        <Navigation
          as="nav"
          className="relative mx-auto rounded-2xl bg-foreground/5 max-w-full"
        >
          {({ ready, size, position, duration }) => (
            <div className="relative py-1 overflow-x-auto no-scrollbar">
              {/* indicator bar */}
              <div
                style={
                  {
                    width: size,
                    transform: `translateX(${position})`,
                    transitionDuration: duration,
                  } as React.CSSProperties
                }
                className={cn(
                  { hidden: !ready },
                  'absolute bottom-0 left-0 h-1 rounded-full bg-primary-red transition-[width,transform]'
                )}
              />

              <Navigation.List
                as="ul"
                className="relative flex items-center gap-4 w-full justify-between sm:justify-center px-4"
              >
                {items.map((item, index) => (
                  <Navigation.Item
                    key={index}
                    as="li"
                    onActivated={navigate}
                    active={slugify(item).toLowerCase() === currentTab}
                    className="shrink-0 sm:flex-1 min-w-[90px]"
                    onClick={(e: React.MouseEvent) => {
                      e.currentTarget.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center',
                      });
                    }}
                  >
                    {({ setActive, isActive }) => (
                      <button
                        type="button"
                        onClick={() => {
                          setActive();
                          handleTabChange(item);
                        }}
                        className={cn(
                          isActive
                            ? 'text-foreground'
                            : 'text-foreground/60 hover:text-foreground',
                          'inline-block w-full py-2 px-0 md:p-4 text-md sm:text-lg transition'
                        )}
                      >
                        {item}
                      </button>
                    )}
                  </Navigation.Item>
                ))}
              </Navigation.List>
            </div>
          )}
        </Navigation>
      </div>

      <div className="mt-4">
        {currentTab === 'live-events' && (
          <OrganizerLiveEvents organizer={organizer} />
        )}
        {currentTab === 'past-events' && (
          <OrganizerPastEvents organizer={organizer} />
        )}
        {currentTab === 'about-us' && <OrganizerAbout organizer={organizer} />}
      </div>
    </div>
  );
}
