import type { SerializedOrganizer } from '@/app/types';
import { InstagramLogoIcon } from '@radix-ui/react-icons';
import { ReadOnlyEditor } from '@repo/design-system/components/blocks/editor-read-only/read-only-editor';
import { parseEditorContent } from '@repo/design-system/components/editor/utils/parse-editor-content';
import { Button } from '@repo/design-system/components/ui/button';
import { Separator } from '@repo/design-system/components/ui/separator';
import { Facebook, GlobeIcon, Mail, Phone, X, Youtube } from 'lucide-react';
import Link from 'next/link';

interface OrganizerAboutProps {
  organizer: SerializedOrganizer;
}

export function OrganizerAbout({ organizer }: OrganizerAboutProps) {
  return (
    <div className="space-y-[24px]">
      <h2 className="font-semibold">About {organizer.name}</h2>

      <div className="text-secondary-foreground">
        <ReadOnlyEditor
          editorSerializedState={parseEditorContent(
            typeof organizer.biography === 'string'
              ? organizer.biography
              : JSON.stringify(organizer.biography)
          )}
          innerClassName="p-0"
        />
      </div>

      <Separator />

      <div className="flex flex-wrap gap-2">
        {/* Website */}
        {organizer.website && (
          <Link href={organizer.website} target="_blank">
            <Button variant="secondary" size="icon" title="Website">
              <GlobeIcon className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* Email */}
        {organizer.email && (
          <Link href={`mailto:${organizer.email}`}>
            <Button variant="secondary" size="icon" title="Email">
              <Mail className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* Phone */}
        {organizer.phone && (
          <Link href={`tel:${organizer.phone}`}>
            <Button variant="secondary" size="icon" title="Phone">
              <Phone className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* WhatsApp */}
        {organizer.whatsapp && (
          <Link href={`https://wa.me/${organizer.whatsapp}`} target="_blank">
            <Button variant="secondary" size="icon" title="WhatsApp">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                x="0px"
                y="0px"
                width="16"
                height="16"
                viewBox="0,0,256,256"
                className="h-4 w-4 text-primary"
              >
                <title id="WhatsApp">WhatsApp</title>
                <g
                  fill="currentColor"
                  fillRule="nonzero"
                  stroke="none"
                  strokeWidth="1"
                  strokeLinecap="butt"
                  strokeLinejoin="miter"
                  strokeMiterlimit="10"
                  strokeDasharray=""
                  strokeDashoffset="0"
                  fontFamily="none"
                  fontWeight="none"
                  fontSize="none"
                  textAnchor="none"
                  style={{ mixBlendMode: 'normal' }}
                >
                  <g transform="scale(5.12,5.12)">
                    <path d="M25,2c-12.69047,0 -23,10.30953 -23,23c0,4.0791 1.11869,7.88588 2.98438,11.20898l-2.94727,10.52148c-0.09582,0.34262 -0.00241,0.71035 0.24531,0.96571c0.24772,0.25536 0.61244,0.35989 0.95781,0.27452l10.9707,-2.71875c3.22369,1.72098 6.88165,2.74805 10.78906,2.74805c12.69047,0 23,-10.30953 23,-23c0,-12.69047 -10.30953,-23 -23,-23zM25,4c11.60953,0 21,9.39047 21,21c0,11.60953 -9.39047,21 -21,21c-3.72198,0 -7.20788,-0.97037 -10.23828,-2.66602c-0.22164,-0.12385 -0.48208,-0.15876 -0.72852,-0.09766l-9.60742,2.38086l2.57617,-9.19141c0.07449,-0.26248 0.03851,-0.54399 -0.09961,-0.7793c-1.84166,-3.12289 -2.90234,-6.75638 -2.90234,-10.64648c0,-11.60953 9.39047,-21 21,-21zM16.64258,13c-0.64104,0 -1.55653,0.23849 -2.30859,1.04883c-0.45172,0.48672 -2.33398,2.32068 -2.33398,5.54492c0,3.36152 2.33139,6.2621 2.61328,6.63477h0.00195v0.00195c-0.02674,-0.03514 0.3578,0.52172 0.87109,1.18945c0.5133,0.66773 1.23108,1.54472 2.13281,2.49414c1.80347,1.89885 4.33914,4.09336 7.48633,5.43555c1.44932,0.61717 2.59271,0.98981 3.45898,1.26172c1.60539,0.5041 3.06762,0.42747 4.16602,0.26563c0.82216,-0.12108 1.72641,-0.51584 2.62109,-1.08203c0.89469,-0.56619 1.77153,-1.2702 2.1582,-2.33984c0.27701,-0.76683 0.41783,-1.47548 0.46875,-2.05859c0.02546,-0.29156 0.02869,-0.54888 0.00977,-0.78711c-0.01897,-0.23823 0.0013,-0.42071 -0.2207,-0.78516c-0.46557,-0.76441 -0.99283,-0.78437 -1.54297,-1.05664c-0.30567,-0.15128 -1.17595,-0.57625 -2.04883,-0.99219c-0.8719,-0.41547 -1.62686,-0.78344 -2.0918,-0.94922c-0.29375,-0.10568 -0.65243,-0.25782 -1.16992,-0.19922c-0.51749,0.0586 -1.0286,0.43198 -1.32617,0.87305c-0.28205,0.41807 -1.4175,1.75835 -1.76367,2.15234c-0.0046,-0.0028 0.02544,0.01104 -0.11133,-0.05664c-0.42813,-0.21189 -0.95173,-0.39205 -1.72656,-0.80078c-0.77483,-0.40873 -1.74407,-1.01229 -2.80469,-1.94727v-0.00195c-1.57861,-1.38975 -2.68437,-3.1346 -3.0332,-3.7207c0.0235,-0.02796 -0.00279,0.0059 0.04687,-0.04297l0.00195,-0.00195c0.35652,-0.35115 0.67247,-0.77056 0.93945,-1.07812c0.37854,-0.43609 0.54559,-0.82052 0.72656,-1.17969c0.36067,-0.71583 0.15985,-1.50352 -0.04883,-1.91797v-0.00195c0.01441,0.02867 -0.11288,-0.25219 -0.25,-0.57617c-0.13751,-0.32491 -0.31279,-0.74613 -0.5,-1.19531c-0.37442,-0.89836 -0.79243,-1.90595 -1.04102,-2.49609v-0.00195c-0.29285,-0.69513 -0.68904,-1.1959 -1.20703,-1.4375c-0.51799,-0.2416 -0.97563,-0.17291 -0.99414,-0.17383h-0.00195c-0.36964,-0.01705 -0.77527,-0.02148 -1.17773,-0.02148zM16.64258,15c0.38554,0 0.76564,0.0047 1.08398,0.01953c0.32749,0.01632 0.30712,0.01766 0.24414,-0.01172c-0.06399,-0.02984 0.02283,-0.03953 0.20898,0.40234c0.24341,0.57785 0.66348,1.58909 1.03906,2.49023c0.18779,0.45057 0.36354,0.87343 0.50391,1.20508c0.14036,0.33165 0.21642,0.51683 0.30469,0.69336v0.00195l0.00195,0.00195c0.08654,0.17075 0.07889,0.06143 0.04883,0.12109c-0.21103,0.41883 -0.23966,0.52166 -0.45312,0.76758c-0.32502,0.37443 -0.65655,0.792 -0.83203,0.96484c-0.15353,0.15082 -0.43055,0.38578 -0.60352,0.8457c-0.17323,0.46063 -0.09238,1.09262 0.18555,1.56445c0.37003,0.62819 1.58941,2.6129 3.48438,4.28125c1.19338,1.05202 2.30519,1.74828 3.19336,2.2168c0.88817,0.46852 1.61157,0.74215 1.77344,0.82227c0.38438,0.19023 0.80448,0.33795 1.29297,0.2793c0.48849,-0.05865 0.90964,-0.35504 1.17773,-0.6582l0.00195,-0.00195c0.3568,-0.40451 1.41702,-1.61513 1.92578,-2.36133c0.02156,0.0076 0.0145,0.0017 0.18359,0.0625v0.00195h0.00195c0.0772,0.02749 1.04413,0.46028 1.90625,0.87109c0.86212,0.41081 1.73716,0.8378 2.02148,0.97852c0.41033,0.20308 0.60422,0.33529 0.6543,0.33594c0.00338,0.08798 0.0068,0.18333 -0.00586,0.32813c-0.03507,0.40164 -0.14243,0.95757 -0.35742,1.55273c-0.10532,0.29136 -0.65389,0.89227 -1.3457,1.33008c-0.69181,0.43781 -1.53386,0.74705 -1.8457,0.79297c-0.9376,0.13815 -2.05083,0.18859 -3.27344,-0.19531c-0.84773,-0.26609 -1.90476,-0.61053 -3.27344,-1.19336c-2.77581,-1.18381 -5.13503,-3.19825 -6.82031,-4.97266c-0.84264,-0.8872 -1.51775,-1.71309 -1.99805,-2.33789c-0.4794,-0.62364 -0.68874,-0.94816 -0.86328,-1.17773l-0.00195,-0.00195c-0.30983,-0.40973 -2.20703,-3.04868 -2.20703,-5.42578c0,-2.51576 1.1685,-3.50231 1.80078,-4.18359c0.33194,-0.35766 0.69484,-0.41016 0.8418,-0.41016z" />
                  </g>
                </g>
              </svg>
            </Button>
          </Link>
        )}

        {/* Facebook */}
        {organizer.facebook && (
          <Link href={organizer.facebook} target="_blank">
            <Button variant="secondary" size="icon" title="Facebook">
              <Facebook className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* Twitter/X */}
        {organizer.twitter && (
          <Link href={organizer.twitter} target="_blank">
            <Button variant="secondary" size="icon" title="Twitter/X">
              <X className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* Instagram */}
        {organizer.instagram && (
          <Link href={organizer.instagram} target="_blank">
            <Button variant="secondary" size="icon" title="Instagram">
              <InstagramLogoIcon className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* YouTube */}
        {organizer.youtube && (
          <Link href={organizer.youtube} target="_blank">
            <Button variant="secondary" size="icon" title="YouTube">
              <Youtube className="h-4 w-4" />
            </Button>
          </Link>
        )}

        {/* TikTok */}
        {organizer.tiktok && (
          <Link href={organizer.tiktok} target="_blank">
            <Button variant="secondary" size="icon" title="TikTok">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                x="0px"
                y="0px"
                width="100"
                height="100"
                viewBox="0,0,256,256"
                className="h-4 w-4 text-primary"
              >
                <title id="TikTok">TikTok</title>
                <g
                  fill="currentColor"
                  fill-rule="nonzero"
                  stroke="none"
                  stroke-width="1"
                  stroke-linecap="butt"
                  stroke-linejoin="miter"
                  stroke-miterlimit="10"
                  stroke-dasharray=""
                  stroke-dashoffset="0"
                  font-family="none"
                  font-weight="none"
                  font-size="none"
                  text-anchor="none"
                  style={{ mixBlendMode: 'normal' }}
                >
                  <g transform="scale(5.12,5.12)">
                    <path d="M9,4c-2.75042,0 -5,2.24958 -5,5v32c0,2.75042 2.24958,5 5,5h32c2.75042,0 5,-2.24958 5,-5v-32c0,-2.75042 -2.24958,-5 -5,-5zM9,6h32c1.67158,0 3,1.32842 3,3v32c0,1.67158 -1.32842,3 -3,3h-32c-1.67158,0 -3,-1.32842 -3,-3v-32c0,-1.67158 1.32842,-3 3,-3zM26.04297,10c-0.5515,0.00005 -0.99887,0.44655 -1,0.99805c0,0 -0.01098,4.87522 -0.02148,9.76172c-0.0053,2.44325 -0.01168,4.88902 -0.01562,6.73047c-0.00394,1.84145 -0.00586,3.0066 -0.00586,3.10352c0,1.81526 -1.64858,3.29883 -3.52734,3.29883c-1.86379,0 -3.35156,-1.48972 -3.35156,-3.35352c0,-1.86379 1.48777,-3.35156 3.35156,-3.35156c0.06314,0 0.1904,0.02075 0.4082,0.04688c0.28415,0.03406 0.56927,-0.05523 0.78323,-0.24529c0.21396,-0.19006 0.33624,-0.46267 0.33591,-0.74885v-4.20117c-0.00005,-0.528 -0.41054,-0.965 -0.9375,-0.99805c-0.15583,-0.0098 -0.35192,-0.0293 -0.58984,-0.0293c-5.24953,0 -9.52734,4.27782 -9.52734,9.52734c0,5.24953 4.27782,9.52734 9.52734,9.52734c5.24938,0 9.52734,-4.27782 9.52734,-9.52734v-9.04883c1.45461,1.16341 3.26752,1.90039 5.26953,1.90039c0.27306,0 0.53277,-0.01618 0.78125,-0.03906c0.51463,-0.04749 0.90832,-0.47927 0.9082,-0.99609v-4.66992c0.0003,-0.52448 -0.40463,-0.9601 -0.92773,-0.99805c-3.14464,-0.22561 -5.65141,-2.67528 -5.97852,-5.79102c-0.05305,-0.50925 -0.48214,-0.89619 -0.99414,-0.89648zM27.04102,12h2.28125c0.72678,3.2987 3.30447,5.8144 6.63672,6.44531v2.86523c-2.13887,-0.10861 -4.01749,-1.1756 -5.12305,-2.85742c-0.24284,-0.36962 -0.69961,-0.53585 -1.12322,-0.40877c-0.4236,0.12708 -0.71344,0.51729 -0.71272,0.95955v11.53516c0,4.16848 -3.35873,7.52734 -7.52734,7.52734c-4.16848,0 -7.52734,-3.35887 -7.52734,-7.52734c0,-4.00052 3.12077,-7.17588 7.05469,-7.43164v2.17578c-2.71358,0.25252 -4.87891,2.47904 -4.87891,5.25586c0,2.94421 2.40735,5.35352 5.35156,5.35352c2.92924,0 5.52734,-2.30609 5.52734,-5.29883c0,0.04892 0.00186,-1.25818 0.00586,-3.09961c0.0039,-1.84143 0.01037,-4.28722 0.01563,-6.73047c0.0094,-4.3869 0.0177,-7.91447 0.01953,-8.76367z" />
                  </g>
                </g>
              </svg>
            </Button>
          </Link>
        )}

        {/* RedNote */}
        {organizer.rednote && (
          <Link href={organizer.rednote} target="_blank">
            <Button variant="secondary" size="icon" title="RedNote">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                x="0px"
                y="0px"
                width="100"
                height="100"
                viewBox="0,0,256,256"
                className="h-4 w-4 text-primary"
              >
                <title id="RedNote">RedNote</title>
                <g
                  fill="#fa5252"
                  fill-rule="nonzero"
                  stroke="none"
                  stroke-width="1"
                  stroke-linecap="butt"
                  stroke-linejoin="miter"
                  stroke-miterlimit="10"
                  stroke-dasharray=""
                  stroke-dashoffset="0"
                  font-family="none"
                  font-weight="none"
                  font-size="none"
                  text-anchor="none"
                  style={{ mixBlendMode: 'normal' }}
                >
                  <g transform="scale(5.12,5.12)">
                    <path d="M35,22v2h1v-2zM35,22v2h1v-2zM44,4h-38c-1.09,0 -2,0.91 -2,2v38c0,1.09 0.91,2 2,2h38c1.09,0 2,-0.91 2,-2v-38c0,-1.09 -0.91,-2 -2,-2zM12,24c0,1.38 -0.19,5.89 -2.61,6.24l-0.28,-1.98c0.39,-0.19 0.89,-2.14 0.89,-4.26v-2h2zM15,30h-2v-11h2zM17.29,29.71c-1.2,-1.2 -1.29,-4.73 -1.29,-5.78v-1.93h2v1.93c0,1.91 0.34,3.99 0.71,4.36zM22,31h-3l1,-2h3zM31,31h-7l1,-2h2v-7h-2l-2.1,4.38h1.72l-1,2h-2.62c-0.8,0 -1.28,-0.91 -0.82,-1.57l1.82,-2.81h-2c-0.78,0 -1.26,-0.85 -0.86,-1.51l3,-5l1.72,1.02l-2.09,3.49h3.23v-2h6v2h-2v7h2zM40,28.5c0,1.38 -1.12,2.5 -2.5,2.5c-1.21,0 -1.22,-0.86 -1.45,-2h1.95v-3h-3v5h-2v-5h-2v-2h2v-2h-1v-2h1v-1h2v1h1c1.1,0 2,0.9 2,2v2c1.1,0 2,0.9 2,2zM40,22h-1v-1c0,-0.55 0.45,-1 1,-1c0.55,0 1,0.45 1,1c0,0.55 -0.45,1 -1,1zM35,24h1v-2h-1zM35,22v2h1v-2z" />
                  </g>
                </g>
              </svg>
            </Button>
          </Link>
        )}
      </div>
    </div>
  );
}
