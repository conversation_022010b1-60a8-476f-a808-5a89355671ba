'use server';

import { database, serializePrisma } from '@repo/database';
import { notFound } from 'next/navigation';

export async function getOrganizer(slug: string) {
  const organizer = await database.organizer.findUnique({
    where: {
      slug,
    },
    select: {
      id: true,
      slug: true,
      logo: true,
      name: true,
      description: true,
      biography: true,
      website: true,
      email: true,
      phone: true,
      whatsapp: true,
      facebook: true,
      twitter: true,
      instagram: true,
      youtube: true,
      tiktok: true,
      rednote: true,
      events: {
        select: {
          title: true,
          slug: true,
          startTime: true,
          endTime: true,
          doorsOpen: true,
          status: true,
          visibility: true,
          category: true,
          venueName: true,
          venueAddress: true,
          venue: {
            select: {
              name: true,
              address: true,
            },
          },
          heroImageUrl: true,
          carouselImageUrls: true,
        },
        // filter out events that are private/not published
        where: {
          status: {
            in: ['published', 'sold_out'],
          },
          visibility: 'public',
        },
      },
      _count: {
        select: {
          events: true,
        },
      },
    },
  });

  if (!organizer) {
    return notFound();
  }

  return serialize<PERSON><PERSON><PERSON>(organizer);
}
