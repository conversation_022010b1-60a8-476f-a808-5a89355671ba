import { getCart } from '@/app/[locale]/(cart)/actions';
import type { SerializedCart } from '@/app/types';
import { EventHeader } from '@/components/header/event-header';
import { cookies } from 'next/headers';
import type { ReactNode } from 'react';
import { CartProvider } from './_lib/cart-context';
import { EventFooter } from '@/components/footer/event-footer';

type EventLayoutProperties = {
  readonly children: ReactNode;
};

const EventLayout = async ({ children }: EventLayoutProperties) => {
  const cookieStore = await cookies();
  const cartId = cookieStore.get('cartId')?.value;
  let initialCartData: SerializedCart | null = null;

  try {
    if (cartId) {
      initialCartData = await getCart(cartId);
    }
  } catch (error) {
    if (cartId) {
      console.error(
        `SSR Error fetching cart data for cartId ${cartId}:`,
        error
      );
    } else {
      console.error('SSR Error creating empty cart', error);
    }
  }

  // log.debug('cart', { initialCartData });

  // 3. Provide the fetched data (or null) to the client context
  return (
    <CartProvider initialCartData={initialCartData}>
      <EventHeader />
      <main className="mx-auto flex max-w-4xl flex-col py-24">{children}</main>
      <EventFooter />
    </CartProvider>
  );
};

export default EventLayout;
