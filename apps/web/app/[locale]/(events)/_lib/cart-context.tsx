'use client';

import type { SerializedCart } from '@/app/types';

import {
  clearCart as clearCartAction,
  createCart,
  createEmptyCart,
  getCart,
  removeCartItem,
  updateCartItemQuantity,
} from '@/app/[locale]/(cart)/actions';
import {
  deleteCartIdCookie,
  getCartIdCookie,
  setCartIdCookie,
} from '@/app/[locale]/(cart)/cookie-actions';
import { toast } from '@repo/design-system/components/ui/use-toast';
import { log } from '@repo/observability/log';
import {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import {
  clearCartFromCache,
  getCartFromCache,
  setCartInCache,
} from './cart-cache';

// Define the shape of our cart context
interface CartContextType {
  cart: SerializedCart | null;
  isMutating: boolean;
  isLoading: boolean; // For initial cart load
  error: string | null;
  totalItems: number;
  totalAmount: number;
  addToCart: (input: {
    eventId: string;
    timeSlotId: string;
    ticketTypeId: string;
    quantity: number;
  }) => Promise<void>;
  removeFromCart: (cartItemId: string) => Promise<void>;
  updateQuantity: (cartItemId: string, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  createNewCart: () => Promise<string | null>;
  refreshCart: (cartId?: string) => Promise<void>;
}

// Create the context with a default value
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component that wraps parts of our app that need cart state
export function CartProvider({
  children,
  initialCartData,
}: {
  children: ReactNode;
  initialCartData?: SerializedCart | null;
}) {
  const [cart, setCart] = useState<SerializedCart | null>(
    initialCartData || null
  );
  const [isLoading, setIsLoading] = useState(!initialCartData); // isLoading is true if we need to fetch initial cart
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch initial cart data if not provided
  useEffect(() => {
    const fetchInitialCart = async () => {
      if (initialCartData) {
        log.debug('Using provided initial cart data', { initialCartData });
        setIsLoading(false); // No need to load if we have initial data
      } else {
        setIsLoading(true); // Start loading
        log.debug('No initial cart data, attempting to fetch from cookie');

        // get cartId from cookie
        const cartId = await getCartIdCookie();

        if (cartId) {
          // if cartId is found, fetch cart data using cookie value
          log.debug('Found cartId cookie', { cartId });

          try {
            const cartData = await getCart(cartId);
            if (cartData) {
              log.debug('Successfully fetched cart from cookie ID', {
                cartId,
                cartData,
              });
              setCart(cartData);
            } else {
              log.warn(
                'Cart ID from cookie was invalid or cart expired, clearing cookie',
                { cartId }
              );
              await deleteCartIdCookie(); // Clean up invalid cookie
            }
          } catch (fetchError) {
            log.error('Error fetching initial cart from cookie', {
              cartId,
              error: fetchError,
            });
            setError('Failed to load cart.'); // Set error state
            await deleteCartIdCookie(); // Clean up potentially bad cookie
          }
        } else {
          // if cartId is found, fetch cart data using cookie value
          log.debug('cartId cookie missing, attempting to create empty cart');

          try {
            const emptyCart = await createEmptyCart();
            if (emptyCart.cartId) {
              log.debug('Successfully created empty cart', {
                cartId: emptyCart.cartId,
                emptyCart,
              });
              setCartIdCookie(emptyCart.cartId);
              setCart(emptyCart.cart);
            } else {
              log.warn('Failed to create empty cart');
              await deleteCartIdCookie(); // Clean up invalid cookie
            }
          } catch (fetchError) {
            log.error('Error creating empty cart', {
              error: fetchError,
            });
            setError('Failed to load cart.'); // Set error state
            await deleteCartIdCookie(); // Clean up potentially bad cookie
          }
        }

        setIsLoading(false); // Finished loading
      }
    };

    fetchInitialCart();
  }, [initialCartData]);

  // Refresh cart data from the server with debounce/retry logic
  const refreshCart = useCallback(
    async (cartId?: string) => {
      const idToUse = cartId || cart?.id;
      if (!idToUse) {
        log.debug('No cart ID to refresh');
        return;
      }

      try {
        const refreshStartTime = Date.now();
        log.debug(`Refreshing cart state [${new Date().toISOString()}]`, {
          cartId: idToUse,
        });

        // Removed intentional delay to improve performance

        // Try to get cart from cache first
        const cacheStart = Date.now();
        let updatedCart = getCartFromCache(idToUse);

        // If cart is in cache, use it
        if (updatedCart) {
          const cacheEnd = Date.now();
          log.debug(`Used cached cart data - took ${cacheEnd - cacheStart}ms`);
        } else {
          // If not in cache, fetch from server
          log.debug(`Cart cache miss for ${idToUse}, fetching from server`);
          const getCartStart = Date.now();
          updatedCart = await getCart(idToUse);
          const getCartEnd = Date.now();
          log.debug(
            `getCart completed [${new Date().toISOString()}] - took ${getCartEnd - getCartStart}ms`
          );

          // Store in cache for future use
          if (updatedCart) {
            setCartInCache(idToUse, updatedCart);
          }
        }

        setCart(updatedCart); // Update state with fresh data

        if (!updatedCart) {
          log.warn(
            `Cart became null after refresh [${new Date().toISOString()}], clearing cookie`,
            {
              cartId,
            }
          );
          await deleteCartIdCookie(); // If cart is gone, remove cookie
        }

        const refreshEndTime = Date.now();
        log.debug(
          `Cart refresh function completed [${new Date().toISOString()}] - total time: ${refreshEndTime - refreshStartTime}ms`
        );
      } catch (fetchError) {
        log.error('Error refreshing cart state', { cartId, error: fetchError });
        setError('Failed to refresh cart.');
      }
    },
    [cart?.id]
  );

  // Add item to cart with optimistic updates
  const addToCart = async (input: {
    eventId: string;
    timeSlotId: string;
    ticketTypeId: string;
    quantity: number;
  }) => {
    // Prevent multiple simultaneous calls
    if (isMutating) {
      return;
    }

    setIsMutating(true);
    setError(null); // Reset error before action
    const startTime = Date.now();
    log.debug(`addToCart context action called [${new Date().toISOString()}]`, {
      input,
    });

    // Store original cart state for rollback if needed
    const originalCart = cart;

    try {
      // Create a more accurate optimistic update
      // if (cart) {
      //   // Find if this item already exists in the cart
      //   const existingItemIndex = cart.cartItems.findIndex(
      //     (item) =>
      //       item.timeSlotId === input.timeSlotId &&
      //       item.ticketTypeId === input.ticketTypeId
      //   );

      //   // Create an optimistic cart update - use type assertion to avoid TS errors
      //   const optimisticCart = { ...cart } as SerializedCart;

      //   if (existingItemIndex >= 0) {
      //     // Update existing item quantity
      //     const updatedItems = [...optimisticCart.cartItems];
      //     updatedItems[existingItemIndex] = {
      //       ...updatedItems[existingItemIndex],
      //       quantity: updatedItems[existingItemIndex].quantity + input.quantity,
      //     };

      //     optimisticCart.cartItems = updatedItems;
      //   } else {
      //     // Try to create a more accurate new item preview
      //     // This is a best-effort attempt without all server data
      //     log.debug('Creating optimistic new cart item');

      //     // We need to find information about the ticket type and time slot
      //     // This would typically come from the form or selection context
      //     // For now, we'll create a minimal placeholder
      //     const newOptimisticItem = {
      //       id: `temp-${Date.now()}`, // Temporary ID
      //       cartId: cart.id,
      //       timeSlotId: input.timeSlotId,
      //       ticketTypeId: input.ticketTypeId,
      //       quantity: input.quantity,
      //       createdAt: new Date().toISOString(),
      //       updatedAt: new Date().toISOString(),
      //       // These fields will be incomplete but provide some structure
      //       ticketType: {
      //         id: input.ticketTypeId,
      //         name: 'Loading...', // Will be updated with real data
      //         price: 0, // Will be updated with real data
      //         description: '',
      //       },
      //       timeSlot: {
      //         id: input.timeSlotId,
      //         startTime: new Date().toISOString(),
      //         endTime: new Date().toISOString(),
      //         doorsOpen: new Date().toISOString(),
      //       },
      //     };

      //     // Add the new item to the cart
      //     optimisticCart.cartItems = [
      //       ...optimisticCart.cartItems,
      //       newOptimisticItem,
      //     ];
      //   }

      //   // Update the UI immediately
      //   setCart(optimisticCart);
      //   log.debug('Applied optimistic UI update');
      // }

      // Perform the actual server action
      const currentCartId = cart?.id || (await getCartIdCookie());
      const serverActionStart = Date.now();
      // Cart is updated via createCart()
      const result = await createCart(input, currentCartId || undefined);
      const serverActionEnd = Date.now();
      log.info(
        `Item added/updated via createCart action [${new Date().toISOString()}] - took ${serverActionEnd - serverActionStart}ms`,
        {
          cartId: result.cartId,
          input,
        }
      );

      // Clear cache for this cart since it's been modified
      clearCartFromCache(result.cartId);

      // Update cartId cookie with createCart response
      if (!currentCartId || result.resetCartCookie) {
        await setCartIdCookie(result.cartId);
        log.debug('Set new cartId cookie', { cartId: result.cartId });
      }

      // Use the cart data returned directly from createCart
      // This eliminates the need for a separate refreshCart call
      if (result.cart) {
        const updateStart = Date.now();
        setCart(result.cart);
        // Store in cache for future use
        setCartInCache(result.cartId, result.cart);
        const updateEnd = Date.now();
        log.debug(
          `Cart state updated with returned data [${new Date().toISOString()}] - took ${updateEnd - updateStart}ms, total operation took ${updateEnd - startTime}ms`
        );
      } else {
        // Fallback to refreshCart only if cart data wasn't returned
        log.debug(
          'No cart data returned from createCart, falling back to refreshCart'
        );
        const refreshStart = Date.now();
        await refreshCart(result.cartId);
        const refreshEnd = Date.now();
        log.debug(
          `Cart refresh completed [${new Date().toISOString()}] - took ${refreshEnd - refreshStart}ms, total operation took ${refreshEnd - startTime}ms`
        );
      }

      toast({
        title: 'Item added',
        description: 'Item successfully added to your cart.',
      });
    } catch (actionError) {
      log.error('Error in addToCart context action', {
        input,
        error: actionError,
      });
      // Revert to original cart state on error
      setCart(originalCart);
      setError('Failed to add item to cart.');
      toast({
        title: 'Error',
        description: 'Could not add item to cart.',
        variant: 'destructive',
      });
    } finally {
      setIsMutating(false);
    }
  };

  // Remove item from cart
  const removeFromCart = async (cartItemId: string) => {
    if (!cart?.id) {
      log.warn('Attempted to remove item from non-existent cart');
      return;
    }

    setIsMutating(true);
    setError(null);
    const startTime = Date.now();
    log.debug(
      `removeItem context action called [${new Date().toISOString()}]`,
      { cartItemId }
    );

    setIsLoading(true);

    try {
      const actionStart = Date.now();
      await removeCartItem(cartItemId);
      const actionEnd = Date.now();
      log.info(
        `Item removed via removeCartItem action [${new Date().toISOString()}] - took ${actionEnd - actionStart}ms`,
        {
          cartId: cart.id,
          cartItemId,
        }
      );

      // Clear cache since cart has been modified
      clearCartFromCache(cart.id);

      // Refresh cart state after removal
      const refreshStart = Date.now();
      await refreshCart();
      const refreshEnd = Date.now();
      log.debug(
        `Cart refresh after removal completed [${new Date().toISOString()}] - took ${refreshEnd - refreshStart}ms, total operation took ${refreshEnd - startTime}ms`
      );

      toast({
        title: 'Item removed',
        description: 'Item successfully removed from your cart.',
      });
    } catch (actionError) {
      log.error('Error in removeItem context action', {
        cartItemId,
        error: actionError,
      });
      setError('Failed to remove item from cart.');
      toast({
        title: 'Error',
        description: 'Could not remove item from cart.',
        variant: 'destructive',
      });
    } finally {
      setIsMutating(false);
      setIsLoading(false);
    }
  };

  // Update item quantity
  const updateQuantity = async (cartItemId: string, quantity: number) => {
    if (!cart?.id) {
      log.warn('Attempted to update quantity in non-existent cart');
      return;
    }

    setIsMutating(true);
    setError(null);
    const startTime = Date.now();
    log.debug(
      `updateQuantity context action called [${new Date().toISOString()}]`,
      { cartItemId, quantity }
    );

    try {
      const actionStart = Date.now();
      await updateCartItemQuantity(cartItemId, quantity);
      const actionEnd = Date.now();
      log.info(
        `Quantity updated via updateCartItemQuantity action [${new Date().toISOString()}] - took ${actionEnd - actionStart}ms`,
        {
          cartId: cart.id,
          cartItemId,
          quantity,
        }
      );

      // Clear cache since cart has been modified
      clearCartFromCache(cart.id);

      // Refresh cart state after update
      const refreshStart = Date.now();
      await refreshCart();
      const refreshEnd = Date.now();
      log.debug(
        `Cart refresh after quantity update completed [${new Date().toISOString()}] - took ${refreshEnd - refreshStart}ms, total operation took ${refreshEnd - startTime}ms`
      );

      toast({
        title: 'Quantity updated',
        description: 'Item quantity successfully updated.',
      });
    } catch (actionError) {
      log.error('Error in updateQuantity context action', {
        cartItemId,
        quantity,
        error: actionError,
      });
      setError('Failed to update quantity.');
      toast({
        title: 'Error',
        description: 'Could not update quantity.',
        variant: 'destructive',
      });
    } finally {
      setIsMutating(false);
    }
  };

  // Clear the entire cart
  const clearCart = async () => {
    if (!cart?.id) {
      log.warn('Attempted to clear non-existent cart');
      return;
    }

    setIsMutating(true);
    setError(null);
    const startTime = Date.now();
    log.debug(`clearCart context action called [${new Date().toISOString()}]`, {
      cartId: cart.id,
    });

    try {
      const actionStart = Date.now();
      await clearCartAction(cart.id);
      const actionEnd = Date.now();
      log.info(
        `Cart cleared via clearCartAction [${new Date().toISOString()}] - took ${actionEnd - actionStart}ms`,
        { cartId: cart.id }
      );

      // Clear cache since cart has been modified
      clearCartFromCache(cart.id);

      // Cart is now effectively empty/abandoned
      setCart(null); // Set local state to null
      await deleteCartIdCookie(); // Remove the cookie

      const endTime = Date.now();
      log.debug(
        `Cart clearing completed [${new Date().toISOString()}] - total operation took ${endTime - startTime}ms`
      );

      toast({
        title: 'Cart cleared',
        description: 'Your cart has been emptied.',
      });
    } catch (actionError) {
      log.error('Error in clearCart context action', {
        cartId: cart.id,
        error: actionError,
      });
      setError('Failed to clear cart.');
      toast({
        title: 'Error',
        description: 'Could not clear your cart.',
        variant: 'destructive',
      });
    } finally {
      setIsMutating(false);
    }
  };

  // Calculate derived values
  const totalItems =
    cart?.cartItems.reduce((total: number, item) => total + item.quantity, 0) ||
    0;

  const totalAmount =
    cart?.cartItems.reduce(
      (total: number, item) =>
        total + Number(item.ticketType.price) * item.quantity,
      0
    ) || 0;

  // Create a new empty cart
  const createNewCart = async () => {
    setIsMutating(true);
    setError(null);
    log.debug('createNewCart context action called');

    try {
      // Delete any existing cart cookie
      await deleteCartIdCookie();

      // Call the server action to create a new empty cart
      const result = await createEmptyCart();

      log.info('New empty cart created via server action', {
        cartId: result.cartId,
      });

      // Store new cartId in cookie
      await setCartIdCookie(result.cartId);

      // Set the cart state to the new empty cart
      setCart(result.cart as SerializedCart);

      toast({
        title: 'New cart created',
        description: 'You can now add items to your cart.',
      });

      return result.cartId;
    } catch (error) {
      log.error('Error creating new cart', { error });
      setError('Failed to create a new cart.');
      toast({
        title: 'Error',
        description: 'Could not create a new cart.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsMutating(false);
    }
  };

  // Provide the cart context to children
  return (
    <CartContext.Provider
      value={{
        cart,
        isMutating,
        isLoading,
        error,
        totalItems,
        totalAmount,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        createNewCart,
        refreshCart,
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

// Hook to use the cart context
export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
