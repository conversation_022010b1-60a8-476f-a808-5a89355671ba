'use client';

import type { SerializedCart } from '@/app/types';
import { log } from '@repo/observability/log';

// Simple in-memory cache for cart data
// This helps reduce database load and improves performance
interface CartCache {
  [cartId: string]: {
    data: SerializedCart;
    timestamp: number;
  };
}

// Cache expiration time in milliseconds (5 seconds)
const CACHE_EXPIRATION = 5000;

// In-memory cache object
const cartCache: CartCache = {};

/**
 * Get cart data from cache
 * @param cartId Cart ID to retrieve
 * @returns Cached cart data or null if not found or expired
 */
export function getCartFromCache(cartId: string): SerializedCart | null {
  const cached = cartCache[cartId];

  if (!cached) {
    return null;
  }

  // Check if cache is expired
  const now = Date.now();
  if (now - cached.timestamp > CACHE_EXPIRATION) {
    log.debug(`Cart cache expired for ${cartId}`);
    delete cartCache[cartId];
    return null;
  }

  log.debug(`Cart cache hit for ${cartId}`);
  return cached.data;
}

/**
 * Store cart data in cache
 * @param cartId Cart ID to store
 * @param data Cart data to cache
 */
export function setCartInCache(cartId: string, data: SerializedCart): void {
  cartCache[cartId] = {
    data,
    timestamp: Date.now(),
  };
  log.debug(`Cart cached for ${cartId}`);
}

/**
 * Clear cart from cache
 * @param cartId Cart ID to clear
 */
export function clearCartFromCache(cartId: string): void {
  delete cartCache[cartId];
  log.debug(`Cart cache cleared for ${cartId}`);
}
