'use server';

import { customerAuth } from '@repo/auth/customer-auth';
import { log } from '@repo/observability/log';
import { headers } from 'next/headers';

export async function getUser() {
  try {
    const session = await customerAuth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return {
        statusCode: 401,
        message: 'Unauthenticated',
      };
    }

    if (!session.user) {
      return null;
    }

    return {
      statusCode: 200,
      data: session.user,
    };
  } catch (error) {
    log.error('Error in getUser', { error });
    return {
      statusCode: 500,
      message: 'Internal server error',
    };
  }
}

export const logout = async () => {
  try {
    await customerAuth.api.signOut({
      headers: await headers(),
    });
    return { status: 'success' };
  } catch (error) {
    log.error('Error in logout', { error });
    return {
      status: 'error',
      message: 'Failed to log out',
    };
  }
};
