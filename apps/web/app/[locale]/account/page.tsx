import { getUser } from '@/app/[locale]/account/action';
import { notFound, redirect } from 'next/navigation';
import { UserInfo } from './components/user-info';

export default async function AccountPage() {
  const userResponse = await getUser();
  const { data: user, statusCode } = userResponse ?? {};

  if (!user) {
    return notFound();
  }

  if (statusCode === 401) {
    return redirect('/auth/login');
  }

  if (statusCode === 500) {
    return (
      <div className="mx-auto w-full space-y-8 px-4 my-10">
        <h1 className="text-2xl font-semibold mb-6">Account</h1>
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">
            Sorry, we encountered an error loading your account information.
            Please try again later.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto w-full space-y-8 px-4 my-10">
      <h1 className="text-2xl font-semibold mb-6">Account</h1>
      <UserInfo user={user} />
    </div>
  );
}
