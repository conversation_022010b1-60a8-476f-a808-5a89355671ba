'use client';

import { EventFooter } from '@/components/footer/event-footer';
import { EventHeader } from '@/components/header/event-header';
import type { ReactNode } from 'react';

type RootLayoutProperties = {
  readonly children: ReactNode;
  readonly params: Promise<{
    locale: string;
  }>;
};

const AccountLayout = ({ children }: RootLayoutProperties) => {
  return (
    <>
      <EventHeader />
      <main className="mx-auto flex max-w-4xl flex-col py-24">{children}</main>
      <EventFooter />
    </>
  );
};

export default AccountLayout;
