'use client';

import type { User } from '@repo/auth/server';
import { useSignOutWithReset } from '@repo/auth/utils/sign-out-handler';
import { Button } from '@repo/design-system/components/ui/button';
import Image from 'next/image';

interface UserInfoProps {
  user: User;
}

export function UserInfo({ user }: UserInfoProps) {
  const signOut = useSignOutWithReset('/auth/login');

  return (
    <div className="bg-background border-border border shadow rounded-lg p-6">
      <h2 className="text-xl font-medium mb-4">Personal Information</h2>
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">Name</p>
            <p className="font-medium">{user.name || 'Not provided'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Email</p>
            <p className="font-medium">{user.email}</p>
          </div>
        </div>
        {user.image && (
          <div className="mt-4">
            <p className="text-sm text-gray-500 mb-2">Profile Image</p>
            <Image
              src={user.image}
              alt="Profile"
              width={64}
              height={64}
              className="rounded-full object-cover"
            />
          </div>
        )}
        <Button variant="destructive" onClick={signOut} className="self-end">
          Sign Out
        </Button>
      </div>
    </div>
  );
}
