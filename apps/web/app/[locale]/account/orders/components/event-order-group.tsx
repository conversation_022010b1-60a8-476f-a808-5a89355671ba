'use client';

import type { Order } from '@/app/[locale]/account/orders/action';
import { OrderCard } from '@/app/[locale]/account/orders/components/order-card';
import { Button } from '@repo/design-system/components/ui/button';
import { formatDate } from '@repo/design-system/lib/format';
import {
  CalendarIcon,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  MapPinIcon,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

interface EventOrderGroupProps {
  eventName: string;
  orders: Order[];
  event: Order['event'];
}

export function EventOrderGroup({
  eventName,
  orders,
  event,
}: EventOrderGroupProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const totalOrders = orders.length;
  const totalTickets = orders.reduce(
    (acc, order) => acc + order.tickets.length,
    0
  );

  return (
    <div className="rounded-lg border bg-card shadow-sm">
      <div className="p-6">
        <div className="flex flex-col md:flex-row gap-6 w-full text-left">
          {/* Event Image */}
          {event.imageUrl && (
            <div className="relative h-40 w-40 overflow-hidden rounded-md flex-shrink-0">
              <Image
                src={event.imageUrl}
                alt={eventName}
                fill
                className="object-cover"
              />
            </div>
          )}

          {/* Event Details */}
          <div className="flex-1">
            <Link href={`/event/${event.slug}`}>
              <Button variant="link" className="!px-0">
                <h2 className="text-xl font-semibold">{eventName}</h2>
                <ExternalLink className="size-[16px]" />
              </Button>
            </Link>
            <div className="mt-4 flex flex-col gap-2">
              {event.startDate && (
                <div className="flex items-center gap-2 text-sm">
                  <CalendarIcon className="h-4 w-4" />
                  <span>
                    {formatDate(event.startDate)}
                    {event.endDate &&
                      event.endDate.toString() !== event.startDate.toString() &&
                      ` - ${formatDate(event.endDate)}`}
                  </span>
                </div>
              )}

              {event.location && (
                <div className="flex items-center gap-2 text-sm">
                  <MapPinIcon className="h-4 w-4" />
                  <span>{event.location}</span>
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-between">
              <p className="text-sm text-muted-foreground">
                {totalOrders} {totalOrders === 1 ? 'order' : 'orders'} •{' '}
                {totalTickets} {totalTickets === 1 ? 'ticket' : 'tickets'}
              </p>

              <Button
                variant="outline"
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-4 flex items-center gap-2 self-end"
              >
                {isExpanded ? 'Hide Orders' : 'View Orders'}
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="border-t">
          {orders.map((order) => (
            <OrderCard key={order.id} order={order} />
          ))}
        </div>
      )}
    </div>
  );
}
