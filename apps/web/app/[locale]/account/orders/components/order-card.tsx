'use client';

import type { Order } from '@/app/[locale]/account/orders/action';
import { Button } from '@repo/design-system/components/ui/button';
import { formatDate } from '@repo/design-system/lib/format';
import { TicketIcon } from 'lucide-react';
import Link from 'next/link';

interface OrderCardProps {
  order: Order;
}

export function OrderCard({ order }: OrderCardProps) {
  // Calculate total tickets
  const totalTickets = order.tickets.length;

  return (
    <div className="border-b last:border-b-0">
      <div className="p-4 md:p-6">
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div>
            <div className="flex items-center gap-2">
              <TicketIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">
                Order #{order.id.substring(0, 8)}
              </span>
            </div>

            <div className="mt-2">
              <p className="text-sm text-muted-foreground">
                Ordered on {formatDate(order.orderedAt)}
              </p>
            </div>

            <div className="mt-4">
              <p className="text-sm">
                <span className="font-medium">{totalTickets}</span>{' '}
                {totalTickets === 1 ? 'ticket' : 'tickets'}
              </p>
            </div>
          </div>

          <div className="flex items-center">
            <Link href={`/account/orders/${order.id}`}>
              <Button variant="outline" size="sm">
                View Details
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
