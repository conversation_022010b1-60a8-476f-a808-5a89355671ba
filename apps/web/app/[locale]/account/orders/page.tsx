import { getOrders } from '@/app/[locale]/account/orders/action';
import { EventOrderGroup } from './components/event-order-group';

export default async function OrdersPage() {
  const { groupedOrders } = await getOrders();

  return (
    <div className="mx-auto w-full space-y-8 px-4 my-10">
      <h1 className="text-2xl font-semibold mb-6">Your Orders</h1>
      {Object.keys(groupedOrders).length > 0 ? (
        <div className="space-y-8">
          {Object.entries(groupedOrders).map(([eventName, orders]) => (
            <EventOrderGroup
              key={eventName}
              eventName={eventName}
              orders={orders}
              event={orders[0].event}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">You don't have any orders yet.</p>
        </div>
      )}
    </div>
  );
}
