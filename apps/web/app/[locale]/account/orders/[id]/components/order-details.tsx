'use client';

import type { Order } from '@/app/[locale]/account/orders/action';
import { formatDate } from '@repo/design-system/lib/format';
import { CalendarIcon, MapPinIcon, TicketIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
} from '@repo/design-system/components/ui/card';

interface OrderDetailsProps {
  order: Order;
}

export function OrderDetails({ order }: OrderDetailsProps) {
  const { event, orderedAt, id, totalAmount, paymentStatus } = order;
  
  return (
    <Card className="mb-8">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="flex-1">
          <h2 className="text-xl font-semibold">Order #{id.substring(0, 8)}</h2>
          <p className="text-sm text-muted-foreground">
            Ordered on {formatDate(orderedAt)}
          </p>
        </div>
        {paymentStatus && (
          <div className="text-right">
            <p className="text-sm font-medium">Payment Status</p>
            <p className="text-sm text-muted-foreground capitalize">{paymentStatus}</p>
          </div>
        )}
        {totalAmount !== undefined && (
          <div className="text-right">
            <p className="text-sm font-medium">Total Amount</p>
            <p className="text-sm text-muted-foreground">${totalAmount.toFixed(2)}</p>
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        <div className="flex flex-col md:flex-row gap-6">
          {/* Event Image */}
          {event.imageUrl && (
            <div className="relative h-32 w-32 overflow-hidden rounded-md">
              <Image
                src={event.imageUrl}
                alt={event.name}
                fill
                className="object-cover"
              />
            </div>
          )}
          
          {/* Event Details */}
          <div className="flex-1">
            <Link href={`/event/${event.slug}`}>
              <Button variant="link" className="!px-0">
                <h3 className="text-lg font-medium">{event.name}</h3>
              </Button>
            </Link>
            
            <div className="mt-2 flex flex-col gap-2">
              {event.startDate && (
                <div className="flex items-center gap-2 text-sm">
                  <CalendarIcon className="h-4 w-4" />
                  <span>
                    {formatDate(event.startDate)}
                    {event.endDate &&
                      event.endDate.toString() !== event.startDate.toString() &&
                      ` - ${formatDate(event.endDate)}`}
                  </span>
                </div>
              )}
              
              {event.location && (
                <div className="flex items-center gap-2 text-sm">
                  <MapPinIcon className="h-4 w-4" />
                  <span>{event.location}</span>
                </div>
              )}
              
              <div className="flex items-center gap-2 text-sm">
                <TicketIcon className="h-4 w-4" />
                <span>{order.tickets.length} {order.tickets.length === 1 ? 'ticket' : 'tickets'}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
