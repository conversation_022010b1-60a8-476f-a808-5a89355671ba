'use client';

import type { Order } from '@/app/[locale]/account/orders/action';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
} from '@repo/design-system/components/ui/card';
import { downloadTicket } from '@repo/email/utils/pdf';
import { DownloadIcon, Loader2 } from 'lucide-react';
import { useState } from 'react';

interface OrderTicketsProps {
  tickets: Order['tickets'];
}

export function OrderTickets({ tickets }: OrderTicketsProps) {
  const [downloadingTicketId, setDownloadingTicketId] = useState<string | null>(
    null
  );

  const handleDownloadTicket = async (ticketId: string) => {
    try {
      setDownloadingTicketId(ticketId);
      const downloadUrl = await downloadTicket(ticketId);

      if (downloadUrl) {
        // Open the PDF in a new tab
        window.open(downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Error downloading ticket:', error);
    } finally {
      setDownloadingTicketId(null);
    }
  };

  // Group tickets by type
  const ticketsByType = tickets.reduce<Record<string, typeof tickets>>(
    (acc, ticket) => {
      if (!acc[ticket.ticketTypeId]) {
        acc[ticket.ticketTypeId] = [];
      }
      acc[ticket.ticketTypeId].push(ticket);
      return acc;
    },
    {}
  );

  return (
    <Card>
      <CardHeader>
        <h2 className="text-xl font-semibold">Tickets</h2>
      </CardHeader>
      <CardContent className="space-y-4">
        {Object.entries(ticketsByType).map(([typeId, typeTickets]) => (
          <div key={typeId} className="border rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">
                Ticket Type: {typeTickets[0].ticketTypeName}
              </h3>
              <Badge>{typeTickets.length} tickets</Badge>
            </div>

            <div className="space-y-2">
              {typeTickets.map((ticket) => (
                <div
                  key={ticket.id}
                  className="flex justify-between items-center p-3 bg-muted/50 rounded-md"
                >
                  <div>
                    <p className="font-medium">Ticket #{ticket.slug}</p>
                    <p className="text-sm text-muted-foreground capitalize">
                      Status: {ticket.status}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadTicket(ticket.id)}
                    disabled={downloadingTicketId === ticket.id}
                  >
                    {downloadingTicketId === ticket.id ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <DownloadIcon className="h-4 w-4 mr-2" />
                        Download
                      </>
                    )}
                  </Button>
                </div>
              ))}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
