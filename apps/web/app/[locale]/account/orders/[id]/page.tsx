import { getOrder } from '@/app/[locale]/account/orders/action';
import { OrderDetails } from '@/app/[locale]/account/orders/[id]/components/order-details';
import { OrderTickets } from '@/app/[locale]/account/orders/[id]/components/order-tickets';
import { notFound } from 'next/navigation';

interface OrderDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function OrderDetailPage({
  params,
}: OrderDetailPageProps) {
  const { id } = await params;
  const order = await getOrder(id);

  if (!order) {
    return notFound();
  }

  return (
    <div className="mx-auto w-full space-y-8 px-4 my-10">
      <h1 className="text-2xl font-semibold mb-6">Order Details</h1>
      <OrderDetails order={order} />
      <OrderTickets tickets={order.tickets} />
    </div>
  );
}
