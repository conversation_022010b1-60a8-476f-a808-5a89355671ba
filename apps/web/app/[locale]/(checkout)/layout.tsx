import { EventHeader } from '@/components/header/event-header';
import type { ReactNode } from 'react';

type CheckoutLayoutProperties = {
  readonly children: ReactNode;
};

const CheckoutLayout = ({ children }: CheckoutLayoutProperties) => {
  return (
    <>
      <EventHeader />
      <main className="mx-auto flex max-w-4xl flex-col px-4 py-24">
        {children}
      </main>
    </>
  );
};

export default CheckoutLayout;
