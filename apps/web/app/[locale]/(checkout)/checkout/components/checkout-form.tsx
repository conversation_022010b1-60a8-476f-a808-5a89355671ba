'use client';

import CheckoutRemark from '@/app/[locale]/(checkout)/checkout/components/checkout-remark';
import type { SerializedCart } from '@/app/types';
import type { ChipPaymentMethod } from '@/app/[locale]/(checkout)/checkout/utils/constant';
import { PaymentMethodPicker } from '@/components/payment-method-picker';
import { zodResolver } from '@hookform/resolvers/zod';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import { Form, useForm } from '@repo/design-system/components/ui/form';
import { Separator } from '@repo/design-system/components/ui/separator';
import { toast } from '@repo/design-system/components/ui/sonner';
import Link from 'next/link';
import { useEffect, useMemo } from 'react';
import * as z from 'zod';
import { checkout } from '../actions';
import { CheckoutFooter } from './checkout-footer';
import { ContactInformation } from './contact-information';
import { OrderSummary } from './order-summary';

interface CheckoutFormProps {
  cart: SerializedCart;
  existingUserInfo?: Partial<CheckoutFormValues> | null;
  existingOrderId?: string | null;
  paymentMethods: Record<string, ChipPaymentMethod>;
}

export const checkoutFormSchema = z.object({
  fullName: z.string().min(1, 'Please enter your full name'),
  email: z.string().email('Please enter a valid email'),
  phone: z.string().min(1, 'Please enter phone number'),
  cardNumber: z
    .string()
    .regex(/^[\d\s]{19}$/, 'Please enter a valid 16-digit card number')
    .optional()
    .or(z.literal('')),
  expiryDate: z
    .string()
    .regex(
      /^(0[1-9]|1[0-2])\/([0-9]{2})$/,
      'Please enter a valid expiry date (MM/YY)'
    )
    .refine((val) => {
      if (!val) {
        return true;
      }
      const [month, year] = val.split('/');
      const expiry = new Date(
        2000 + Number.parseInt(year),
        Number.parseInt(month) - 1
      );
      return expiry > new Date();
    }, 'Card has expired')
    .optional()
    .or(z.literal('')),
  cvv: z
    .string()
    .regex(/^\d{3,4}$/, 'Please enter a valid CVV')
    .optional()
    .or(z.literal('')),
  acceptMarketing: z.boolean().optional(),
  paymentMethod: z.enum(['fpx', 'card', 'duitnow', 'eWallet'], {
    required_error: 'Please select a payment method',
    invalid_type_error: 'Please select a valid payment method',
  }),
  remark: z.string().optional(),
});

export type CheckoutFormValues = z.infer<typeof checkoutFormSchema>;

// Storage key for checkout form data
const getStorageKey = (cartId: string) => `checkout_form_${cartId}`;

// Function to save form data to localStorage
const saveFormDataToStorage = (
  data: Partial<CheckoutFormValues>,
  cartId: string
) => {
  // Only store non-sensitive information (exclude card details)
  const dataToStore = {
    fullName: data.fullName || '',
    email: data.email || '',
    phone: data.phone || '',
    acceptMarketing: data.acceptMarketing || false,
    paymentMethod: data.paymentMethod || 'chip',
    remark: data.remark || '',
  };

  try {
    localStorage.setItem(getStorageKey(cartId), JSON.stringify(dataToStore));
  } catch (error) {
    console.error('Error saving form data to localStorage:', error);
  }
};

// Function to load form data from localStorage
const loadFormDataFromStorage = (
  cartId: string
): Partial<CheckoutFormValues> => {
  try {
    const storedData = localStorage.getItem(getStorageKey(cartId));
    if (storedData) {
      return JSON.parse(storedData);
    }
  } catch (error) {
    console.error('Error loading form data from localStorage:', error);
  }
  return {};
};

// Function to clear form data from localStorage
// const clearFormDataFromStorage = (cartId: string) => {
//   try {
//     localStorage.removeItem(getStorageKey(cartId));
//   } catch (error) {
//     console.error('Error clearing form data from localStorage:', error);
//   }
// };

export function CheckoutForm({
  cart,
  existingUserInfo,
  existingOrderId,
  paymentMethods,
}: CheckoutFormProps) {
  // Load saved form data if available
  const savedFormData =
    typeof window !== 'undefined' ? loadFormDataFromStorage(cart.id) : {};

  // Prioritize existing user info from database over localStorage
  const methods = useForm<CheckoutFormValues>({
    resolver: zodResolver(checkoutFormSchema),
    defaultValues: {
      fullName: existingUserInfo?.fullName || savedFormData.fullName || '',
      email: existingUserInfo?.email || savedFormData.email || '',
      phone: existingUserInfo?.phone || savedFormData.phone || '',
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      // acceptTerms: false,
      acceptMarketing: savedFormData.acceptMarketing || true,
      paymentMethod:
        existingUserInfo?.paymentMethod ||
        savedFormData.paymentMethod ||
        (Object.keys(paymentMethods)[0] as CheckoutFormValues['paymentMethod']),
      remark: existingUserInfo?.remark || savedFormData.remark || '',
    },
  });

  const formPaymentMethod = methods.watch('paymentMethod');

  const { total, subtotal, chipProcessingFee } = useMemo(() => {
    const selectedChipPaymentMethod = paymentMethods[formPaymentMethod];
    let calculatedTotal: number;
    let calculatedChipProcessingFee: number | null = null;

    const calculatedSubtotal = cart.cartItems.reduce((acc, item) => {
      return acc + Number(item.ticketType.price) * item.quantity;
    }, 0);

    if (selectedChipPaymentMethod) {
      const fixedFee = selectedChipPaymentMethod?.rateFixed || 0;
      const percentFee =
        calculatedSubtotal * (selectedChipPaymentMethod?.ratePercent || 0);
      calculatedChipProcessingFee = fixedFee + percentFee;

      // Check ticket sales mode to determine fee handling
      const ticketSalesMode = cart.event?.ticketSalesMode || 'pass_on_fee';

      if (ticketSalesMode === 'absorb_fee') {
        // Absorb fee mode: customer pays ticket price, organizer absorbs processing fee
        calculatedTotal = calculatedSubtotal;
      } else {
        // Pass-on fee mode: customer pays ticket price + processing fee
        calculatedTotal = calculatedSubtotal + calculatedChipProcessingFee;
      }
    } else {
      calculatedTotal = calculatedSubtotal;
    }

    return {
      total: calculatedTotal,
      subtotal: calculatedSubtotal,
      chipProcessingFee: calculatedChipProcessingFee,
    };
  }, [
    formPaymentMethod,
    cart.cartItems,
    cart.event?.ticketSalesMode,
    paymentMethods,
  ]);

  // Watch for form value changes and save to localStorage
  useEffect(() => {
    const subscription = methods.watch((formValues) => {
      if (formValues.fullName || formValues.email || formValues.phone) {
        saveFormDataToStorage(
          formValues as Partial<CheckoutFormValues>,
          cart.id
        );
      }
    });

    return () => subscription.unsubscribe();
  }, [methods, cart.id]);

  // Helper function to prettify error messages
  const prettifyErrorMessage = (errorMessage: string): string => {
    // Handle database/Prisma constraint errors
    if (
      errorMessage.includes(
        'Unique constraint failed on the fields: (`cart_id`)'
      )
    ) {
      return 'This order is already being processed. Please wait or refresh the page.';
    }

    // Handle other Prisma constraint errors
    if (errorMessage.includes('Unique constraint failed')) {
      return 'This information is already in use. Please try again.';
    }

    // Handle Prisma validation errors
    if (
      errorMessage.includes('Invalid `prisma.') &&
      errorMessage.includes('invocation:')
    ) {
      return 'There was an issue processing your request. Please try again.';
    }

    // Handle cart expiration
    if (errorMessage.includes('Cart has expired')) {
      return 'Your session has expired. Please add items to your cart again.';
    }

    // Handle existing order scenarios
    if (errorMessage.includes('Order not found or is no longer valid')) {
      return 'Your previous order session has expired. Please try again.';
    }

    if (errorMessage.includes('Found existing order in hold status')) {
      return 'Continuing with your previous order. Please proceed with payment.';
    }

    // Handle inventory errors
    if (errorMessage.includes('Not enough tickets available')) {
      return errorMessage; // Keep these specific as they're user-friendly
    }

    // Handle payment errors
    if (errorMessage.includes('Failed to process payment')) {
      return 'Payment processing failed. Please check your payment details and try again.';
    }

    // Handle network/connection errors
    if (errorMessage.includes('fetch') || errorMessage.includes('network')) {
      return 'Connection error. Please check your internet connection and try again.';
    }

    // Remove "Checkout failed:" prefix if present
    const cleanMessage = errorMessage.replace(/^Checkout failed: /, '');

    // Return the original message if no specific pattern matches
    return cleanMessage;
  };

  const onSubmit = async (data: CheckoutFormValues) => {
    try {
      if (cart.event?.checkoutFormQuestion && !data.remark) {
        methods.setError('remark', {
          message: 'Please enter additional information',
        });
        toast.error('Please enter additional information');
        return;
      }

      // Save form data before submitting
      saveFormDataToStorage(data, cart.id);

      // Show appropriate loading message
      toast.loading('Processing your payment...', { id: 'checkout' });

      // Use the unified checkout function with optional existingOrderId
      const result = await checkout(data, cart.id, existingOrderId);

      // As of now, our result must be a redirect URL
      if (result?.success && result.redirectUrl) {
        // We don't clear the form data here because payment might fail
        // It will be cleared on successful payment confirmation
        window.location.href = result.redirectUrl;
        return;

        // If no redirect URL (direct success), clear the stored form data
        // clearFormDataFromStorage(cart.id);

        // Otherwise, show success message
        // router.push(`/events/${slug}/confirmation`)
      }
    } catch (error) {
      console.error('Checkout error:', error);
      if (error instanceof Error) {
        const prettifiedMessage = prettifyErrorMessage(error.message);
        toast.error(prettifiedMessage, {
          id: 'checkout',
        });
      } else {
        toast.error('Failed to process your order. Please try again.', {
          id: 'checkout',
        });
      }
    }
  };

  return (
    <Form {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="w-full">
        <div className="mt-12 mb-16 space-y-8 p-4">
          <OrderSummary
            cart={cart}
            chipProcessingFee={chipProcessingFee}
            subtotal={subtotal}
            total={total}
            paymentMethodRatePercent={
              paymentMethods[formPaymentMethod]?.ratePercent || null
            }
          />
          <Separator />

          <ContactInformation />
          <Separator />

          <CheckoutRemark cart={cart} />
          <Separator />

          <PaymentMethodPicker paymentMethods={paymentMethods} />
          <Separator />

          <>
            <div className="items-top flex space-x-2">
              <Checkbox
                id="acceptMarketing"
                defaultChecked
                {...methods.register('acceptMarketing')}
              />
              <div className="grid gap-1.5 leading-none">
                <label
                  htmlFor="acceptMarketing"
                  className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Keep me updated on the latest news, events, and exclusive
                  offers on this event organizer.{' '}
                </label>
              </div>
            </div>

            <p className="text-muted-foreground text-sm">
              By clicking “Checkout”, I accept the{' '}
              <Link
                href="/legal/terms-of-service"
                className="text-primary hover:underline"
                target="_blank"
              >
                Terms of Service
              </Link>{' '}
              and have read{' '}
              <Link
                href="/legal/consumer-data-privacy-policy"
                className="text-primary hover:underline"
                target="_blank"
              >
                Privacy Policy
              </Link>
              . I agree that TicketCARE may share my information with the event
              organizer.
            </p>

            {/* {methods.formState.errors.acceptTerms && (
              <p className="text-sm text-red-500">
                {methods.formState.errors.acceptTerms.message}
              </p>
            )} */}
          </>

          <CheckoutFooter total={total} />
        </div>
      </form>
    </Form>
  );
}
