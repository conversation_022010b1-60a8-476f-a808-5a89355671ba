import type { SerializedCart } from '@/app/types';
import { Info } from '@repo/design-system/components/icons';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { useFormContext } from '@repo/design-system/components/ui/form';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import {
  formatCurrency,
  formatDate,
  formatTime,
} from '@repo/design-system/lib/format';
import Image from 'next/image';
import type { FC } from 'react';

type OrderSummaryProps = {
  cart: SerializedCart;
  chipProcessingFee?: number | null;
  subtotal: number;
  total: number;
  paymentMethodRatePercent?: number | null; // Pass the rate percent from parent component
};

export const OrderSummary: FC<OrderSummaryProps> = ({
  cart,
  chipProcessingFee,
  subtotal,
  total,
  paymentMethodRatePercent,
}) => {
  const event = cart.event;

  const timeSlot =
    cart.cartItems.length > 0 ? cart.cartItems[0]?.timeSlot : null;

  return (
    <div className="space-y-[24px]">
      <h2 className="font-semibold text-lg">Order Summary</h2>

      {/* supposed to be able to purchase single event only */}
      <Card className="flex">
        <CardHeader>
          <Image
            src={event?.heroImageUrl ?? ''}
            alt={event?.title ?? 'event-image'}
            width={80}
            height={80}
            className="rounded-lg object-cover"
          />
        </CardHeader>
        <div className="flex-1">
          <CardHeader>
            <CardTitle>{event?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {timeSlot && (
              <>
                <CardDescription>
                  {formatDate(new Date(timeSlot?.startTime))}
                </CardDescription>
                <CardDescription>
                  {formatTime(new Date(timeSlot?.startTime))}
                </CardDescription>
              </>
            )}
          </CardContent>
        </div>
      </Card>

      <div className="space-y-2 text-sm">
        {cart.cartItems.map((item) => (
          <div key={item.id} className="space-y-2">
            <div className="flex justify-between">
              <span>
                {item.ticketType.name} x {item.quantity}
              </span>
              <span>
                {formatCurrency(Number(item.ticketType.price) * item.quantity)}
              </span>
            </div>
          </div>
        ))}
        <div className="mt-4 flex justify-between text-muted-foreground">
          <span>Subtotal</span>
          <span>{formatCurrency(subtotal)}</span>
        </div>
        {(chipProcessingFee ?? 0) > 0 && (
          <div className="flex justify-between text-muted-foreground">
            <div className="flex items-center gap-1">
              <span className="flex items-center gap-1">
                Processing Fee{' '}
                {paymentMethodRatePercent
                  ? `(${(paymentMethodRatePercent * 100).toFixed(1)}%)`
                  : ''}
                {event?.ticketSalesMode === 'absorb_fee' && (
                  <span className="text-xs text-green-600 ml-1">
                    (Absorbed by organizer)
                  </span>
                )}
              </span>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="ml-1 h-4 w-4 text-gray-500" />
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  {event?.ticketSalesMode === 'absorb_fee'
                    ? 'Processing fees are absorbed by the organizer. You pay the ticket price only.'
                    : 'Transaction costs charged by payment processors.'}
                </TooltipContent>
              </Tooltip>
            </div>
            <span>
              {event?.ticketSalesMode === 'absorb_fee'
                ? 'RM 0.00'
                : formatCurrency(chipProcessingFee ?? 0)}
            </span>
          </div>
        )}
        <div className="flex justify-between border-t pt-2 font-medium">
          <span>Total</span>
          <span>{formatCurrency(total)}</span>
        </div>
      </div>
    </div>
  );
};
