'use client';

import type { SerializedCart } from '@/app/types';
import { CartStatus } from '@repo/database/types';
import { calculateTimeLeft } from '@repo/design-system/lib/format';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface CheckoutTimerProps {
  cart: SerializedCart;
}

export function CheckoutTimer({ cart }: CheckoutTimerProps) {
  const [timeLeft, setTimeLeft] = useState<string>('00:00');
  const router = useRouter();

  useEffect(() => {
    //
    if (cart.status !== CartStatus.active) {
      return;
    }

    // Function to update the timer display
    const updateTimer = () => {
      const timeLeft = calculateTimeLeft(new Date(cart.expiresAt));
      setTimeLeft(
        `${timeLeft.minutes}:${timeLeft.seconds < 10 ? '0' : ''}${timeLeft.seconds}`
      );

      if (timeLeft.minutes === 0 && timeLeft.seconds === 0) {
        clearInterval(interval);
        // Redirect to cart error page
        router.push(`/checkout/expired?cartId=${cart.id}`);
      }
    };

    // Update immediately on mount
    updateTimer();

    // Then update every second
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [cart.status, cart.expiresAt, cart.id, router]);

  if (cart.status !== CartStatus.active) {
    return <></>;
  }

  return (
    <div className="fixed top-16 right-0 left-0 bg-red-500 py-2 text-center text-white">
      Your tickets are held for{' '}
      <span className="inline-block min-w-[3.5rem] font-mono">{timeLeft}</span>
    </div>
  );
}
