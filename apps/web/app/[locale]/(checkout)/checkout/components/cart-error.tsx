'use client';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import { ArrowLeft, ShoppingCart } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function CartError({
  error,
}: { error: string; showCreateNewCart?: boolean }) {
  const router = useRouter();
  // NOTE: using usecart here throws error
  // let FE create another cart on next addTocart() invocation
  // const { createNewCart } = useCart();
  return (
    <div className="flex min-h-[500px] w-full flex-col items-center justify-center p-4 md:p-6">
      <div className="flex w-full max-w-md flex-col items-center space-y-6 text-center">
        <div className="rounded-full bg-red-100 p-6">
          <ShoppingCart className="h-12 w-12 text-red-500" />
        </div>

        <div className="space-y-3">
          <h2 className="font-semibold text-secondary-foreground text-xl">
            Error
          </h2>
          <p className="text-muted-foreground">{error}</p>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <Button
            onClick={() => {
              // // Create new cart optimistically without waiting
              // createNewCart().catch((error) => {
              //   console.error('Background cart creation failed:', error);
              //   // No need to show error to user as they're already redirected
              // });
              // Redirect immediately without waiting for cart creation
              router.push('/events');
            }}
            className="flex items-center space-x-2"
            variant="default"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Go Back</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
