import type { SerializedCart } from '@/app/types';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useFormContext,
} from '@repo/design-system/components/ui/form';
import { Textarea } from '@repo/design-system/components/ui/textarea';

interface CheckoutRemarkProps {
  cart: SerializedCart;
}

export default function CheckoutRemark({ cart }: CheckoutRemarkProps) {
  const form = useFormContext();

  if (!cart.event?.checkoutFormQuestion) {
    return null;
  }

  return (
    <div className="space-y-[24px]">
      <h2 className="font-semibold text-lg">Additional Information*</h2>
      <div className="space-y-[16px]">
        <FormField
          control={form.control}
          name="remark"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-muted-foreground text-sm">
                {cart.event?.checkoutFormQuestion}
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Remark"
                  className="h-[80px] w-full p-2"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
