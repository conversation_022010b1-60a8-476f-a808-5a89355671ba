'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { useFormContext } from '@repo/design-system/components/ui/form';
import { formatCurrency } from '@repo/design-system/lib/format';
import { ArrowRight } from 'lucide-react';

interface CheckoutFooterProps {
  total: number;
}

export function CheckoutFooter({ total }: CheckoutFooterProps) {
  const {
    formState: { isLoading, isSubmitting },
  } = useFormContext();

  return (
    <div className="fixed right-0 bottom-0 left-0 h-16 border-t bg-background">
      <div className="mx-auto flex w-full max-w-4xl items-center justify-between gap-4 bg-background px-4 py-3">
        <span className="font-bold text-xl">{formatCurrency(total)}</span>

        <Button size="lg" type="submit" disabled={isSubmitting}>
          {isLoading || isSubmitting ? 'Processing...' : 'Checkout'}
          <ArrowRight size={20} />
        </Button>
      </div>
    </div>
  );
}
