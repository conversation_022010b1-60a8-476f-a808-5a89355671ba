'use server';

import { database } from '@repo/database';
import { log } from '@repo/observability/log';

/**
 * Send order confirmation email
 * @param orderId Order ID
 */
export async function sendOrderConfirmationEmail(orderId: string) {
  try {
    // Get order details with all related information
    const order = await database.order.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        event: true,
        tickets: {
          include: {
            ticketType: true,
            timeSlot: true,
          },
        },
      },
    });

    if (!order) {
      log.warn(`Order not found for sending confirmation email: ${orderId}`);
      return;
    }

    // In a real implementation, you would use an email service like SendGrid, Mailgun, etc.
    // For now, we'll just log the email content
    log.info(`Sending order confirmation email for order ${orderId}`, {
      to: order.user.email,
      subject: `Order Confirmation #${orderId}`,
      orderDetails: {
        orderId: order.id,
        eventName: order.event?.title,
        totalAmount: order.totalAmount.toString(),
        ticketCount: order.tickets.length,
        paymentStatus: order.paymentStatus,
      },
    });

    // In a real implementation, you would return the result of the email sending operation
    return { success: true };
  } catch (error) {
    log.error(`Error sending order confirmation email: ${error}`);
    return { success: false, error: String(error) };
  }
}

/**
 * Send ticket details email
 * @param orderId Order ID
 */
export async function sendTicketDetailsEmail(orderId: string) {
  try {
    // Get order details with all related information
    const order = await database.order.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        event: true,
        tickets: {
          include: {
            ticketType: true,
            timeSlot: true,
          },
        },
      },
    });

    if (!order) {
      log.warn(`Order not found for sending ticket details email: ${orderId}`);
      return;
    }

    // In a real implementation, you would use an email service like SendGrid, Mailgun, etc.
    // For now, we'll just log the email content
    log.info(`Sending ticket details email for order ${orderId}`, {
      to: order.user.email,
      subject: `Your Tickets for ${order.event?.title}`,
      ticketDetails: order.tickets.map((ticket) => ({
        ticketId: ticket.id,
        slug: ticket.slug,
        eventName: order.event?.title,
        ticketType: ticket.ticketType.name,
        date: ticket.timeSlot.startTime,
      })),
    });

    // In a real implementation, you would return the result of the email sending operation
    return { success: true };
  } catch (error) {
    log.error(`Error sending ticket details email: ${error}`);
    return { success: false, error: String(error) };
  }
}
