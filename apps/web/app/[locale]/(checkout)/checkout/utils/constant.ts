import { env } from '@/env';

export type ChipPaymentMethod = {
  methods: string[];
  rateFixed?: number;
  ratePercent?: number;
};

export const getPaymentMethods = async (
  hasByok: boolean,
  chipSecretKey?: string | null,
  brandId?: string | null
): Promise<Record<string, ChipPaymentMethod>> => {
  if (hasByok) {
    if (!chipSecretKey || !brandId) {
      console.error(
        'Chip secret key or brand ID not provided, returning default BYOK methods'
      );
      return BYOK_CHIP_PAYMENT_METHODS;
    }

    try {
      // Fetch available payment methods from chip-in payment gateway
      const response = await fetch(
        // Set amount to 100 to retrieve duitnow & fpx, they require minimum 100 to be activated
        `https://gate.chip-in.asia/api/v1/payment_methods/?brand_id=${brandId}&currency=MYR&amount=100`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${chipSecretKey}`,
            'Content-Type': 'application/json',
          },
          // Add a timeout of 10 seconds
          signal: AbortSignal.timeout(10000),
        }
      );

      if (!response.ok) {
        console.error(
          `Failed to fetch payment methods: ${response.status} ${response.statusText}`
        );
        // Return default BYOK methods in case of API failure
        return BYOK_CHIP_PAYMENT_METHODS;
      }

      const data = await response.json();

      // Transform the API response into the format expected by our application
      const availableMethods = data.available_payment_methods || [];

      // Group payment methods into categories (card, eWallet, etc.) based on the response
      const methodsByCategory: Record<string, string[]> = {};

      // Initialize categories
      methodsByCategory.card = [];
      methodsByCategory.eWallet = [];
      methodsByCategory.fpx = [];
      methodsByCategory.duitnow = [];

      // Categorize payment methods based on their names
      for (const method of availableMethods) {
        if (['visa', 'mastercard', 'american_express'].includes(method)) {
          methodsByCategory.card.push(method);
        } else if (
          [
            'razer_grabpay',
            'razer_shopeepay',
            'razer_tng',
            'boost',
            'shopeepay',
            'touchngo',
          ].includes(method)
        ) {
          methodsByCategory.eWallet.push(method);
        } else if (method === 'fpx') {
          methodsByCategory.fpx.push(method);
        } else if (['duitnow_qr', 'duitnow', 'duitnow_obw'].includes(method)) {
          methodsByCategory.duitnow.push(method);
        } else {
          // If it doesn't fit into known categories, add it to card as a fallback
          methodsByCategory.card.push(method);
        }
      }

      // Create a payment methods object based on the available methods from the API
      const paymentMethods: Record<string, ChipPaymentMethod> = {};

      // Add card methods if available
      if (methodsByCategory.card.length > 0) {
        paymentMethods.card = {
          methods: methodsByCategory.card,
          ratePercent: BYOK_CHIP_PAYMENT_METHODS.card.ratePercent,
        };
      }

      // Add eWallet methods if available
      if (methodsByCategory.eWallet.length > 0) {
        paymentMethods.eWallet = {
          methods: methodsByCategory.eWallet,
          ratePercent: BYOK_CHIP_PAYMENT_METHODS.eWallet.ratePercent,
        };
      }

      // Add FPX methods if available
      if (methodsByCategory.fpx.length > 0) {
        paymentMethods.fpx = {
          methods: methodsByCategory.fpx,
          rateFixed: BYOK_CHIP_PAYMENT_METHODS.fpx.rateFixed,
        };
      }

      // Add DuitNow methods if available
      if (methodsByCategory.duitnow.length > 0) {
        paymentMethods.duitnow = {
          methods: methodsByCategory.duitnow,
          ratePercent: BYOK_CHIP_PAYMENT_METHODS.duitnow.ratePercent,
        };
      }

      return paymentMethods;
    } catch (error) {
      console.error('Error fetching payment methods from Chip-in API:', error);

      // In case of any error (network, timeout, etc.), return default BYOK methods
      return BYOK_CHIP_PAYMENT_METHODS;
    }
  }

  return CHIP_PAYMENT_METHODS;
};

// rate for our own ticketcare payment
const CHIP_PAYMENT_METHODS: Record<string, ChipPaymentMethod> = {
  fpx: {
    methods: ['fpx'],
    rateFixed: Number.parseInt(env.NEXT_PUBLIC_CHIP_FPX_RATE ?? '0'),
  },
  card: {
    methods: ['visa', 'mastercard'],
    ratePercent: Number.parseFloat(env.NEXT_PUBLIC_CHIP_CARD_RATE ?? '0'),
  },
  duitnow: {
    methods: ['duitnow_qr'],
    ratePercent: Number.parseFloat(env.NEXT_PUBLIC_CHIP_DUITNOW_RATE ?? '0'),
  },
  eWallet: {
    methods: ['razer_grabpay', 'razer_shopeepay', 'razer_tng'],
    ratePercent: Number.parseFloat(env.NEXT_PUBLIC_CHIP_EWALLET_RATE ?? '0'),
  },
};

// rate for byok
export const BYOK_CHIP_PAYMENT_METHODS: Record<string, ChipPaymentMethod> = {
  fpx: {
    methods: ['fpx'],
    rateFixed: 1,
  },
  card: {
    methods: ['visa', 'mastercard'],
    ratePercent: 0.03,
  },
  duitnow: {
    methods: ['duitnow_qr'],
    ratePercent: 0.016,
  },
  eWallet: {
    methods: ['razer_grabpay', 'razer_shopeepay', 'razer_tng'],
    ratePercent: 0.014,
  },
};

// rate for donation
export const DONATE_CHIP_PAYMENT_METHODS: Record<string, ChipPaymentMethod> = {
  fpx: {
    methods: ['fpx'],
  },
  card: {
    methods: ['visa', 'mastercard'],
  },
  duitnow: {
    methods: ['duitnow_qr'],
  },
  eWallet: {
    methods: ['razer_grabpay', 'razer_shopeepay', 'razer_tng'],
  },
};
