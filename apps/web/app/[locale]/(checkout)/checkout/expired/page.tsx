'use client';

import { CartProvider } from '@/app/[locale]/(events)/_lib/cart-context';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { CartError } from '../components/cart-error';

export default function ExpiredCheckoutPage() {
  const searchParams = useSearchParams();
  const cartId = searchParams.get('cartId');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // This is just to ensure the page has time to load and render properly
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex min-h-[500px] w-full flex-col items-center justify-center p-4">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  return (
    <CartProvider initialCartData={null}>
      <CartError
        error={`Your checkout session has expired. The items you selected are no longer reserved.${
          cartId ? ` (Cart ID: ${cartId})` : ''
        }`}
      />
    </CartProvider>
  );
}
