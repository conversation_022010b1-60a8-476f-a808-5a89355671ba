'use server';

import { database, generateTicketSlug } from '@repo/database';
import CustomPaymentKeyService from '@repo/database/payment-keys';
import { CartStatus, OrderStatus, TicketStatus } from '@repo/database/types';
import { formatDate, formatTime } from '@repo/design-system/lib/format';
import { log } from '@repo/observability/log';
import { chip } from '@repo/payments';
import { type ChipClient, createChipClient } from '@repo/payments/chip';
import type { CheckoutFormValues } from './components/checkout-form';
import { getPaymentMethods } from './utils/constant';

type InventoryUpdate = {
  inventoryId: string;
  quantity: number;
};

type ChipPaymentMethod = {
  methods: string[];
  rateFixed?: number;
  ratePercent?: number;
};

// Helper function to validate cart and check if it's valid
export async function validateCart(cartId: string) {
  if (!cartId) {
    throw new Error('Invalid cart');
  }

  // find idle cart details
  const cart = await database.cart.findUnique({
    where: { id: cartId },
    include: {
      event: {
        include: {
          eventModule: {
            include: {
              customPaymentModule: true,
            },
          },
        },
      },
      cartItems: {
        include: {
          timeSlot: true,
          ticketType: true,
        },
      },
    },
  });

  if (
    !cart ||
    cart.status === CartStatus.converted ||
    cart.status === CartStatus.abandoned
  ) {
    throw new Error('Cart not found');
  }

  // check if cart expired
  if (cart.expiresAt < new Date()) {
    if (cart.status === 'expired') {
      throw new Error('Cart has expired, please try again');
    }

    // Update cart status to 'expired'
    database.cart.update({
      where: { id: cartId },
      data: { status: CartStatus.expired },
    });
    throw new Error('Cart has expired, please try again');
  }

  // Update cart status to 'active'
  await database.cart.update({
    where: { id: cartId },
    data: {
      status: CartStatus.active,
      // Update expiration time to give more time during payment processing
      expiresAt: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
    },
  });

  return cart;
}

// Helper function to calculate total amount from cart items
function calculateTotalAmount(
  cart: Awaited<ReturnType<typeof validateCart>>,
  chipPaymentMethod: ChipPaymentMethod
) {
  // Calculate base price
  const baseAmount = cart.cartItems.reduce((sum: number, item) => {
    const price = item.ticketType?.price
      ? Number.parseFloat(item.ticketType.price.toString())
      : 0;
    return sum + price * item.quantity;
  }, 0);

  // Calculate final price with chip processing rate
  if (chipPaymentMethod) {
    const percentFee = baseAmount * (chipPaymentMethod.ratePercent || 0);
    const fixedFee = chipPaymentMethod.rateFixed || 0;
    const processingFee = percentFee + fixedFee;

    // Check ticket sales mode to determine fee handling
    const ticketSalesMode = cart.event?.ticketSalesMode || 'pass_on_fee';

    if (ticketSalesMode === 'absorb_fee') {
      // Absorb fee mode: customer pays ticket price only, organizer absorbs processing fee
      return baseAmount;
    }

    // Pass-on fee mode: customer pays ticket price + processing fee
    return baseAmount + processingFee;
  }

  // Fallback to base amount
  return baseAmount;
}

// Helper function to get user information
async function getOrCreateUserInfo(formData: CheckoutFormValues) {
  const email = formData.email;
  const fullName = formData.fullName;
  const phone = formData.phone || '';

  // First, try to find an existing user by email
  let user = await database.user.findFirst({
    where: { email },
    select: {
      id: true,
      name: true,
      email: true,
      phone: true,
    },
  });

  // If no user exists, create one
  if (!user && email && fullName) {
    // Split name into first and last name
    const [firstName, ...lastNameParts] = fullName.split(' ');
    const lastName = lastNameParts.join(' ');

    user = await database.user.create({
      data: {
        firstName,
        lastName,
        name: fullName,
        email,
        emailVerified: false,
        phone,
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
      },
    });
  }

  // If we still don't have a user, throw an error
  if (!user) {
    throw new Error('Failed to create or find user');
  }

  return {
    userId: user.id,
    userEmail: user.email,
    userName: user.name || '',
    userPhone: user.phone || '',
  };
}

// Helper function to check inventory availability
async function checkInventoryAvailability(
  cart: Awaited<ReturnType<typeof validateCart>>
): Promise<InventoryUpdate[]> {
  const inventoryUpdates: InventoryUpdate[] = [];

  for (const item of cart.cartItems) {
    const currentDate = new Date();
    const startDate = new Date(item.ticketType?.saleStartTime || '');
    const endDate = new Date(item.ticketType?.saleEndTime || '');

    // Check if ticket sale hasn't started yet
    if (startDate > currentDate) {
      throw new Error(
        `Sales for "${item.ticketType?.name}" start on ${formatDate(startDate)} at ${formatTime(startDate)}.`
      );
    }

    // Check if ticket sale has already ended
    if (endDate < currentDate) {
      throw new Error(
        `Sales for "${item.ticketType?.name}" ended on ${formatDate(endDate)} at ${formatTime(endDate)}.`
      );
    }

    const inventory = await database.inventory.findFirst({
      where: {
        ticketTypeId: item.ticketTypeId,
        timeSlotId: item.timeSlotId,
      },
    });

    if (!inventory || inventory.quantity < item.quantity) {
      throw new Error(
        `Not enough tickets available for ${item.ticketType?.name}`
      );
    }

    inventoryUpdates.push({
      inventoryId: inventory.id,
      quantity: item.quantity,
    });
  }

  return inventoryUpdates;
}

// Helper function to update inventory
async function updateInventory(inventoryUpdates: InventoryUpdate[]) {
  for (const update of inventoryUpdates) {
    await database.inventory.update({
      where: {
        id: update.inventoryId,
      },
      data: {
        quantity: {
          decrement: update.quantity,
        },
      },
    });
  }
}

// Helper function to restore inventory in case of checkout failure
async function restoreInventory(inventoryUpdates: InventoryUpdate[]) {
  for (const update of inventoryUpdates) {
    await database.inventory.update({
      where: {
        id: update.inventoryId,
      },
      data: {
        quantity: {
          increment: update.quantity,
        },
      },
    });
  }
}

// Helper function to create order and tickets in a transaction
async function createOrderTransaction(
  cart: Awaited<ReturnType<typeof validateCart>>,
  userInfo: Awaited<ReturnType<typeof getOrCreateUserInfo>>,
  totalAmount: number,
  paymentMethod: string,
  remark: string
) {
  return await database.$transaction(async (tx) => {
    // Get the event ID from the first cart item
    const eventId = cart.eventId;

    // Create order on hold
    const order = await tx.order.create({
      data: {
        userId: userInfo.userId,
        cartId: cart.id,
        eventId,
        totalAmount,
        status: OrderStatus.hold,
        paymentMethod,
        transactionId: '',
        paymentStatus: 'pending',
        checkoutFormAnswer: remark,
        orderedAt: new Date(),
      },
    });

    // update cart with user id
    await tx.cart.update({
      where: { id: cart.id },
      data: {
        userId: order.userId, // bind user ID from order to cart
      },
    });

    // Create tickets on hold for each cart item
    for (const item of cart.cartItems) {
      // Pre-generate unique slugs
      let slugs = Array.from({ length: item.quantity }).map(() =>
        generateTicketSlug()
      );
      // Ensure all slugs are unique (just in case)
      const uniqueSlugs = new Set(slugs);
      if (uniqueSlugs.size !== slugs.length) {
        // If we found duplicates, regenerate until all are unique
        slugs = Array.from(uniqueSlugs);
        while (slugs.length < item.quantity) {
          const newSlug = generateTicketSlug();
          if (!slugs.includes(newSlug)) {
            slugs.push(newSlug);
          }
        }
      }

      // Create tickets in bulk using createMany
      await tx.ticket.createMany({
        data: Array.from({ length: item.quantity }).map((_, index) => ({
          eventId: item.ticketType.eventId,
          ticketTypeId: item.ticketTypeId,
          timeSlotId: item.timeSlotId,
          orderId: order.id,
          slug: slugs[index],
          status: TicketStatus.hold,
          purchaseDate: new Date(),
          ownerName: userInfo.userName,
          ownerEmail: userInfo.userEmail,
          ownerPhone: userInfo.userPhone,
        })),
      });
    }

    // Fetch created tickets
    const createdTickets = await tx.ticket.findMany({
      where: {
        orderId: order.id,
      },
    });

    return { order, tickets: createdTickets };
  });
}

// Helper function to prepare products for payment
function prepareProductsForPayment(
  cart: Awaited<ReturnType<typeof validateCart>>,
  chipPaymentMethod: ChipPaymentMethod
) {
  let cartPrice = 0;

  const ticketProduct = cart.cartItems.map((item) => {
    const price = item.ticketType?.price
      ? Number.parseFloat(item.ticketType.price.toString())
      : 0;

    cartPrice += price * item.quantity;

    return {
      name: item.ticketType?.name || 'Ticket',
      quantity: item.quantity.toString(),
      price: price,
      description: `${cart.event?.title || 'Event'} - ${item.ticketType?.name || 'Ticket'}`,
      category: 'ticket',
    };
  });

  const finalProduct = [...ticketProduct];

  // Only add processing fees to payment breakdown if in pass-on fee mode
  const ticketSalesMode = cart.event?.ticketSalesMode || 'pass_on_fee';

  if (ticketSalesMode === 'pass_on_fee') {
    if (chipPaymentMethod.rateFixed) {
      finalProduct.push({
        name: `Chip Processing Fee (RM ${(chipPaymentMethod.rateFixed).toFixed(2)})`,
        quantity: '1',
        price: chipPaymentMethod.rateFixed,
        description: 'Chip processing fee',
        category: 'processing_fee',
      });
    }

    if (chipPaymentMethod.ratePercent) {
      finalProduct.push({
        name: `Chip Processing Fee (${(chipPaymentMethod.ratePercent * 100).toFixed(2)}%)`,
        quantity: '1',
        price: cartPrice * chipPaymentMethod.ratePercent,
        description: 'Chip processing fee',
        category: 'processing_fee',
      });
    }
  }
  // Note: In absorb_fee mode, the organizer still pays the processing fees
  // but they're not shown to the customer in the payment breakdown

  return finalProduct;
}

// Helper function to process payment with Chip
async function processChipPayment(
  chipClient: ChipClient,
  totalAmount: number,
  products: ReturnType<typeof prepareProductsForPayment>,
  userInfo: Awaited<ReturnType<typeof getOrCreateUserInfo>>,
  orderId: string,
  cartId: string,
  chipPaymentMethod: ChipPaymentMethod
) {
  try {
    log.info('Processing Chip payment', { orderId, cartId, totalAmount });

    // Create payment with Chip-In
    const payment = await chipClient.createPayment({
      amount: totalAmount,
      currency: 'MYR',
      products,
      email: userInfo.userEmail,
      fullName: userInfo.userName,
      phone: userInfo.userPhone,
      successUrl: `${process.env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`,
      failureUrl: `${process.env.NEXT_PUBLIC_API_URL}/api/webhooks/chip`,
      successRedirectUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/confirmation/${cartId}?status=success&orderId=${orderId}`,
      failureRedirectUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/confirmation/${cartId}?status=failure`,
      cancelRedirectUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/confirmation/${cartId}?status=cancel`,
      reference: `Payment for Order #${orderId}`,
      notes: `${products[0]?.description || 'Ticket Purchase'} | ${products.length > 1 ? `+${products.length - 1} more items` : ''} | ${userInfo.userName}`,
      paymentMethods: chipPaymentMethod.methods || [],
    });

    log.info('Chip payment created successfully', {
      paymentId: payment.id,
      orderId,
      cartId,
      checkoutUrl: payment.checkout_url,
    });

    // Update order with payment ID
    await database.order.update({
      where: { id: orderId },
      data: {
        transactionId: payment.id,
      },
    });

    return payment;
  } catch (error) {
    log.error('Chip payment error:', { error, orderId, cartId });
    throw new Error('Failed to process payment with Chip-In');
  }
}

// checks & return the custom payment module if custom payment is enabled
async function validateCustomPaymentModule(eventId?: string | null) {
  if (!eventId) {
    throw new Error('EventId is required');
  }

  // fetch event module
  const eventModule = await database.eventModule.findUnique({
    where: {
      eventId,
    },
    select: {
      id: true,
      customPaymentEnabled: true,
      customPaymentModule: {
        select: {
          id: true,
          chipsEnabled: true,
          stripeEnabled: true,
        },
      },
    },
  });

  if (!eventModule) {
    throw new Error('Event module not found');
  }

  // further fetch custom payment module if custom payment is enabled
  if (eventModule.customPaymentEnabled) {
    if (!eventModule.customPaymentModule) {
      throw new Error('Custom payment module not found');
    }

    return eventModule.customPaymentModule;
  }

  return null;
}

/**
 * Main checkout function that processes the entire checkout flow
 * Can either create a new order or continue with an existing one
 * @param _formData Form data from the checkout form
 * @param cartId Cart ID to process
 * @param existingOrderId Optional existing order ID to continue checkout with
 * @returns Object with success status, order ID, and redirect URL if applicable
 */
export async function checkout(
  _formData: CheckoutFormValues,
  cartId: string,
  existingOrderId?: string | null
) {
  // Define variables in the outer scope so they're accessible in the catch block
  let inventoryUpdates: InventoryUpdate[] | undefined;
  let result: Awaited<ReturnType<typeof createOrderTransaction>> | undefined;
  let orderId: string | undefined;
  let eventId: string | undefined;

  try {
    // Step 1: Validate cart
    const cart = await validateCart(cartId);

    // Determine payment methods securely using event configuration and key service
    const hasByok =
      cart.event?.eventModule?.customPaymentEnabled &&
      cart.event.eventModule.customPaymentModule?.chipsEnabled;

    let paymentMethods = await getPaymentMethods(false);
    if (hasByok && cart.eventId) {
      try {
        const { chipSecretKey, chipBrandId } =
          await CustomPaymentKeyService.chipKeys(cart.eventId);
        paymentMethods = await getPaymentMethods(
          true,
          chipSecretKey,
          chipBrandId
        );
      } catch {
        // Fallback to internal default methods if BYOK keys are unavailable
        paymentMethods = await getPaymentMethods(false);
      }
    }
    const selectedChipPaymentMethod = paymentMethods[_formData.paymentMethod];

    // Step 2: Calculate total amount
    const totalAmount = calculateTotalAmount(cart, selectedChipPaymentMethod);

    // Step 3: Get user information
    const userInfo = await getOrCreateUserInfo(_formData);

    // Step 4: Determine is it existing order or new order
    if (existingOrderId) {
      // Step 4.1: Handle existing order flow (e.g: when user cancel/payment failed)
      // Step 4.1.1: Verify the order exists and belongs to this cart
      const existingOrder = await database.order.findUnique({
        where: {
          id: existingOrderId,
          cartId,
          status: OrderStatus.hold, // Only continue with hold orders
        },
      });

      if (!existingOrder) {
        throw new Error('Order not found or is no longer valid');
      }

      if (!existingOrder.eventId) {
        throw new Error('Order Event ID not found');
      }

      // Step 4.1.2: Set orderId for payment processing
      orderId = existingOrderId;
      // Step 4.1.3: Set eventId for custom payment module validation
      eventId = existingOrder.eventId;
    } else {
      // Step 4.2: Handle standard order flow
      // Step 4.2.1: Check for any existing orders for this cart first
      const existingCartOrder = await database.order.findFirst({
        where: {
          cartId,
          status: OrderStatus.hold,
        },
        orderBy: { createdAt: 'desc' },
      });

      if (existingCartOrder) {
        // Found existing order - reuse it instead of creating new one
        log.info(
          'Found existing order for cart, reusing instead of creating new one',
          {
            cartId,
            existingOrderId: existingCartOrder.id,
          }
        );

        orderId = existingCartOrder.id;
        eventId = existingCartOrder.eventId || undefined;

        if (!eventId) {
          throw new Error('Order Event ID not found');
        }
      } else {
        // No existing order - proceed with creating new one
        // Step 4.2.2: Check inventory availability
        inventoryUpdates = await checkInventoryAvailability(cart);

        // Step 4.2.3: Update inventory
        await updateInventory(inventoryUpdates);

        // Step 4.2.4: Create order and tickets in a transaction
        result = await createOrderTransaction(
          cart,
          userInfo,
          totalAmount,
          _formData.paymentMethod,
          _formData.remark || ''
        );

        // Step 4.2.5: Set orderId for payment processing
        orderId = result.order.id;
        if (!result.order.eventId) {
          throw new Error('Order Event ID not found');
        }
        // Step 4.2.6: Set eventId for custom payment module validation
        eventId = result.order.eventId;
      }
    }

    // NOTE Custom Payment flow (overview for Step 5 - 6)
    // Step 1: Check for flag if event is using custom payment
    // Step 2: If flag valid, fetch keys of respective payment option from organizer
    // Step 3: Create a new custom payment client with the custom keys if flag valid else use default chip client
    // Step 4: Process payment with the appropriate chip client
    // NOTE: Custom Payment flow

    // Step 5: Using eventId, fetch custom payment module if event is using custom payment
    const customPaymentModule = await validateCustomPaymentModule(eventId);

    // Step 6: Handle different payment gateway (TODO: e.g. Chips & Stripes)
    if (customPaymentModule?.chipsEnabled) {
      // Step 6.1.1: Initialize with default chip client
      let chipClient: ChipClient = chip;

      if (customPaymentModule?.chipsEnabled) {
        // Step 6.1.2: Fetch custom payment module keys
        const { chipSecretKey, chipBrandId, chipPublicKey } =
          await CustomPaymentKeyService.chipKeys(eventId);

        // Step 6.1.3: Create new chip client with the custom keys
        if (chipSecretKey && chipBrandId && chipPublicKey) {
          const customChipClient = createChipClient({
            secretKey: chipSecretKey,
            brand: chipBrandId,
            publicKey: chipPublicKey,
          });

          // Step 6.1.4: Override default chip client
          chipClient = customChipClient;
        }
      }

      // Step 6.1.5: Prepare products for payment
      const products = prepareProductsForPayment(
        cart,
        selectedChipPaymentMethod
      );

      // Step 6.1.6: Process payment with Chip
      const payment = await processChipPayment(
        chipClient, // pass the client into processChipPayment
        totalAmount,
        products,
        userInfo,
        orderId,
        cartId,
        selectedChipPaymentMethod
      );

      // Step 6.1.7: Return checkout URL instead of redirecting directly
      if (payment.checkout_url) {
        log.info('Returning Chip-In checkout URL', {
          checkoutUrl: payment.checkout_url,
          orderId,
          cartId,
          isExistingOrder: !!existingOrderId,
          paymentMethod: selectedChipPaymentMethod,
        });

        return {
          success: true,
          orderId,
          redirectUrl: payment.checkout_url,
        };
      }
    } else if (customPaymentModule?.stripeEnabled) {
      // Step 6.2: For future implementation of Stripe payment
      throw new Error('Stripe payment gateway not implemented yet');
    } else {
      // Step 6.3: defaults to chips
      // Step 6.3.1: Prepare products for payment
      const products = prepareProductsForPayment(
        cart,
        selectedChipPaymentMethod
      );
      // Step 6.3.2: Process payment with Chip
      const payment = await processChipPayment(
        chip, // default chip
        totalAmount,
        products,
        userInfo,
        orderId,
        cartId,
        selectedChipPaymentMethod
      );

      // Step 6.3.3: Return checkout URL instead of redirecting directly
      if (payment.checkout_url) {
        log.info('Returning Chip-In checkout URL', {
          checkoutUrl: payment.checkout_url,
          orderId,
          cartId,
          isExistingOrder: !!existingOrderId,
        });

        return {
          success: true,
          orderId,
          redirectUrl: payment.checkout_url,
        };
      }
    }

    // Step 6.4: This code will only execute if no redirect URL was returned
    return {
      success: true,
      orderId,
    };
  } catch (error) {
    log.error('Checkout error:', { error, cartId, existingOrderId, eventId });

    // Handle unique constraint error for cart_id - this means an order already exists for this cart
    if (
      error instanceof Error &&
      error.message.includes(
        'Unique constraint failed on the fields: (`cart_id`)'
      )
    ) {
      log.warn(
        'Unique constraint error on cart_id, looking for existing order',
        {
          cartId,
        }
      );

      try {
        // Find the existing order and retry with it
        const existingOrder = await database.order.findFirst({
          where: {
            cartId,
            status: OrderStatus.hold,
          },
          orderBy: { createdAt: 'desc' },
        });

        if (existingOrder) {
          log.info(
            'Found existing order after constraint error, retrying checkout',
            {
              cartId,
              existingOrderId: existingOrder.id,
            }
          );

          // Recursive call with the existing order ID
          return await checkout(_formData, cartId, existingOrder.id);
        }
      } catch (retryError) {
        log.error('Error during retry after constraint violation', {
          retryError,
          cartId,
        });
      }
    }

    // Step 7: If inventory was updated for a new order, we should try to restore it
    try {
      if (inventoryUpdates && !existingOrderId) {
        await restoreInventory(inventoryUpdates);
        log.info('Inventory restored after checkout failure', { cartId });
      }
    } catch (restoreError) {
      log.error('Failed to restore inventory:', {
        error: restoreError,
        cartId,
      });
    }

    throw new Error(
      `Checkout failed: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
