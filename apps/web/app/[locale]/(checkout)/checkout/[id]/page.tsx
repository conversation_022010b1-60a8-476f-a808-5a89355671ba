// import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { getCart } from '@/app/[locale]/(cart)/actions';
import {
  getExistingOrderInfo,
  updateExpiredCartStatus,
} from '@/app/[locale]/(checkout)/checkout/[id]/actions';
import { CartStatus } from '@repo/database/types';
import CustomPaymentKeyService from '@repo/database/payment-keys';
import type { ReactElement } from 'react';
import { CartError } from '../components/cart-error';
import { CheckoutForm } from '../components/checkout-form';
import { CheckoutTimer } from '../components/checkout-timer';
import { getPaymentMethods } from '../utils/constant';

type PageProps = {
  params: Promise<{ locale: string; id: string }>;
  // searchParams: { [key: string]: string | string[] | undefined };
};

export default async function CheckoutPage({
  params,
  // searchParams,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;

  if (!id) {
    return <CartError error="Invalid cart" />;
  }

  // fetch IDLE or ACTIVE cart with the same id only
  const cart = await getCart(id, true);

  // client side loading
  // if (cart is loading) {
  //   return (
  //     <div className="flex min-h-[500px] w-full flex-col items-center justify-start gap-8 bg-background p-4">
  //       <div className="w-full max-w-md space-y-8">
  //         <Skeleton className="h-8 w-48" />

  //         <div className="flex items-start space-x-4 rounded-lg bg-muted p-4">
  //           <Skeleton className="h-24 w-24 flex-shrink-0 rounded-lg" />
  //           <div className="flex-1 space-y-4">
  //             <Skeleton className="h-6 w-3/4" />
  //             <div className="space-y-2">
  //               <Skeleton className="h-4 w-40" />
  //               <Skeleton className="h-4 w-32" />
  //             </div>
  //           </div>
  //         </div>

  //         <div className="space-y-4 pt-6">
  //           <div className="flex items-center justify-between">
  //             <Skeleton className="h-4 w-32" />
  //             <Skeleton className="h-4 w-24" />
  //           </div>

  //           <div className="flex items-center justify-between">
  //             <Skeleton className="h-4 w-24" />
  //             <Skeleton className="h-4 w-24" />
  //           </div>

  //           <div className="flex items-center justify-between">
  //             <div className="flex items-center gap-2">
  //               <Skeleton className="h-4 w-32" />
  //               <Skeleton className="h-4 w-4 rounded-full" />
  //             </div>
  //             <Skeleton className="h-4 w-24" />
  //           </div>

  //           <div className="flex items-center justify-between pt-4">
  //             <Skeleton className="h-6 w-20" />
  //             <Skeleton className="h-6 w-28" />
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  if (!cart) {
    return <CartError error="Cart not found" />;
  }

  // Check if cart has expired based on expiresAt date
  if (
    cart.status === CartStatus.expired ||
    new Date(cart.expiresAt) < new Date()
  ) {
    // If cart is expired but status is not updated yet, update it
    if (cart.status !== CartStatus.expired) {
      await updateExpiredCartStatus(id);
    }

    return (
      <CartError error="Cart has expired. The items you selected are no longer reserved." />
    );
  }

  // Check if there's an existing order in 'hold' status for this cart
  // This would happen if the user previously started checkout but payment failed
  const { existingUserInfo, existingOrderId } = await getExistingOrderInfo(id);

  // Server-side: determine available payment methods securely
  const hasByok =
    cart.event?.eventModule?.customPaymentEnabled &&
    cart.event.eventModule.customPaymentModule?.chipsEnabled;

  let paymentMethods = await getPaymentMethods(false);
  try {
    if (hasByok && cart.event?.id) {
      const { chipSecretKey, chipBrandId } =
        await CustomPaymentKeyService.chipKeys(cart.event.id);
      paymentMethods = await getPaymentMethods(
        true,
        chipSecretKey,
        chipBrandId
      );
    }
  } catch {
    // fallback to default internal rates
    paymentMethods = await getPaymentMethods(false);
  }

  return (
    <>
      <CheckoutTimer cart={cart} />
      <CheckoutForm
        cart={cart}
        existingUserInfo={existingUserInfo}
        existingOrderId={existingOrderId}
        paymentMethods={paymentMethods}
      />
    </>
  );
}
