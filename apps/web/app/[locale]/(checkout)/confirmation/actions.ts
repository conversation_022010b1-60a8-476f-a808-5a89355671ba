'use server';

import { database } from '@repo/database';
import {
  CartStatus,
  OrderStatus,
  PaymentStatus,
  TicketStatus,
} from '@repo/database/types';
import { log } from '@repo/observability/log';

/**
 * This function marks orders as cancelled & cart as abandoned
 * Then restores the inventory quantity
 */
export async function postCheckoutCleanUp(
  cartId: string,
  orderId: string | null
) {
  if (!orderId) {
    return;
  }

  try {
    // find the cart associated with the order
    // since we can retrive user from cart
    const cart = await database.cart.findUnique({
      where: { id: cartId, status: CartStatus.converted },
      include: { user: true },
    });

    if (!cart || !cart.userId) {
      log.info(`no cart found ${cartId}`, {
        cartId,
      });

      return;
    }

    // Find orders that might be related to this user and still in 'hold' status
    const holdOrders = await database.order.findMany({
      where: {
        status: OrderStatus.hold,
        paymentStatus: PaymentStatus.pending,
        userId: cart.userId, // user from cart
      },
      include: { user: true },
    });

    if (holdOrders.length > 0) {
      log.info(
        `Found ${holdOrders.length} orders in hold status for cart ${cartId}`,
        {
          orderIds: holdOrders.map((o) => o.id),
        }
      );

      // Mark these orders as voided since payment was successful for a different order
      for (const order of holdOrders) {
        if (order.id !== orderId) {
          // update cart with the same email to abandoned
          await database.cart.updateMany({
            where: {
              userId: order.userId,
              NOT: { status: CartStatus.converted }, // dont update converted cart
            },
            data: { status: CartStatus.abandoned },
          });
          log.info(`Updated abandoned carts of ${order.userId} to abandoned`, {
            userId: order.userId,
          });

          // Don't update the successful order
          await database.order.update({
            where: { id: order.id },
            data: {
              status: OrderStatus.void,
              paymentStatus: PaymentStatus.cancelled,
            },
          });
          log.info(`Updated order ${order.id} status to void`, {
            orderId: order.id,
          });

          // update ticket status to void
          const affectedTickets = await database.ticket.updateManyAndReturn({
            where: { orderId: order.id },
            data: { status: TicketStatus.void },
          });
          log.info(`Updated tickets for order ${order.id} to void`, {
            orderId: order.id,
          });

          // Restore inventory for voided tickets
          if (affectedTickets.length > 0) {
            // Group tickets by ticketType and timeSlot for inventory restoration
            const ticketCounts: Record<string, number> = {};

            // Count tickets by ticketType and timeSlot combination
            for (const ticket of affectedTickets) {
              const key = `${ticket.ticketTypeId}|${ticket.timeSlotId}`;
              ticketCounts[key] = (ticketCounts[key] || 0) + 1;
            }

            // Update inventory for each combination
            for (const [key, count] of Object.entries(ticketCounts)) {
              const [ticketTypeId, timeSlotId] = key.split('|');

              // Find the inventory for this combination
              const inventory = await database.inventory.findFirst({
                where: {
                  ticketTypeId,
                  timeSlotId,
                },
              });

              if (inventory) {
                // Increment inventory by the number of voided tickets
                await database.inventory.update({
                  where: { id: inventory.id },
                  data: {
                    quantity: {
                      increment: count,
                    },
                  },
                });

                log.info(`Restored ${count} tickets to inventory`, {
                  inventoryId: inventory.id,
                  ticketTypeId,
                  timeSlotId,
                  count,
                });
              } else {
                log.warn(
                  `Inventory not found for ticket type ${ticketTypeId} and time slot ${timeSlotId}`
                );
              }
            }
          }
        }
      }
    }
  } catch (error) {
    log.error('Error during postCheckoutCleanUp', { error, cartId, orderId });
  }
}

/**
 * Check the current status of an order
 * This is used to poll for payment confirmation from the webhook
 */
export async function checkOrderStatus(orderId: string | null) {
  if (!orderId) {
    return { status: 'unknown', paymentStatus: 'unknown', eventType: null };
  }

  try {
    const order = await database.order.findUnique({
      where: { id: orderId },
      select: {
        status: true,
        paymentStatus: true,
        event: {
          select: {
            eventType: true,
          },
        },
      },
    });

    if (!order) {
      log.warn(`Order not found when checking status: ${orderId}`);
      return { status: 'not_found', paymentStatus: 'unknown', eventType: null };
    }

    return {
      status: order.status,
      paymentStatus: order.paymentStatus,
      eventType: order.event?.eventType || null,
    };
  } catch (error) {
    log.error('Error checking order status', { error, orderId });
    return { status: 'error', paymentStatus: 'error', eventType: null };
  }
}

/**
 * Get order information from cart ID and search params
 * This is used to pre-fetch order status on the server side
 */
export async function getOrderInfoFromCart(
  cartId: string,
  searchParams: { [key: string]: string | string[] | undefined }
) {
  try {
    // Get orderId from search params
    const orderId =
      typeof searchParams.orderId === 'string' ? searchParams.orderId : null;
    const status =
      typeof searchParams.status === 'string' ? searchParams.status : 'pending';

    // If we don't have an orderId or the status is not 'success', return early
    if (!orderId || status !== 'success') {
      return {
        orderId,
        status,
        orderStatus: null,
        paymentStatus: null,
        initialPaymentState: 'processing',
      };
    }

    // Check if the order exists and get its status
    const orderStatus = await checkOrderStatus(orderId);

    // Determine the initial payment state based on the order status
    let initialPaymentState = 'processing';

    if (
      orderStatus.status === 'completed' &&
      orderStatus.paymentStatus === 'paid'
    ) {
      initialPaymentState = 'confirmed';
    } else if (
      orderStatus.status === 'cancelled' ||
      orderStatus.paymentStatus === 'cancelled' ||
      orderStatus.paymentStatus === 'refunded'
    ) {
      initialPaymentState = 'failed';
    }

    return {
      orderId,
      status,
      orderStatus: orderStatus.status,
      paymentStatus: orderStatus.paymentStatus,
      initialPaymentState,
    };
  } catch (error) {
    log.error('Error getting order info from cart', { error, cartId });
    return {
      orderId: null,
      status: 'error',
      orderStatus: null,
      paymentStatus: null,
      initialPaymentState: 'processing',
    };
  }
}
