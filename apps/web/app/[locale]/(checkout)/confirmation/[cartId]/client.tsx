'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useInterval } from 'usehooks-ts';
import { checkOrderStatus, postCheckoutCleanUp } from '../actions';

type PaymentStatus = 'success' | 'failure' | 'cancel' | 'pending';

// Function to clear checkout form data from localStorage (client-side)
const clearCheckoutFormData = (cartId: string) => {
  try {
    localStorage.removeItem(`checkout_form_${cartId}`);
  } catch (error) {
    console.error('Error clearing checkout form data:', error);
  }
};

// Confirmation dialog component for abandoning checkout
interface AbandonCheckoutDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function AbandonCheckoutDialog({
  open,
  onOpenChange,
}: AbandonCheckoutDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Abandon checkout process?</AlertDialogTitle>
          <AlertDialogDescription>
            Your tickets spot will no longer be reserved. You'll need to start
            the checkout process again if you want to purchase these tickets.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Link href="/events">Continue</Link>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Success component
function SuccessContent({
  cartId,
  initialOrderId,
  initialPaymentState,
}: {
  cartId: string;
  initialOrderId: string | null;
  initialPaymentState: string;
}) {
  const searchParams = useSearchParams();
  // Use the server-provided orderId as fallback if client-side params are not available
  const orderId = searchParams.get('orderId') || initialOrderId;
  // Start with the server-provided payment state
  const [paymentStatus, setPaymentStatus] =
    useState<string>(initialPaymentState);
  const [pollingCount, setPollingCount] = useState(0);
  const [eventType, setEventType] = useState<string | null>(null);

  // abandon checkout dialog state
  const [showDialog, setShowDialog] = useState(false);

  // Determine if we should poll based on order ID and payment state
  const shouldPoll =
    orderId &&
    initialPaymentState !== 'confirmed' &&
    initialPaymentState !== 'failed' &&
    pollingCount < 10;

  // Function to check order status
  const checkStatus = async () => {
    if (!orderId) {
      return;
    }

    try {
      // Check the current order status
      const result = await checkOrderStatus(orderId);

      // If the payment is success (completed and paid)
      if (result.status === 'completed' && result.paymentStatus === 'paid') {
        // Payment is confirmed - update UI and clear form data
        setPaymentStatus('confirmed');
        setEventType(result.eventType);
        clearCheckoutFormData(cartId);

        // post checkout cleaning up
        await postCheckoutCleanUp(cartId, orderId);
      }
      // If the payment has failed or been cancelled
      else if (
        result.status === 'cancelled' ||
        result.paymentStatus === 'cancelled' ||
        result.paymentStatus === 'refunded'
      ) {
        // Payment failed - update UI but don't clear form data
        setPaymentStatus('failed');
      }

      // Increment polling count
      setPollingCount((prev: number) => prev + 1);
    } catch (error) {
      console.error('Error polling for order status:', error);
      // Continue polling on error
    }
  };

  // Use the useInterval hook for polling
  useInterval(checkStatus, shouldPoll ? 3000 : null);

  // Show different UI based on payment status
  if (paymentStatus === 'processing') {
    return (
      <>
        <CardHeader className="flex flex-col items-center space-y-2 text-center">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <CardTitle className="text-2xl">Processing Payment</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground">
            Your payment is being processed. This may take a moment. Please do
            not close this page.
          </p>
          <div className="mt-4 rounded-md bg-muted p-4">
            <p className="font-medium">Order Number: {orderId}</p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <p className="caption text-center">
            You'll receive a confirmation email once your payment is processed.
          </p>
        </CardFooter>
      </>
    );
  }

  if (paymentStatus === 'failed') {
    return (
      <>
        <AbandonCheckoutDialog open={showDialog} onOpenChange={setShowDialog} />

        <CardHeader className="flex flex-col items-center space-y-2 text-center">
          <XCircle className="h-16 w-16 text-red-500" />
          <CardTitle className="text-2xl">Payment Processing Failed</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-muted-foreground">
            There was an issue processing your payment. Your order has not been
            completed.
          </p>
          <div className="mt-4 rounded-md bg-muted p-4">
            <p className="font-medium">Order Number: {orderId}</p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button asChild className="w-full">
            <Link href={`/checkout/${cartId}`}>Return to Checkout</Link>
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => setShowDialog(true)}
          >
            Return to Home
          </Button>
        </CardFooter>
      </>
    );
  }

  // Default: confirmed payment
  return (
    <>
      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <CheckCircle className="h-16 w-16 text-green-500" />
        <CardTitle className="text-2xl">Payment Successful</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          {eventType === 'ngo'
            ? 'Your donation is confirmed! You will receive your registration details via email shortly.'
            : 'Your order has been completed successfully. You will receive a confirmation email shortly.'}
        </p>
        <div className="mt-4 rounded-md bg-muted p-4">
          <p className="font-medium">Order Number: {orderId}</p>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button variant="outline" asChild className="w-full">
          <Link href="/events">Return to Home</Link>
        </Button>
      </CardFooter>
    </>
  );
}

// Failure component
function FailureContent({ cartId }: { cartId: string }) {
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <AbandonCheckoutDialog open={showDialog} onOpenChange={setShowDialog} />

      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <XCircle className="h-16 w-16 text-red-500" />
        <CardTitle className="text-2xl">Payment Failed</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          We couldn't process your payment. Please try again or use a different
          payment method.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/checkout/${cartId}`}>Return to Checkout</Link>
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => setShowDialog(true)}
        >
          Return to Home
        </Button>
      </CardFooter>
    </>
  );
}

// Cancel component
function CancelContent({ cartId }: { cartId: string }) {
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <AbandonCheckoutDialog open={showDialog} onOpenChange={setShowDialog} />

      <CardHeader className="flex flex-col items-center space-y-2 text-center">
        <AlertCircle className="h-16 w-16 text-amber-500" />
        <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <p className="text-muted-foreground">
          Your payment was cancelled. Your cart items are still saved if you'd
          like to complete your purchase.
        </p>
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button asChild className="w-full">
          <Link href={`/checkout/${cartId}`}>Return to Checkout</Link>
        </Button>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => setShowDialog(true)}
        >
          Return to Home
        </Button>
      </CardFooter>
    </>
  );
}

interface ConfirmationClientProps {
  cartId: string;
  orderId: string | null;
  initialStatus: string;
  initialPaymentState: string;
}

export function ConfirmationClient({
  cartId,
  orderId,
  initialStatus,
  initialPaymentState,
}: ConfirmationClientProps) {
  const searchParams = useSearchParams();
  // Use the server-provided status as fallback if client-side params are not available
  const status = (searchParams.get('status') as PaymentStatus) || initialStatus;

  return (
    <div className="container flex w-full justify-center py-12">
      <Card className="max-w-md">
        {status === 'success' && (
          <SuccessContent
            cartId={cartId}
            initialOrderId={orderId}
            initialPaymentState={initialPaymentState}
          />
        )}
        {status === 'failure' && <FailureContent cartId={cartId} />}
        {status === 'cancel' && <CancelContent cartId={cartId} />}
      </Card>
    </div>
  );
}
