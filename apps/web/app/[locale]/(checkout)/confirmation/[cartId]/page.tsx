import { getOrderInfoFromCart } from '../actions';
import { ConfirmationClient } from './client';

export default async function ConfirmationPage({
  params,
  searchParams,
}: {
  params: Promise<{ cartId: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // Get the cart ID from params
  const { cartId } = await params;

  // Pre-fetch order information on the server side
  const orderInfo = await getOrderInfoFromCart(cartId, await searchParams);

  // Pass the cartId and pre-fetched order info to the client component
  return (
    <ConfirmationClient
      cartId={cartId}
      orderId={orderInfo.orderId}
      initialStatus={orderInfo.status}
      initialPaymentState={orderInfo.initialPaymentState}
    />
  );
}
