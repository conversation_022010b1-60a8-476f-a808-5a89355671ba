import LegalContent from '@/app/[locale]/legal/components/legal-content';
import { legalData } from '@/app/[locale]/legal/legal-data';
import type { Metadata } from 'next';

type PageProps = {
  readonly params: Promise<{
    slug: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const slug = (await params).slug;

  // Get the title from legal data based on slug
  const pageData = legalData[slug as keyof typeof legalData];
  const title = pageData?.title || 'Legal Information';

  const siteUrl =
    process.env.NEXT_PUBLIC_WEB_URL ||
    process.env.NEXT_PUBLIC_SITE_URL ||
    'https://ticketcare-web.vercel.app/';

  const canonicalUrl = `${siteUrl}/legal/${slug}`;
  // const defaultImage = '/images/default-legal.jpg';
  // const ogImageUrl = new URL(`${siteUrl}${defaultImage}`);

  return {
    title: `${title} | TicketCARE`,
    description: `TicketCARE ${title.toLowerCase()} - Important information about our ticketing platform.`,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: `${title} | TicketCARE`,
      description: `TicketCARE ${title.toLowerCase()} - Important information about our ticketing platform.`,
      url: canonicalUrl,
      siteName: 'TicketCARE',
      // images: [
      //   {
      //     url: ogImageUrl.toString(),
      //     width: 1200,
      //     height: 630,
      //     alt: `TicketCARE ${title}`,
      //   },
      // ],
    },
  };
}

export default function LegalDetailPage() {
  return (
    <div className="container">
      <LegalContent />
    </div>
  );
}
