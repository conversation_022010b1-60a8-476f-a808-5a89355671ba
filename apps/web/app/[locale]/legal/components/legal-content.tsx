'use client';

import { cn, slugify } from '@repo/design-system/lib/utils';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import type React from 'react';
import { Fragment, useEffect, useRef, useState } from 'react';
import { legalData } from '../legal-data';

type LegalSectionKey = keyof typeof legalData;
type ContentItem = { subtitle?: string; description: React.ReactNode };

export default function LegalContent() {
  const params = useParams();
  const { slug } = params;

  const [activeHeadings, setActiveHeadings] = useState<string[]>([]);
  const [currentSection, setCurrentSection] = useState<LegalSectionKey>(
    slug as LegalSectionKey
  );
  const sectionRefs = useRef<Record<string, HTMLElement>>({});

  useEffect(() => {
    const sections = Object.keys(sectionRefs.current);

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      for (const entry of entries) {
        const id = entry.target.id;

        if (entry.isIntersecting) {
          setActiveHeadings((prev) =>
            prev.includes(id) ? prev : [...prev, id]
          );
        } else {
          setActiveHeadings((prev) => prev.filter((heading) => heading !== id));
        }
      }
    };

    const observer = new IntersectionObserver(observerCallback, {
      root: null,
      rootMargin: '-100px 0px -300px 0px',
      threshold: 0.1,
    });

    for (const sectionId of sections) {
      const element = sectionRefs.current[sectionId];
      if (element) {
        observer.observe(element);
      }
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const addSectionRef = (id: string, ref: HTMLElement | null) => {
    if (ref) {
      sectionRefs.current[id] = ref;
    }
  };

  // Get the current section data
  const sectionData = legalData[currentSection];

  return (
    <section>
      <h1 className="text-balance font-bold hero-text">
        All the important legal and security stuff, right here.
      </h1>
      <p className="mt-5 max-w-2xl text-balance text-xl font-medium">
        Glad you're here! Let&apos;s cover the key details—how Ticketcare works,
        what you can expect, and how we protect you.
      </p>
      <div className="relative mt-16 grid gap-10 lg:mt-28 lg:grid-cols-5">
        <aside className="top-20 flex h-fit w-full max-w-56 flex-col gap-5 lg:sticky">
          <div className="space-y-2">
            {Object.entries(legalData).map(([key, section]) => (
              <Link
                key={key}
                href={`/legal/${key}`}
                className={cn(
                  'block hover:underline',
                  currentSection === key ? 'font-bold' : ''
                )}
                onClick={() => setCurrentSection(key as LegalSectionKey)}
              >
                <p className="caption">{section.title}</p>
              </Link>
            ))}
          </div>
        </aside>

        <div className="gap-6 lg:col-span-3">
          <div className="max-w-prose lg:mx-auto">
            <div className="prose dark:prose-invert prose-h3:mt-14 prose-h3:scroll-mt-20 prose-h3:text-lg prose-li:text-sm">
              <h2 className="text-4xl font-semibold" id={currentSection}>
                {sectionData.title}
              </h2>

              {sectionData.content.map((item: ContentItem, index) => {
                if (!item.subtitle) {
                  return <Fragment key={index}>{item.description}</Fragment>;
                }

                const headingId = slugify(item.subtitle);

                return (
                  <div key={headingId}>
                    {item.subtitle && (
                      <h3
                        id={headingId}
                        ref={(ref) => addSectionRef(headingId, ref)}
                        className="text-2xl font-semibold"
                      >
                        {item.subtitle}
                      </h3>
                    )}
                    {item.description}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <nav className="sticky top-20 hidden h-fit lg:block">
          <p className="text-muted-foreground text-sm">ON THIS PAGE</p>
          <ul className="text-muted-foreground mt-1.5 text-xs">
            {sectionData.content.map((item: ContentItem) => {
              if (!item.subtitle) return null;
              const headingId = slugify(item.subtitle);

              return (
                <li key={headingId}>
                  <a
                    className={cn(
                      'border-border block border-l py-1 pl-2.5 transition-colors duration-200',
                      activeHeadings.includes(headingId)
                        ? 'border-primary text-primary font-medium'
                        : 'text-muted-foreground hover:text-primary'
                    )}
                    href={`#${headingId}`}
                  >
                    {item.subtitle}
                  </a>
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
    </section>
  );
}
