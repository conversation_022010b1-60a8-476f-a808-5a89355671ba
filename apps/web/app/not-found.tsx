import '@repo/design-system/styles/globals.css';
import { Button } from '@repo/design-system/components/ui/button';
import { DesignSystemProvider } from '@repo/design-system';
import { fonts } from '@repo/design-system/lib/fonts';
import { cn } from '@repo/design-system/lib/utils';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Uncharted Territory - TicketCARE',
  description: "You've ventured into unexplored digital waters. Let's get you back on track!",
  icons: {
    icon: '/icon.ico',
    shortcut: '/favicon.ico',
    apple: '/apple-icon.png',
  },
};

export default function NotFoundPage() {
  return (
    <html lang="en" className={cn(fonts, 'scroll-smooth')} suppressHydrationWarning>
      <body>
        <DesignSystemProvider>
          <div className="flex min-h-[70vh] flex-col items-center justify-center text-center">
            <div className="space-y-6">
              <div className="inline-block rounded-full bg-primary-red/10 p-3">
                <div className="rounded-full bg-primary-red/20 p-2">
                  <div className="rounded-full bg-primary-red p-2">
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      height="24"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      width="24"
                      xmlns="http://www.w3.org/2000/svg"
                      aria-labelledby="notFoundIcon"
                    >
                      <title id="notFoundIcon">Page Not Found Icon</title>
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                      <path d="M15 9l-6 6" />
                      <path d="M9 9l6 6" />
                    </svg>
                  </div>
                </div>
              </div>
              <h1 className="text-4xl font-bold tracking-tighter hero-text">
                Oops! You've Ventured Too Far
              </h1>
              <p className="mx-auto max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed">
                Looks like you've wandered into uncharted territory! 
                <br/>
                Even our best explorers haven't mapped this page yet.
              </p>
              <Button asChild className="mt-4">
                <Link href="/" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Home
                </Link>
              </Button>
            </div>
          </div>
        </DesignSystemProvider>
      </body>
    </html>
  );
}
