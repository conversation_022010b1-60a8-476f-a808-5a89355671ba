import { keys as database } from '@repo/database/keys';
import { keys as email } from '@repo/email/keys';
import { keys as flags } from '@repo/feature-flags/keys';
import { keys as core } from '@repo/next-config/keys';
import { keys as observability } from '@repo/observability/keys';
import { keys as rateLimit } from '@repo/rate-limit/keys';
import { keys as security } from '@repo/security/keys';
import { keys as storage } from '@repo/storage/keys';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  extends: [
    core(),
    email(),
    observability(),
    flags(),
    security(),
    rateLimit(),
    database(),
    storage(),
  ],
  server: {},
  client: {
    NEXT_PUBLIC_CHIP_FPX_RATE: z.string().optional(),
    NEXT_PUBLIC_CHIP_CARD_RATE: z.string().optional(),
    NEXT_PUBLIC_CHIP_DUITNOW_RATE: z.string().optional(),
    NEXT_PUBLIC_CHIP_EWALLET_RATE: z.string().optional(),
  },
  runtimeEnv: {
    NEXT_PUBLIC_CHIP_FPX_RATE: process.env.NEXT_PUBLIC_CHIP_FPX_RATE,
    NEXT_PUBLIC_CHIP_CARD_RATE: process.env.NEXT_PUBLIC_CHIP_CARD_RATE,
    NEXT_PUBLIC_CHIP_DUITNOW_RATE: process.env.NEXT_PUBLIC_CHIP_DUITNOW_RATE,
    NEXT_PUBLIC_CHIP_EWALLET_RATE: process.env.NEXT_PUBLIC_CHIP_EWALLET_RATE,
  },
});
