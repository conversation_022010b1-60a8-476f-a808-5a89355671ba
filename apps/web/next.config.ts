import { env } from '@/env';
import { config, withAnalyzer } from '@repo/next-config';
import { withLogging, withSentry } from '@repo/observability/next-config';
import type { NextConfig } from 'next';

// Start with the base config
let nextConfig: NextConfig = withLogging(config);

// Add configuration for Prisma as an external package
nextConfig.serverExternalPackages = ['@prisma/client'];

nextConfig.images?.remotePatterns?.push(
  {
    protocol: 'https',
    hostname: 'assets.basehub.com',
  },
  {
    protocol: 'https',
    hostname: 'media.360hq.my',
  }
);

if (process.env.NODE_ENV === 'production') {
  const redirects: NextConfig['redirects'] = async () => [
    {
      source: '/legal',
      destination: '/legal/privacy',
      statusCode: 301,
    },
  ];

  nextConfig.redirects = redirects;
}

if (env.VERCEL) {
  nextConfig = withSentry(nextConfig);
}

if (env.ANALYZE === 'true') {
  nextConfig = withAnalyzer(nextConfig);
}

export default nextConfig;
