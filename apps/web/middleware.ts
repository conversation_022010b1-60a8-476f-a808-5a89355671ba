import { env } from '@/env';
import {
  internationalizationMiddleware,
  type I18nNextRequest,
} from '@repo/internationalization/middleware';
import { parseError } from '@repo/observability/error';
import { secure } from '@repo/security';
import {
  noseconeMiddleware,
  noseconeOptions,
  noseconeOptionsWithToolbar,
} from '@repo/security/middleware';
import { auth } from '@repo/auth/server';
import { NextResponse, type NextRequest } from 'next/server';

export const config = {
  // matcher tells Next.js which routes to run the middleware on. This runs the
  // middleware on all routes except for static assets, API routes, and Posthog ingest
  // Also specifically includes auth and account routes for authentication handling
  matcher: [
    '/((?!_next/static|_next/image|api|ingest|favicon.ico).*)',
    '/account/:path*',
    '/auth/:path*',
  ],
};

const securityHeaders = env.FLAGS_SECRET
  ? noseconeMiddleware(noseconeOptionsWithToolbar)
  : noseconeMiddleware(noseconeOptions);

const UNREADY_ROUTES = [
  '/features/event-ticketing',
  '/features/event-marketing-and-promotion',
  '/features/event-management',
  '/features/ticket-scanning',
  '/features/reporting-analytics',
  '/features/modules-integrations',
  '/first-timers',
  '/event-pros',
  '/free-events-ticketing',
  '/community',
  '/charities',
  '/tours-and-attractions',
  '/festival-ticketing',
  '/workshop-ticketing',
  '/conference-ticketing',
  '/ticketing-for-school',
  '/venue-ticketing',
  '/industries',
  '/apps',
  '/about',
  '/partner-program',
  '/modules/impactspark',
  '/modules/seatsmart',
  '/modules/checkoutflow',
  '/modules/complysync',
  '/modules/reachstudio',
  '/modules/crowdconnect',
  '/modules/fankeeper',
  '/modules/passport',
  '/modules/gearlounge',
  '/modules/innercircle',
];

/**
 * Custom authentication middleware
 * Handles redirects for protected routes and authenticated users
 * Also enforces customer-only access (redirects organizers to app)
 */
const handleAuth = async (request: NextRequest) => {
  const pathname = request.nextUrl.pathname;

  // Manually read the session cookie
  const cookieHeader = request.headers.get('cookie') || '';
  const cookies = cookieHeader.split(';').reduce(
    (acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    },
    {} as Record<string, string>
  );

  // Check for the better-auth.session_token cookie
  const hasSessionCookie = !!cookies['better-auth.session_token'];

  // If user has a session, check their role
  if (hasSessionCookie) {
    try {
      const session = await auth.api.getSession({
        headers: request.headers,
      });

      // If user is an organizer or has organizer access, redirect to app
      if (
        session?.user &&
        (session.session?.organizerId ||
          session.user.role === 'super-admin' ||
          session.user.role === 'admin' ||
          session.user.role !== 'customer')
      ) {
        const appUrl = env.NEXT_PUBLIC_APP_URL || 'https://app.ticketcare.my';
        return NextResponse.redirect(new URL('/', appUrl));
      }
    } catch (error) {
      console.error('Error checking user role in web middleware:', error);
      // Continue with normal auth flow if there's an error
    }
  }

  // Redirect to login if accessing /account without being logged in
  if (!hasSessionCookie && pathname.includes('/account')) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  // Redirect to account if accessing /auth while logged in
  if (hasSessionCookie && pathname.includes('/auth')) {
    return NextResponse.redirect(new URL('/account', request.url));
  }

  return null;
};

/**
 * Main middleware function that combines all middleware functionality
 */
const middleware = async (request: NextRequest) => {
  // Check authentication first
  const authResponse = handleAuth(request);
  if (authResponse) return authResponse;

  // Continue with other middleware functionality
  const pathname = request.nextUrl.pathname;

  // Check if the path is exactly /legal and redirect to /legal/info-for-ticket-buyers
  if (pathname === '/legal' || pathname.endsWith('/legal')) {
    const url = request.nextUrl.clone();
    if (pathname === '/legal') {
      url.pathname = '/legal/info-for-ticket-buyers';
    } else {
      // For localized routes like /en/legal
      const parts = pathname.split('/');
      parts.pop(); // Remove 'legal'
      url.pathname = [...parts, 'legal', 'info-for-ticket-buyers'].join('/');
    }
    return NextResponse.redirect(url);
  }

  // Check if the current path is in the list of unready routes
  const isUnreadyRoute = UNREADY_ROUTES.some((route) => {
    // Check for exact match or localized version (e.g., /en/features/event-ticketing)
    return pathname === route || pathname.endsWith(route);
  });
  if (isUnreadyRoute) {
    // Redirect to coming-soon page
    return NextResponse.redirect(new URL('/coming-soon', request.url));
  }

  // Cast the request through unknown to bypass strict type checking
  const i18nResponse = internationalizationMiddleware(
    request as unknown as I18nNextRequest
  );
  if (i18nResponse) {
    return i18nResponse;
  }

  if (!env.ARCJET_KEY) {
    return securityHeaders();
  }

  try {
    await secure(
      [
        // See https://docs.arcjet.com/bot-protection/identifying-bots
        'CATEGORY:SEARCH_ENGINE', // Allow search engines
        'CATEGORY:PREVIEW', // Allow preview links to show OG images
        'CATEGORY:MONITOR', // Allow uptime monitoring services
      ],
      request
    );

    return securityHeaders();
  } catch (error) {
    const message = parseError(error);

    return NextResponse.json({ error: message }, { status: 403 });
  }
};

export default middleware;
