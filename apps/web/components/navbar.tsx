import integrationIcon from '@/app/assets/navbar/integration.svg';
import managingIcon from '@/app/assets/navbar/managing.svg';
import overviewIcon from '@/app/assets/navbar/overview.svg';
import promotingIcon from '@/app/assets/navbar/promoting.svg';
import reportingIcon from '@/app/assets/navbar/reporting.svg';
import scanningIcon from '@/app/assets/navbar/scanning.svg';
import ticketingIcon from '@/app/assets/navbar/ticketing.svg';
import { env } from '@/env';
import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import { ModeToggle } from '@repo/design-system/components/mode-toggle';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/design-system/components/ui/accordion';
import { Button } from '@repo/design-system/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@repo/design-system/components/ui/navigation-menu';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@repo/design-system/components/ui/sheet';
import { Menu } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import type { ReactNode } from 'react';

interface TwoColumnMenuItem {
  title: string;
  subtitle?: string;
  url?: string;
  isHeader?: boolean;
  isBold?: boolean;
}

interface MenuItem {
  title: string;
  url: string;
  icon?: ReactNode;
  description?: string;
  items?: MenuItem[];
  twoColumnItems?: TwoColumnMenuItem[][];
}

interface NavbarProps {
  menu?: MenuItem[];
  auth?: {
    login: {
      title: string;
      url: string;
    };
    signup: {
      title: string;
      url: string;
    };
  };
}

const Navbar = ({
  menu = [
    { title: 'How it works', url: '/how-it-works' },
    {
      title: 'Features',
      url: '/features',
      items: [
        {
          title: 'Overview',
          description: 'Everything you need to manage successful events',
          icon: (
            <Image
              src={overviewIcon}
              alt="Overview"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/event-organizers',
        },
        {
          title: 'Ticketing',
          description: 'Sell tickets seamlessly, anytime, anywhere',
          icon: (
            <Image
              src={ticketingIcon}
              alt="Ticketing"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/features/event-ticketing',
        },
        {
          title: 'Promoting',
          description: 'Boost your event with powerful marketing tools',
          icon: (
            <Image
              src={promotingIcon}
              alt="Promoting"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/features/event-marketing-and-promotion',
        },
        {
          title: 'Managing',
          description: 'Effortlessly manage attendees and events',
          icon: (
            <Image
              src={managingIcon}
              alt="Managing"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/features/event-management',
        },
        {
          title: 'Scanning',
          description: 'Fast, secure, and reliable check-in solutions',
          icon: (
            <Image
              src={scanningIcon}
              alt="Scanning"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/features/ticket-scanning',
        },
        {
          title: 'Reporting',
          description: 'Real-time insights to track your success',
          icon: (
            <Image
              src={reportingIcon}
              alt="Reporting"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/features/reporting-analytics',
        },
        {
          title: 'Integrating',
          description: 'Connect with your favorite tools & platforms',
          icon: (
            <Image
              src={integrationIcon}
              alt="Integrating"
              width={20}
              height={20}
              className="dark:brightness-0 dark:invert dark:filter"
            />
          ),
          url: '/features/modules-integrations',
        },
      ],
    },
    {
      title: "Who's it for",
      url: '/',
      twoColumnItems: [
        [
          {
            title: 'Event Creators',
            isHeader: true,
          },
          {
            title: 'First-timers',
            url: '/first-timers',
          },
          {
            title: 'Event pros',
            url: '/event-pros',
          },
          {
            title: 'Free events',
            url: '/free-events-ticketing',
          },
          {
            title: 'Community',
            url: '/community',
          },
          {
            title: 'Charities',
            url: '/charities',
          },
          {
            title: 'Attractions',
            url: '/tours-and-attractions',
          },
        ],
        [
          {
            title: 'Industry Solutions',
            isHeader: true,
          },
          {
            title: 'Festivals',
            url: '/festival-ticketing',
          },
          {
            title: 'Workshops',
            url: '/workshop-ticketing',
          },
          {
            title: 'Conferences',
            url: '/conference-ticketing',
          },
          {
            title: 'School & colleges',
            url: '/ticketing-for-school',
          },
          {
            title: 'Venues',
            url: '/venue-ticketing',
          },
          {
            title: 'View all',
            url: '/industries',
            isBold: true,
          },
        ],
      ],
    },
    {
      title: 'Pricing',
      url: '/pricing',
    },
    {
      title: 'Support',
      url: '/',
      items: [
        {
          title: 'Blog',
          url: 'https://blog.ticketcare.my',
        },
        {
          title: 'Help Center',
          url: 'https://docs.ticketcare.my',
        },
        {
          title: 'Contact Us',
          url: '/contact-us',
        },
      ],
    },
  ],
}: NavbarProps) => {
  return (
    <section className="py-4 shadow-md max-h-[72px]">
      <div className="container mx-auto">
        {/* Desktop Menu */}
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            {/* Logo */}
            <Link href="/" className="w-[128px]">
              <Image
                src={darkLogo}
                alt="Logo"
                width={248}
                height={124}
                className="hidden dark:block"
              />
              <Image
                src={lightLogo}
                alt="Logo"
                width={248}
                height={124}
                className="block dark:hidden"
              />
            </Link>
            <div className="flex items-center">
              <NavigationMenu
                viewport={false} // required to make list float underneath parent
              >
                <NavigationMenuList>
                  {menu.map((item) => renderMenuItem(item))}
                </NavigationMenuList>
              </NavigationMenu>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <ModeToggle />
            <Button asChild variant="outline" size="sm">
              <Link href="/events">Discover Events</Link>
            </Button>
            <Button asChild size="sm" variant="primary-red">
              <Link
                href={new URL('/sign-up', env.NEXT_PUBLIC_APP_URL).toString()}
              >
                Organize Events
              </Link>
            </Button>
          </div>
        </nav>

        {/* Mobile Menu */}
        <div className="block lg:hidden px-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/" className="w-[128px]">
              <Image
                src={darkLogo}
                alt="Logo"
                width={248}
                height={124}
                className="hidden dark:block"
              />
              <Image
                src={lightLogo}
                alt="Logo"
                width={248}
                height={124}
                className="block dark:hidden"
              />
            </Link>
            <div className="flex items-center gap-2 lg:hidden">
              <ModeToggle />
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Menu className="size-4" />
                  </Button>
                </SheetTrigger>
                <SheetContent className="w-full h-full flex flex-col gap-0">
                  <SheetHeader className="shadow-md">
                    <SheetTitle>
                      <Link href="/" className="block w-[128px]">
                        <Image
                          src={darkLogo}
                          alt="Logo"
                          width={248}
                          height={124}
                          className="hidden dark:block"
                        />
                        <Image
                          src={lightLogo}
                          alt="Logo"
                          width={248}
                          height={124}
                          className="block dark:hidden"
                        />
                      </Link>
                    </SheetTitle>
                  </SheetHeader>
                  <div className="overflow-y-auto flex-1 px-4 py-8">
                    <Accordion
                      type="single"
                      collapsible
                      className="flex w-full flex-col gap-4"
                    >
                      {menu.map((item) => renderMobileMenuItem(item))}
                    </Accordion>
                  </div>
                  <div className="flex items-center gap-3 justify-center p-4 border-t-1">
                    <Button asChild variant="outline" size="sm">
                      <Link href="/events">Discover Events</Link>
                    </Button>
                    <Button asChild size="sm" variant="primary-red">
                      <Link
                        href={new URL(
                          '/sign-up',
                          env.NEXT_PUBLIC_APP_URL
                        ).toString()}
                      >
                        Organize Events
                      </Link>
                    </Button>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

const renderMenuItem = (item: MenuItem) => {
  // Handle two-column layout
  if (item.twoColumnItems) {
    return (
      <NavigationMenuItem key={item.title}>
        <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>
        <NavigationMenuContent className="bg-popover text-popover-foreground z-10">
          <div className="flex w-90 space-x-2">
            {item.twoColumnItems.map((column, columnIndex) => (
              <div key={columnIndex} className="flex flex-1 flex-col">
                {column.map((subItem, itemIndex) => (
                  <TwoColumnMenuLink
                    key={`${columnIndex}-${itemIndex}`}
                    item={subItem}
                  />
                ))}
              </div>
            ))}
          </div>
        </NavigationMenuContent>
      </NavigationMenuItem>
    );
  }

  // Handle regular dropdown
  if (item.items) {
    return (
      <NavigationMenuItem key={item.title}>
        <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>
        <NavigationMenuContent className="bg-popover text-popover-foreground z-10">
          {item.items.map((subItem) => (
            <div
              key={subItem.title}
              className={subItem.description ? 'w-80' : 'w-50'}
            >
              <NavigationMenuLink asChild>
                <SubMenuLink item={subItem} />
              </NavigationMenuLink>
            </div>
          ))}
        </NavigationMenuContent>
      </NavigationMenuItem>
    );
  }

  return (
    <NavigationMenuItem key={item.title}>
      <NavigationMenuLink
        href={item.url}
        className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 font-medium text-sm transition-colors hover:bg-muted hover:text-accent-foreground"
      >
        {item.title}
      </NavigationMenuLink>
    </NavigationMenuItem>
  );
};

const renderMobileMenuItem = (item: MenuItem) => {
  if (item.items) {
    return (
      <AccordionItem key={item.title} value={item.title} className="border-b-0">
        <AccordionTrigger className="py-0 font-semibold text-md hover:no-underline">
          {item.title}
        </AccordionTrigger>
        <AccordionContent className="mt-2">
          {item.items.map((subItem) => (
            <SubMenuLink key={subItem.title} item={subItem} />
          ))}
        </AccordionContent>
      </AccordionItem>
    );
  }

  const isExternal = item.url.startsWith('https');

  return (
    <Link
      key={item.title}
      href={item.url}
      className="font-semibold text-md"
      {...(isExternal ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
    >
      {item.title}
    </Link>
  );
};

const SubMenuLink = ({ item }: { item: MenuItem }) => {
  const isExternal = item.url.startsWith('https');

  return (
    <Link
      className="flex select-none flex-row gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-muted hover:text-accent-foreground"
      href={item.url}
      {...(isExternal ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
    >
      <div className="text-foreground">{item.icon}</div>
      <div className="space-y-2">
        <div className="font-semibold leading-[0.8rem]">{item.title}</div>
        {item.description && (
          <p className="text-muted-foreground text-sm leading-snug">
            {item.description}
          </p>
        )}
      </div>
    </Link>
  );
};

const TwoColumnMenuLink = ({ item }: { item: TwoColumnMenuItem }) => {
  if (item.isHeader) {
    return <h3 className="p-3 font-semibold">{item.title}</h3>;
  }

  const isExternal = item.url?.startsWith('https');

  return (
    <Link
      className="flex select-none flex-row gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-muted hover:text-accent-foreground"
      href={item.url || '/'}
      {...(isExternal ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
    >
      <div
        className={`${item.isBold ? 'font-bold underline' : 'font-medium'} text-sm`}
      >
        {item.title}
      </div>
      {item.subtitle && (
        <p className="text-muted-foreground text-sm leading-snug">
          {item.subtitle}
        </p>
      )}
    </Link>
  );
};

export { Navbar };
