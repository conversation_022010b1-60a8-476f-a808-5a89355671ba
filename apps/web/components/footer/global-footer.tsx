import { env } from '@/env';
import { Button } from '@repo/design-system/components/ui/button';
import { CircleCheck } from 'lucide-react';
import Link from 'next/link';

const sections = [
  {
    title: 'Use TicketCARE',
    links: [
      { name: 'Create an event', href: `${env.NEXT_PUBLIC_APP_URL}/events` },
      { name: 'Integrations', href: '/features/modules-integrations' },
      { name: 'Pricing', href: '/pricing' },
      { name: 'Check-In App', href: '/apps' },
      { name: 'Blog', href: 'https://blog.ticketcare.my' },
      { name: 'Help Center', href: 'https://docs.ticketcare.my' },
    ],
  },
  {
    title: 'Company',
    links: [
      { name: 'About TicketCARE', href: '/about' },
      { name: 'Terms of Service', href: '/legal/terms-of-service' },
      {
        name: 'Consumer Data Privacy Policy',
        href: '/legal/consumer-data-privacy-policy',
      },
      {
        name: 'Event Organizer Privacy Policy',
        href: '/legal/event-organizer-privacy-policy',
      },
      { name: 'Website Terms of Use', href: '/legal/website-terms-of-use' },
      {
        name: 'Cancellation & Refund Policy',
        href: '/legal/cancellation-refund-policy',
      },
    ],
  },
  {
    title: 'Support',
    links: [
      { name: 'Info for ticket buyers', href: '/legal/info-for-ticket-buyers' },
      { name: 'Contact us', href: '/contact-us' },
      { name: 'Book a demo', href: 'https://cal.com/360hq/ticketcare' },
      { name: 'Partner program', href: '/partner-program' },
      { name: 'Service status', href: 'https://status.ticketcare.my/' },
    ],
  },
  {
    title: 'Quicklinks',
    links: [
      { name: 'Discover events', href: '/events' },
      { name: 'Ticket2u alternative', href: '/' },
      { name: 'CloudJoi alternative', href: '/' },
      { name: 'BookMyShow alternative', href: '/' },
      { name: 'Howei alternative', href: '/' },
      { name: 'Ticketing for charities', href: '/' },
      { name: 'Ticketing for schools', href: '/' },
    ],
  },
];

// Footer9 @ https://www.shadcnblocks.com/block/footer9
const GlobalFooter = () => {
  return (
    <section className="py-32 border-t border-border">
      <div className="container px-4 mx-auto">
        <footer>
          <div className="mb-14 flex flex-col justify-between gap-11 md:items-start xl:flex-row xl:items-center xl:gap-6">
            <div>
              <h1 className="mb-4 text-4xl font-semibold">
                Your tickets, handled with care & flair
              </h1>
              <p className="text-muted-foreground mb-8 text-xl">
                Try it free, or pick the perfect plan—your tickets deserve VIP
                treatment.
              </p>
              <div className="flex items-center gap-3">
                <Button asChild size="sm" variant="primary-red">
                  <Link
                    href={new URL(
                      '/sign-up',
                      env.NEXT_PUBLIC_APP_URL
                    ).toString()}
                  >
                    Start for free
                  </Link>
                </Button>
                <Button variant="outline">
                  <Link href="/pricing">Compare plans</Link>
                </Button>
              </div>
            </div>
            <div className="bg-background flex flex-col justify-between gap-6 rounded-2xl p-6 shadow-lg md:flex-row">
              <div className="flex flex-col items-center justify-center p-10">
                <div className="flex text-6xl font-semibold">
                  0<div className="h-full text-xl">RM</div>
                </div>
                <div className="text-sm">Free forever</div>
              </div>
              <div className="bg-muted-foreground/30 h-[1px] w-full md:h-auto md:w-[1px]" />
              <ul className="text-muted-foreground flex flex-col justify-center space-y-3">
                <li className="hover:text-primary flex items-center gap-2 font-medium">
                  <CircleCheck className="text-primary h-5 w-5" />
                  <p className="text-gray-400">Schools</p>
                </li>
                <li className="hover:text-primary flex items-center gap-2 font-medium">
                  <CircleCheck className="text-primary h-5 w-5" />
                  <p className="text-gray-400">Free Events</p>
                </li>
                <li className="hover:text-primary flex items-center gap-2 font-medium">
                  <CircleCheck className="text-primary h-5 w-5" />
                  <p className="text-gray-400">&lt;25 tickets</p>
                </li>
                <li className="hover:text-primary flex items-center gap-2 font-medium">
                  <CircleCheck className="text-primary h-5 w-5" />
                  <p className="text-gray-400">Basic Support</p>
                </li>
              </ul>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-8 border-t pt-20 lg:grid-cols-5">
            {sections.map((section, sectionIdx) => (
              <div key={sectionIdx}>
                <h3 className="mb-4 font-bold">{section.title}</h3>
                <ul className="text-muted-foreground space-y-4">
                  {section.links.map((link, index) => {
                    const isExternal = link.href.startsWith('http');

                    return (
                      <li
                        key={index}
                        className="hover:text-primary-red hover:font-semibold font-medium"
                      >
                        <Link
                          href={link.href}
                          {...(isExternal
                            ? { target: '_blank', rel: 'noopener noreferrer' }
                            : {})}
                        >
                          {link.name}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ))}
          </div>
          <div className="text-muted-foreground mt-20 flex flex-col items-start justify-between gap-4 border-t pt-8 text-center text-sm font-medium lg:flex-row lg:items-center">
            <p className="text-gray-400">
              © {new Date(Date.now()).getFullYear()} Omakase Software Sdn Bhd
              (201901001715). All rights reserved.
            </p>
            {/* <ul className="flex justify-center gap-4 lg:justify-start">
              <li className="hover:text-primary">
                <Link href="/">Privacy</Link>
              </li>
              <li className="hover:text-primary">
                <Link href="/">Terms</Link>
              </li>
              <li className="hover:text-primary">
                <Link href="/">Imprint</Link>
              </li>
            </ul> */}
          </div>
        </footer>
      </div>
    </section>
  );
};

export { GlobalFooter };
