import darkLogo from '@/public/logo-dark.png';
import lightLogo from '@/public/logo-light.png';
import Image from 'next/image';
import Link from 'next/link';

interface MenuItem {
  title: string;
  links: {
    text: string;
    url: string;
  }[];
}

interface EventFooterProps {
  menuItems?: MenuItem[];
  copyright?: string;
  bottomLinks?: {
    text: string;
    url: string;
  }[];
}

const EventFooter = ({
  menuItems = [
    {
      title: 'Links',
      links: [
        { text: 'Home', url: '/' },
        { text: 'Discover events', url: '/events' },
        { text: 'Find your tickets', url: '/account/orders' },
      ],
    },
    {
      title: 'Support',
      links: [
        { text: 'Info for ticket buyers', url: '/legal' },
        { text: 'Blog', url: 'https://blog.ticketcare.my' },
        { text: 'Help Center', url: 'https://docs.ticketcare.my' },
        { text: 'Contact Us', url: '/contact-us' },
      ],
    },
    {
      title: 'Legal',
      links: [
        { text: 'Terms of Service', url: '/legal/terms-of-service' },
        {
          text: 'Consumer Data Privacy Policy',
          url: '/legal/consumer-data-privacy-policy',
        },
        { text: 'Website Terms of Use', url: '/legal/website-terms-of-use' },
        {
          text: 'Cancellation & Refund Policy',
          url: '/legal/cancellation-refund-policy',
        },
      ],
    },
  ],
  copyright = `© ${new Date(Date.now()).getFullYear()} Omakase Software Sdn Bhd (************). All rights reserved.`,
  // bottomLinks = [
  //   { text: 'Terms and Conditions', url: '#' },
  //   { text: 'Privacy Policy', url: '#' },
  // ],
}: EventFooterProps) => {
  return (
    <section className="py-32 border-t border-border">
      <div className="container mx-auto px-4">
        <footer>
          <div className="grid grid-cols-2 gap-8 lg:grid-cols-6">
            <div className="col-span-2 mb-8 lg:mb-0">
              <div className="flex items-center gap-2 lg:justify-start">
                <Link href="/" className="block w-[160px]">
                  <Image
                    src={darkLogo}
                    alt="Logo"
                    width={445}
                    height={128}
                    className="hidden dark:block"
                  />
                  <Image
                    src={lightLogo}
                    alt="Logo"
                    width={445}
                    height={128}
                    className="block dark:hidden"
                  />
                </Link>
              </div>
            </div>
            {menuItems.map((section, sectionIdx) => (
              <div key={sectionIdx}>
                <h3 className="mb-4 font-bold">{section.title}</h3>
                <ul className="text-muted-foreground space-y-4">
                  {section.links.map((link, linkIdx) => (
                    <li
                      key={linkIdx}
                      className="hover:text-primary-red font-medium"
                    >
                      <Link
                        href={link.url}
                        {...(link.url.startsWith('http')
                          ? { target: '_blank', rel: 'noopener noreferrer' }
                          : {})}
                      >
                        {link.text}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          <div className="text-muted-foreground mt-24 flex flex-col justify-between gap-4 border-t pt-8 text-sm font-medium md:flex-row md:items-center">
            <p>{copyright}</p>
            {/* <ul className="flex gap-4">
              {bottomLinks.map((link, linkIdx) => (
                <li key={linkIdx} className="hover:text-primary underline">
                  <a href={link.url}>{link.text}</a>
                </li>
              ))}
            </ul> */}
          </div>
        </footer>
      </div>
    </section>
  );
};

export { EventFooter };
