'use client';

import { Banner } from '@/components/banner';
import { Navbar } from '@/components/navbar';
import { cn } from '@repo/design-system/lib/utils';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

export const GlobalHeader = () => {
  const route = usePathname();
  const [bannerOpen, setBannerOpen] = useState(!route.includes('/pricing'));

  return (
    <>
      <header className="fixed top-0 right-0 left-0 z-50 w-full bg-background">
        <Navbar />
        <Banner
          title={
            <p className="text-sm font-medium">
              Secure your <span className="font-bold">Founter's Pass</span> 🎉
            </p>
          }
          description="A special thanks to our early believers. Limited time only!"
          buttonText="Learn more"
          buttonUrl="/pricing"
          defaultVisible={!route.includes('/pricing')}
          parentHandleClose={setBannerOpen}
        />
      </header>
      <div
        className={cn(
          'h-[72px] transition-all ease-in-out duration-200',
          bannerOpen ? 'pt-64 md:pt-40' : 'pt-24'
        )}
      />
    </>
  );
};
