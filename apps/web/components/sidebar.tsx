import { capitalize } from '@repo/design-system/lib/utils';
import type { ReactNode } from 'react';

type SidebarProperties = {
  readonly date: Date;
  readonly readingTime: string;
  readonly tags?: string[];
  readonly toc?: ReactNode;
};

export const Sidebar = async ({
  date,
  readingTime,
  tags,
  toc: Toc,
}: SidebarProperties) => (
  <div className="col-span-4 flex w-72 flex-col items-start gap-8 border-foreground/10 border-l px-6 lg:col-span-2">
    <div className="grid gap-2">
      <p className="caption">Published</p>
      <p className="rounded-sm text-foreground text-sm">
        {new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
          timeZone: 'America/New_York',
        }).format(date)}
      </p>
    </div>
    <div className="grid gap-2">
      <p className="caption">Reading Time</p>
      <p className="caption rounded-sm text-foreground">{readingTime}</p>
    </div>
    {tags && (
      <div className="grid gap-2">
        <p className="caption">Tags</p>
        <p className="caption rounded-sm text-foreground">
          {tags.map(capitalize).join(', ')}
        </p>
      </div>
    )}
    {Toc ? (
      <div className="-mx-2">
        <div className="grid gap-2 p-2">
          <p className="caption text-muted-foreground">Sections</p>
          {Toc}
        </div>
      </div>
    ) : undefined}
  </div>
);
