'use client';

import type { ChipPaymentMethod } from '@/app/[locale]/(checkout)/checkout/utils/constant';
import ChipCards from '@/public/chip-cards.png';
import ChipDuitnow from '@/public/chip-duitnow.png';
import ChipEWallets from '@/public/chip-ewallets.png';
import ChipFPX from '@/public/chip-fpx.png';
import { CheckIcon } from '@radix-ui/react-icons';
import {
  FormField,
  FormMessage,
  useFormContext,
} from '@repo/design-system/components/ui/form';
import {
  ToggleGroup,
  ToggleGroupItem,
} from '@repo/design-system/components/ui/toggle-group';
import Image from 'next/image';
import type { FC } from 'react';

interface PaymentMethodPickerProps {
  paymentMethods: Record<string, ChipPaymentMethod>;
}

export const PaymentMethodPicker: FC<PaymentMethodPickerProps> = ({
  paymentMethods,
}) => {
  const form = useFormContext();

  return (
    <div className="space-y-[24px]">
      <div>
        <h2>Payment Method</h2>
        <p className="text-muted-foreground">
          Payments are securely processed via third-party provider. You will be
          redirected to their payment gateway to complete your transaction.
        </p>
      </div>
      <FormField
        control={form.control}
        name="paymentMethod"
        render={({ field }) => (
          <>
            <ToggleGroup
              type="single"
              value={field.value}
              onValueChange={(value) => value && field.onChange(value)}
              className="grid w-full grid-cols-2 gap-[16px] space-y-3"
            >
              <ToggleGroupItem
                value="fpx"
                className="m-0 block h-fit space-y-4 rounded-md border p-[16px] hover:text-primary data-[state=on]:border-primary-red data-[state=on]:bg-transparent"
                disabled={!paymentMethods.fpx}
              >
                <div className="flex w-full justify-between">
                  <span>Malaysia FPX (B2C)</span>
                  {field.value === 'fpx' && (
                    <CheckIcon className="rounded-full bg-primary-red text-white" />
                  )}
                </div>
                <Image src={ChipFPX} alt="FPX" draggable={false} />
              </ToggleGroupItem>
              <ToggleGroupItem
                value="card"
                className="m-0 block h-fit space-y-4 rounded-md border p-[16px] hover:text-primary data-[state=on]:border-primary-red data-[state=on]:bg-transparent"
                disabled={!paymentMethods.card}
              >
                <div className="flex w-full justify-between">
                  <span>Credit / Debit Card</span>
                  {field.value === 'card' && (
                    <CheckIcon className="rounded-full bg-primary-red text-white" />
                  )}
                </div>
                <Image src={ChipCards} alt="Cards" draggable={false} />
              </ToggleGroupItem>
              <ToggleGroupItem
                value="duitnow"
                className="m-0 block h-fit space-y-4 rounded-md border p-[16px] hover:text-primary data-[state=on]:border-primary-red data-[state=on]:bg-transparent"
                disabled={!paymentMethods.duitnow}
              >
                <div className="flex w-full justify-between">
                  <span>DuitNow QR</span>
                  {field.value === 'duitnow' && (
                    <CheckIcon className="rounded-full bg-primary-red text-white" />
                  )}
                </div>
                <Image src={ChipDuitnow} alt="DuitNow" draggable={false} />
              </ToggleGroupItem>
              <ToggleGroupItem
                value="eWallet"
                className="m-0 block h-fit space-y-4 rounded-md border p-[16px] hover:text-primary data-[state=on]:border-primary-red data-[state=on]:bg-transparent"
                disabled={!paymentMethods.eWallet}
              >
                <div className="flex w-full justify-between">
                  <span>E-Wallet</span>
                  {field.value === 'eWallet' && (
                    <CheckIcon className="rounded-full bg-primary-red text-white" />
                  )}
                </div>
                <Image src={ChipEWallets} alt="eWallets" draggable={false} />
              </ToggleGroupItem>
            </ToggleGroup>
            <FormMessage />
          </>
        )}
      />
    </div>
  );
};
