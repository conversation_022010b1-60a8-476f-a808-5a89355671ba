'use client';

import { Button } from '@repo/design-system/components/ui/button';
import { X } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { type ReactNode, useState } from 'react';

interface BannerProps {
  title: string | ReactNode;
  description: string;
  buttonText: string;
  buttonUrl: string;
  defaultVisible?: boolean;
  parentHandleClose?: (open: boolean) => void;
}

const Banner = ({
  title = '',
  description = '',
  buttonText = 'Learn more',
  buttonUrl = '/',
  defaultVisible = true,
  parentHandleClose,
}: BannerProps) => {
  const [isVisible, setIsVisible] = useState(defaultVisible);

  const handleClose = () => {
    parentHandleClose?.(false);
    setIsVisible(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.section
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1, ease: 'easeInOut' }}
          className="bg-primary w-full p-4"
        >
          <div className="container mx-auto">
            <div className="relative flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-8 w-8 md:hidden"
                onClick={handleClose}
              >
                <X className="h-4 w-4" />
              </Button>

              <div className="text-primary-foreground flex flex-col items-start gap-3 pt-2 md:flex-row md:items-center md:pt-0">
                <div className="flex flex-col gap-1 md:flex-row md:items-center">
                  {typeof title === 'object' ? (
                    title
                  ) : (
                    <p className="text-sm font-medium">{title}</p>
                  )}
                  <p className="text-primary-foreground/80 text-sm">
                    {description}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Link href={buttonUrl}>
                  <Button
                    size="sm"
                    className="dark w-full md:w-auto"
                    variant="secondary"
                  >
                    {buttonText}
                  </Button>
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  className="hidden h-8 w-8 invert md:inline-flex"
                  onClick={handleClose}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 absolute top-0 right-0 invert md:hidden"
                onClick={handleClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </motion.section>
      )}
    </AnimatePresence>
  );
};

export { Banner };
