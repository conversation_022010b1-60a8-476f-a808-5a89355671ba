'use client';

import {
  <PERSON><PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@repo/design-system/components/ui/breadcrumb';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Fragment } from 'react';

// Helper function to capitalize first letter of each word
const capitalize = (str: string): string => {
  return str
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

interface BreadcrumbsProps {
  childLabel?: string;
  includeLastSegment?: boolean;
  parentLabels?: string[];
  isEventRoot?: boolean;
}

export function Breadcrumbs({
  childLabel,
  includeLastSegment = true,
  parentLabels = [],
  isEventRoot = false,
}: BreadcrumbsProps = {}) {
  const pathname = usePathname();

  // Remove locale prefix if present (e.g., /en/events -> /events)
  const pathParts = pathname.split('/');
  const localeIndex = pathParts.length > 1 ? 1 : 0;
  const pathWithoutLocale = `/${pathParts.slice(localeIndex + 1).join('/')}`;

  // Split the path into segments and clean them with regex
  const pathRegex = /\[\w+\]|\(\w+\)/g;
  const segments = pathWithoutLocale
    .split('/')
    .filter(Boolean)
    .map((segment) => {
      // Remove any dynamic route parameters (e.g., [id] or (group))
      return segment.replace(pathRegex, '');
    });

  // Create breadcrumb items
  // If includeLastSegment is false, exclude the last segment
  const segmentsToUse = includeLastSegment ? segments : segments.slice(0, -1);

  const breadcrumbs = segmentsToUse.map((segment) => {
    // For each segment in segmentsToUse, find its original index in the segments array
    const originalIndex = segments.indexOf(segment);

    // Create the href for this breadcrumb using the original segments
    const href = `/${segments.slice(0, originalIndex + 1).join('/')}`;

    // Format the segment name for display
    const name = capitalize(segment.replace(/-/g, ' '));

    return {
      href,
      name,
    };
  });

  // If there are no segments, we're on the home page
  if (breadcrumbs.length === 0) {
    return <div className="mb-8 md:mb-12" />;
  }

  return (
    <Breadcrumb className="px-4 mb-8 md:mb-12">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href={isEventRoot ? '/events' : '/'}>Home</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>

        <BreadcrumbSeparator />

        {/* Path segments */}
        {breadcrumbs.map((breadcrumb, index) => (
          <Fragment key={breadcrumb.href}>
            <BreadcrumbItem>
              {index === breadcrumbs.length - 1 ? (
                <BreadcrumbPage>{childLabel || breadcrumb.name}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link href={breadcrumb.href}>
                    {parentLabels[index] || breadcrumb.name}
                  </Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>

            {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
          </Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
