# Donation Flow (Updated):

## Overview
1. Donation is a separate purchase flow from regular tickets
2. Customers first purchase a donations package with flexible pricing (they input the amount)
4. After purchasing a donations, customers receive a receipt email & certificate

## User Journey
1. Customer clicks on the donations tab in @apps/web/app/[locale]/(events)/events/[slug]/components/event-tabs.tsx to view available donations packages
2. They input their desired donations amount (within min/max limits set by the organizer)
3. They upload their business logo/image as part of the donations purchase process
4. They complete the checkout for the donations
5. They receive an email for confirmation

## Database Changes
1. The schema at @packages/database/prisma/schema.prisma needs to be updated to separate donations from tickets
2. Create new models:
  - `DonationPurchase`: Tracks purchased donations, including optional company name/description/logo, amount paid

## Admin Panel
1. If any event has premium tier, they are allowed to accept donations
2. Add a table for donations accepted for this events
