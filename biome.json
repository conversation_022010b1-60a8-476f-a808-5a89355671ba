{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "extends": ["ultracite"], "javascript": {}, "files": {"ignore": ["packages/design-system/components/ui/**", "packages/design-system/components/blocks/**", "packages/design-system/components/editor/**", "packages/design-system/lib/**", "packages/design-system/hooks/**", "apps/email/.react-email/**"]}, "linter": {"rules": {"nursery": {"useSortedClasses": "off", "noNestedTernary": "off"}, "style": {"useBlockStatements": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off"}, "suspicious": {"noConsole": {"level": "error", "options": {"allow": ["error"]}}}}}}