services:
  postgres:
    image: postgres:latest
    container_name: ticketcare-db
    ports:
      - "5432:5432"  # Expose port 5432 on the host
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: ticketcare
    volumes:
      - postgres_data:/var/lib/postgresql/data  # Persist data
      - ./init:/docker-entrypoint-initdb.d  # Auto-run init scripts on first startup

volumes:
  postgres_data:
