# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a **Turborepo monorepo** with three main Next.js applications and shared packages:

- **apps/api**: Backend API (`localhost:3002`) - REST endpoints, webhooks, admin operations
- **apps/app**: Admin dashboard (`localhost:3000`) - Event management, orders, user management
- **apps/web**: Public website (`localhost:3001`) - Event discovery, ticketing, donations

## Key Technologies

- **Framework**: Next.js 15 with App Router
- **Database**: Prisma + PostgreSQL
- **Authentication**: Better Auth
- **Payments**: Stripe + Chip (Malaysian payment gateway)
- **Styling**: Tailwind CSS v4 + shadcn/ui
- **Testing**: Vitest
- **Linting**: Biome
- **Type Checking**: TypeScript

## Development Commands

```bash
# Install dependencies
pnpm install

# Development - runs all apps concurrently
pnpm dev

# Individual app development
pnpm --filter api dev    # API (port 3002)
pnpm --filter app dev    # Admin (port 3000)
pnpm --filter web dev    # Public site (port 3001)

# Build all apps
pnpm build

# Lint and format
pnpm lint
pnpm format

# Testing
pnpm test

# Database operations
pnpm prisma         # Format and generate Prisma client
pnpm migrate        # Run migrations
pnpm seed          # Seed database with test data
pnpm migrate-reset # Reset database
```

## Directory Structure

```
├── apps/
│   ├── api/           # Backend API (Next.js API routes)
│   ├── app/           # Admin dashboard
│   └── web/           # Public website
├── packages/
│   ├── database/      # Prisma schema & database utilities
│   ├── design-system/ # Shared UI components (shadcn/ui)
│   ├── auth/          # Authentication utilities
│   ├── payments/      # Payment processing (Stripe + Chip)
│   └── [other-shared]/ # Analytics, email, storage, etc.
```

## Key Business Logic

- **Events**: Create/manage events with ticket types, time slots, inventory
- **Orders**: Handle ticket purchases with Stripe/Chip payments
- **Donations**: Support event-based donations
- **Ticketing**: QR code generation, ticket redemption, verification
- **Multi-tenant**: Support for multiple organizers/venues

## Environment Setup

1. Copy environment files from `_env/` to root as `.env.local`
2. Run `pnpm install` to install dependencies
3. Run `pnpm migrate` to set up database
4. Run `pnpm seed` to populate test data
5. Start development with `pnpm dev`

## Testing

- Unit tests: `pnpm test`
- Individual app tests: `pnpm --filter [app-name] test`
- All tests run automatically before builds

## Database Schema

Core entities: `User`, `Organizer`, `Venue`, `Event`, `TicketType`, `Order`, `Ticket`, `Donation`, `EventModule`

View detailed schema: `packages/database/prisma/schema.prisma`