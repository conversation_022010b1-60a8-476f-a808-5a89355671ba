{"name": "@repo/ai", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@ai-sdk/openai": "^1.2.0", "@t3-oss/env-nextjs": "^0.12.0", "ai": "^4.1.52", "react": "^19.0.0", "react-markdown": "^10.0.1", "tailwind-merge": "^3.0.2", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4"}}