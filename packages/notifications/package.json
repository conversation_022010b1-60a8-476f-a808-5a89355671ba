{"name": "@repo/notifications", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@knocklabs/node": "^0.6.18", "@knocklabs/react": "^0.5.0", "@t3-oss/env-nextjs": "^0.12.0", "react": "^19.0.0", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.2", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4"}}