{"name": "@repo/analytics", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@next/third-parties": "15.1.7", "@t3-oss/env-nextjs": "^0.12.0", "@vercel/analytics": "^1.5.0", "posthog-js": "^1.227.0", "posthog-node": "^4.11.1", "react": "^19.0.0", "server-only": "^0.0.1", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4"}}