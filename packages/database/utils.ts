import {
  CartStatus,
  OrderStatus,
  PaymentStatus,
  TicketStatus,
} from '@prisma/client';
import { log } from '@repo/observability/log';
import { database } from '.';

/**
 * This function finds expired carts and cleans up their related orders
 * It will:
 * 1. Find expired carts with status idle or active
 * 2. For each cart, find orders in 'hold' status
 * 3. Mark orders as void and restore inventory
 * 4. Mark carts as abandoned
 *
 * @param expirationThreshold Optional time in minutes before current time to consider carts expired (default: 30)
 * @returns Summary of cleanup operations performed
 */
export async function cleanUpExpiredCart() {
  try {
    // Find expired carts
    const expiredCarts = await database.cart.findMany({
      where: {
        OR: [{ status: CartStatus.idle }, { status: CartStatus.active }],
        expiresAt: {
          lt: new Date(),
        },
      },
      include: {
        user: true,
      },
    });

    if (expiredCarts.length === 0) {
      log.info('No expired carts found');
      return {
        processedCarts: 0,
        processedOrders: 0,
        restoredTickets: 0,
      };
    }

    log.info(`Found ${expiredCarts.length} expired carts`, {
      cartIds: expiredCarts.map((cart) => cart.id),
    });

    let processedOrders = 0;
    let restoredTickets = 0;

    // Process each expired cart
    for (const cart of expiredCarts) {
      // Find orders related to this cart that are in 'hold' status
      const holdOrders = await database.order.findMany({
        where: {
          cartId: cart.id,
          status: OrderStatus.hold,
          paymentStatus: PaymentStatus.pending,
        },
        include: {
          user: true,
        },
      });

      if (holdOrders.length > 0) {
        log.info(
          `Found ${holdOrders.length} orders in hold status for cart ${cart.id}`,
          {
            cartId: cart.id,
            orderIds: holdOrders.map((o) => o.id),
          }
        );

        // Process each order
        for (const order of holdOrders) {
          // Update order status to void
          await database.order.update({
            where: { id: order.id },
            data: {
              status: OrderStatus.void,
              paymentStatus: PaymentStatus.cancelled,
            },
          });
          log.info(`Updated order ${order.id} status to void`, {
            orderId: order.id,
          });

          // Update ticket status to void
          const affectedTickets = await database.ticket.updateManyAndReturn({
            where: { orderId: order.id },
            data: { status: TicketStatus.void },
          });
          log.info(
            `Updated ${affectedTickets.length} tickets for order ${order.id} to void`,
            {
              orderId: order.id,
            }
          );

          // Restore inventory for voided tickets
          if (affectedTickets.length > 0) {
            // Group tickets by ticketType and timeSlot for inventory restoration
            const ticketCounts: Record<string, number> = {};

            // Count tickets by ticketType and timeSlot combination
            for (const ticket of affectedTickets) {
              const key = `${ticket.ticketTypeId}|${ticket.timeSlotId}`;
              ticketCounts[key] = (ticketCounts[key] || 0) + 1;
            }

            // Update inventory for each combination
            for (const [key, count] of Object.entries(ticketCounts)) {
              const [ticketTypeId, timeSlotId] = key.split('|');

              // Find the inventory for this combination
              const inventory = await database.inventory.findFirst({
                where: {
                  ticketTypeId,
                  timeSlotId,
                },
              });

              if (inventory) {
                // Increment inventory by the number of voided tickets
                await database.inventory.update({
                  where: { id: inventory.id },
                  data: {
                    quantity: {
                      increment: count,
                    },
                  },
                });

                log.info(`Restored ${count} tickets to inventory`, {
                  inventoryId: inventory.id,
                  ticketTypeId,
                  timeSlotId,
                  count,
                });

                restoredTickets += count;
              } else {
                log.warn(
                  `Inventory not found for ticket type ${ticketTypeId} and time slot ${timeSlotId}`
                );
              }
            }
          }

          processedOrders++;
        }
      }

      // Mark cart as abandoned
      await database.cart.update({
        where: { id: cart.id },
        data: { status: CartStatus.abandoned },
      });
      log.info(`Updated cart ${cart.id} status to abandoned`, {
        cartId: cart.id,
      });
    }

    return {
      processedCarts: expiredCarts.length,
      processedOrders,
      restoredTickets,
    };
  } catch (error) {
    log.error('Error during cleanUpExpiredCart', { error });
    throw error;
  }
}

// Function to generate ticket slug
export function generateTicketSlug(): string {
  const prefix = 'E';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;

  // Use current timestamp to seed the random generation
  const timestamp = Date.now();
  const timestampStr = timestamp.toString();

  // Mix timestamp digits into the random selection
  for (let i = 0; i < 8; i++) {
    // Use a different digit from timestamp for each position if available
    const timestampDigit =
      i < timestampStr.length ? Number.parseInt(timestampStr.at(i) || '0') : 0;
    // Use the timestamp digit to offset the random selection
    const randomIndex =
      (Math.floor(Math.random() * characters.length) + timestampDigit) %
      characters.length;
    result += characters.charAt(randomIndex);
  }

  return result;
}
