import { database, decrypt } from '@repo/database';

async function chipKeys(eventId?: string | null) {
  const event = await database.event.findUnique({
    where: { id: eventId ?? '' },
    select: {
      organizerId: true,
      organizer: {
        select: {
          chipSecretKey: true,
          chipBrandId: true,
          chipWebhookSecret: true,
        },
      },
    },
  });

  if (!event) {
    throw new Error('Event not found');
  }

  const { organizer } = event;

  if (
    !organizer ||
    !organizer.chipSecretKey ||
    !organizer.chipBrandId ||
    !organizer.chipWebhookSecret
  ) {
    throw new Error('Organizer not found');
  }

  // Decrypt the sensitive data
  const secretKey = organizer.chipSecretKey
    ? decrypt(organizer.chipSecretKey)
    : null;
  const brandId = organizer.chipBrandId ? decrypt(organizer.chipBrandId) : null;
  const webhookSecret = organizer.chipWebhookSecret
    ? decrypt(organizer.chipWebhookSecret)
    : null;

  return {
    chipSecretKey: secretKey,
    chipBrandId: brandId,
    chipPublicKey: webhookSecret,
  };
}

const CustomPaymentKeyService = { chipKeys };
export default CustomPaymentKeyService;
