import 'server-only';

import { Prisma } from '@prisma/client';
import prisma from './prisma-client';

export const database = prisma;

type SerializedPrisma<T> = T extends Prisma.Decimal
  ? number
  : T extends Date
    ? string
    : T extends object
      ? { [K in keyof T]: SerializedPrisma<T[K]> }
      : T extends Array<infer U>
        ? SerializedPrisma<U>[]
        : T;

export const serializePrisma = <T>(data: T): SerializedPrisma<T> => {
  return JSON.parse(
    JSON.stringify(data, (_, value) => {
      if (value instanceof Prisma.Decimal) {
        return value.toNumber();
      }

      if (value instanceof Date) {
        return value.toISOString();
      }

      return value;
    })
  );
};

export * from './utils';
export { encrypt, decrypt } from './encryption';
export * from './payment-keys';
