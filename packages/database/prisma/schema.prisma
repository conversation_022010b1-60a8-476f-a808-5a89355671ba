// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

//// User model (core entity for all roles)

model User {
  id            String    @id @default(uuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @map("email_verified")
  image         String?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  sessions      Session[]
  accounts      Account[]
  role          String    @default("customer") // super-admin, admin, customer
  banned        Boolean?  @default(false)
  banReason     String?   @map("ban_reason")
  banExpires    DateTime? @map("ban_expires")

  // Relationships
  organizer      Organizer? // A User can be an Organizer
  orders         Order[] // A User can place orders (customer role)
  carts          Cart[] // A User can have carts (customer role)
  approvedEvents Event[]    @relation("ApprovedBy") // Admin approves events

  firstName String?   @map("first_name")
  lastName  String?   @map("last_name")
  phone     String?
  dob       DateTime?

  // organization fields
  members          Member[]
  invitations      Invitation[]
  TicketRedemption TicketRedemption[]

  // roadmap relationships
  roadmapTasks     RoadmapTask[]
  reviewedRequests PublicRequest[]

  @@map("user")
}

//// Organization models

model Organization {
  id          String       @id @default(uuid())
  name        String
  slug        String?
  logo        String?
  createdAt   DateTime     @default(now()) @map("created_at")
  metadata    String?
  members     Member[]
  invitations Invitation[]

  chipApiKey String? @map("chip_api_key") // api key for chip payment gateway

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id @default(uuid())
  organizationId String       @map("organization_id")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String       @map("user_id")
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String // owner, organizer
  createdAt      DateTime     @default(now()) @map("created_at")

  @@map("member")
}

model Invitation {
  id             String       @id @default(uuid())
  organizationId String       @map("organization_id")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime     @map("expires_at")
  inviterId      String       @map("inviter_id")
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

//// Auth models

model Session {
  id             String   @id @default(uuid())
  expiresAt      DateTime @map("expires_at")
  token          String   @unique
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  ipAddress      String?  @map("ip_address")
  userAgent      String?  @map("user_agent")
  userId         String   @map("user_id")
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  impersonatedBy String?  @map("impersonated_by")

  activeOrganizationId   String? @map("active_organization_id")
  activeOrganizationName String? @map("active_organization_name")
  organizerId            String? @map("organizer_id")

  @@map("session")
}

model Account {
  id                    String    @id @default(uuid())
  accountId             String?   @map("account_id") // oauth account id
  providerId            String    @map("provider_id") // account type: credential, google, apple, oauth...
  userId                String    @map("user_id")
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @map("access_token")
  refreshToken          String?   @map("refresh_token")
  idToken               String?   @map("id_token")
  accessTokenExpiresAt  DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt DateTime? @map("refresh_token_expires_at")
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  @@map("account")
}

model Verification {
  id         String    @id @default(uuid())
  identifier String
  value      String
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime? @default(now()) @map("created_at")
  updatedAt  DateTime? @updatedAt @map("updated_at")

  @@map("verification")
}

model Organizer {
  id                 String             @id @default(uuid())
  userId             String             @unique @map("user_id") // One User per Organizer
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  name               String
  slug               String             @unique
  description        String?
  biography          Json?
  logo               String?
  website            String?
  facebook           String?
  twitter            String?
  instagram          String?
  youtube            String?
  tiktok             String?
  rednote            String?
  whatsapp           String?
  email              String
  phone              String?
  address            String?
  signature          String?
  picName            String?            @map("pic_name")
  picTitle           String?            @map("pic_title")
  accountName        String?            @map("account_name")
  accountNumber      String?            @map("account_number")
  bankName           String?            @map("bank_name")
  routingNumber      String?            @map("routing_number")
  verificationStatus VerificationStatus @map("verification_status")
  payoutFrequency    PayoutFrequency    @map("payout_frequency")
  commissionRate     Decimal            @map("commission_rate")
  emailNotifications Boolean            @map("email_notifications")
  smsNotifications   Boolean            @map("sms_notifications")
  pushNotifications  Boolean            @map("push_notifications")
  events             Event[]
  createdAt          DateTime           @default(now()) @map("created_at")
  updatedAt          DateTime           @updatedAt @map("updated_at")

  // CHIP payment integration fields
  chipSecretKey     String? @map("chip_secret_key")
  chipBrandId       String? @map("chip_brand_id")
  chipWebhookSecret String? @map("chip_webhook_secret")

  // Premium event upgrades
  premiumUpgrades EventPremiumUpgrade[]

  @@index([userId])
  @@map("organizer")
}

model Venue {
  id            String   @id @default(uuid())
  name          String
  slug          String   @unique
  description   String?
  address       String?
  city          String?
  state         String?
  country       String?
  postalCode    String?  @map("postal_code")
  latitude      Decimal?
  longitude     Decimal?
  totalCapacity Int?     @map("total_capacity")
  // amenities      String[]
  // parking        Boolean?
  // accessibility  Boolean?
  // food           Boolean?
  // drinks         Boolean?
  // ageRestriction Int?     @map("age_restriction")
  // dresscode      String?
  // customRules    String[] @map("custom_rules")
  images        String[]
  events        Event[]
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("venue")
}

model Event {
  id            String  @id @default(uuid())
  venueId       String? @map("venue_id")
  organizerId   String  @map("organizer_id")
  premiumTierId String? @map("premium_tier_id") // Reference to the premium tier
  approvedById  String? @map("approved_by_id")

  slug               String  @unique
  waitingListEnabled Boolean @default(false) @map("waiting_list_enabled")
  requiresApproval   Boolean @default(false) @map("requires_approval")
  approvedBy         User?   @relation("ApprovedBy", fields: [approvedById], references: [id], onDelete: SetNull)
  eventType          String  @default("sales") @map("event_type") // sales -> normal ticket, ngo -> change "buy" to "donate"
  ticketSalesMode    String  @default("pass_on_fee") @map("ticket_sales_mode") // pass_on_fee -> attendees pay fees, absorb_fee -> organizers pay fees

  // organizer
  organizer Organizer @relation(fields: [organizerId], references: [id], onDelete: Cascade)

  // editorial
  status               EventStatus @default(draft)
  visibility           Visibility  @default(public)
  startTime            DateTime    @map("start_time")
  endTime              DateTime    @map("end_time")
  doorsOpen            DateTime?   @map("doors_open")
  title                String
  description          String?
  category             String[]    @map("categories")
  tags                 String[]    @map("tags")
  refundPolicy         String?     @map("refund_policy")
  terms                String?
  updates              Json?       @map("updates") // JSONB array of event updates
  heroImageUrl         String?     @map("hero_image_url")
  sponsorBannerUrl     String?     @map("sponsor_banner_url")
  carouselImageUrls    String[]    @map("carousel_image_urls")
  checkoutFormQuestion String?     @map("checkout_form_question")

  // venue
  venueName    String? @map("venue_name")
  venueAddress String? @map("venue_address")
  venue        Venue?  @relation(fields: [venueId], references: [id], onDelete: Restrict)

  // tickets
  ticketsSold Int          @default(0) @map("tickets_sold")
  eventDates  EventDate[]
  tickets     Ticket[]
  ticketTypes TicketType[]

  // cart
  Cart Cart[]

  // orders
  orders Order[] @relation("EventOrders")

  // event premium tier
  isPremiumEvent     Boolean               @default(false) @map("is_premium_event") // Flag for premium events
  premiumTier        PremiumTier?          @relation(fields: [premiumTierId], references: [id])
  premiumUpgrades    EventPremiumUpgrade[]
  maxTicketsPerEvent Int                   @default(20) @map("max_tickets_per_event") // Default 20 for free users

  // event module
  eventModule    EventModule?
  eventDonations EventDonation[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@index([organizerId])
  @@index([approvedById])
  @@index([premiumTierId])
  @@map("event")
}

// Premium tier model for different levels of premium events
model PremiumTier {
  id                 String                @id @default(uuid())
  name               String // e.g., "Basic", "Standard", "Pro"
  description        String?
  maxTicketsPerEvent Int // Maximum number of tickets allowed for this tier
  price              Decimal // Price for this tier
  isActive           Boolean               @default(true)
  createdAt          DateTime              @default(now()) @map("created_at")
  updatedAt          DateTime              @updatedAt @map("updated_at")
  events             Event[]
  premiumUpgrades    EventPremiumUpgrade[]

  @@map("premium_tier")
}

model EventDate {
  id        String     @id @default(uuid())
  eventId   String     @map("event_id")
  date      DateTime
  event     Event      @relation(fields: [eventId], references: [id], onDelete: Cascade)
  timeSlots TimeSlot[]
  createdAt DateTime   @default(now()) @map("created_at")
  updatedAt DateTime   @updatedAt @map("updated_at")

  @@index([eventId])
  @@map("event_date")
}

model TimeSlot {
  id          String      @id @default(uuid())
  eventDateId String      @map("event_date_id")
  startTime   DateTime    @map("start_time")
  endTime     DateTime    @map("end_time")
  doorsOpen   DateTime    @map("doors_open")
  eventDate   EventDate   @relation(fields: [eventDateId], references: [id], onDelete: Cascade)
  tickets     Ticket[]
  inventory   Inventory[]
  cartItems   CartItem[]
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  @@index([eventDateId])
  @@map("time_slot")
}

model TicketType {
  id            String      @id @default(uuid())
  eventId       String      @map("event_id")
  visibility    Visibility  @default(public)
  name          String
  description   String?
  price         Decimal
  maxPerOrder   Int         @map("max_per_order")
  minPerOrder   Int         @map("min_per_order")
  saleStartTime DateTime    @map("sale_start_time")
  saleEndTime   DateTime    @map("sale_end_time")
  event         Event       @relation(fields: [eventId], references: [id], onDelete: Cascade)
  inventory     Inventory[]
  tickets       Ticket[]
  cartItems     CartItem[]
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")

  @@index([eventId])
  @@map("ticket_type")
}

model Inventory {
  id           String     @id @default(uuid())
  timeSlotId   String     @map("time_slot_id")
  ticketTypeId String     @map("ticket_type_id")
  quantity     Int
  timeSlot     TimeSlot   @relation(fields: [timeSlotId], references: [id], onDelete: Cascade)
  ticketType   TicketType @relation(fields: [ticketTypeId], references: [id], onDelete: Cascade)
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")

  @@unique([timeSlotId, ticketTypeId])
  @@index([timeSlotId])
  @@index([ticketTypeId])
  @@map("inventory")
}

model Ticket {
  id           String             @id @default(uuid())
  slug         String             @unique
  timeSlotId   String             @map("time_slot_id")
  ticketTypeId String             @map("ticket_type_id")
  orderId      String?            @map("order_id")
  status       TicketStatus
  purchaseDate DateTime?          @map("purchase_date")
  ownerName    String?            @map("owner_name")
  ownerEmail   String?            @map("owner_email")
  ownerPhone   String?            @map("owner_phone")
  timeSlot     TimeSlot           @relation(fields: [timeSlotId], references: [id], onDelete: Cascade)
  ticketType   TicketType         @relation(fields: [ticketTypeId], references: [id], onDelete: Cascade)
  order        Order?             @relation(fields: [orderId], references: [id], onDelete: SetNull)
  event        Event?             @relation(fields: [eventId], references: [id], onDelete: SetNull)
  eventId      String?
  redemptions  TicketRedemption[]
  createdAt    DateTime           @default(now()) @map("created_at")
  updatedAt    DateTime           @updatedAt @map("updated_at")

  @@index([timeSlotId])
  @@index([ticketTypeId])
  @@index([orderId])
  @@index([eventId])
  @@map("ticket")
}

model TicketRedemption {
  id        String         @id @default(uuid())
  ticketId  String         @map("ticket_id")
  ticket    Ticket         @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  scannedAt DateTime       @default(now()) @map("scanned_at")
  scannedBy String?        @map("scanned_by")
  type      RedemptionType @default(entry) @map("type")
  user      User?          @relation(fields: [scannedBy], references: [id], onDelete: SetNull)
  remark    String?
  createdAt DateTime       @default(now()) @map("created_at")
  updatedAt DateTime       @updatedAt @map("updated_at")

  @@index([ticketId])
  @@index([scannedBy])
  @@map("ticket_redemption")
}

model Cart {
  id        String     @id @default(uuid())
  userId    String?    @map("user_id")
  eventId   String?    @map("event_id")
  status    CartStatus
  expiresAt DateTime   @map("expires_at")
  cartItems CartItem[]
  user      User?      @relation(fields: [userId], references: [id], onDelete: SetNull)
  event     Event?     @relation(fields: [eventId], references: [id], onDelete: SetNull)
  order     Order?
  createdAt DateTime   @default(now()) @map("created_at")
  updatedAt DateTime   @updatedAt @map("updated_at")

  @@index([userId])
  @@map("cart")
}

model CartItem {
  id           String     @id @default(uuid())
  cartId       String     @map("cart_id")
  timeSlotId   String     @map("time_slot_id")
  ticketTypeId String     @map("ticket_type_id")
  quantity     Int
  cart         Cart       @relation(fields: [cartId], references: [id], onDelete: Cascade)
  timeSlot     TimeSlot   @relation(fields: [timeSlotId], references: [id], onDelete: Cascade)
  ticketType   TicketType @relation(fields: [ticketTypeId], references: [id], onDelete: Cascade)
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")

  @@unique([cartId, timeSlotId, ticketTypeId])
  @@index([cartId])
  @@index([timeSlotId])
  @@index([ticketTypeId])
  @@map("cart_item")
}

model Order {
  id                 String        @id @default(uuid())
  userId             String        @map("user_id")
  eventId            String?       @map("event_id")
  cartId             String?       @unique @map("cart_id")
  status             OrderStatus
  totalAmount        Decimal       @map("total_amount")
  paymentMethod      String        @map("payment_method")
  transactionId      String        @map("transaction_id") // Payment gateway transaction ID
  paymentStatus      PaymentStatus @default(pending) @map("payment_status")
  user               User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  event              Event?        @relation("EventOrders", fields: [eventId], references: [id], onDelete: SetNull)
  cart               Cart?         @relation(fields: [cartId], references: [id])
  tickets            Ticket[]
  checkoutFormAnswer String?       @map("checkout_form_answer")
  orderedAt          DateTime      @map("ordered_at")
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")

  @@index([userId])
  @@index([eventId])
  @@index([cartId])
  @@map("order")
}

model EventPremiumUpgrade {
  id            String                    @id
  eventId       String                    @map("event_id")
  organizerId   String                    @map("organizer_id")
  premiumTierId String?                   @map("premium_tier_id")
  amount        Decimal
  status        EventPremiumUpgradeStatus @default(pending)
  paymentStatus PaymentStatus             @default(pending) @map("payment_status")
  transactionId String                    @map("transaction_id") // Payment gateway transaction ID
  metadata      Json? // For storing additional data like upgradeType, paymentMethod, etc.
  createdAt     DateTime                  @default(now()) @map("created_at")
  updatedAt     DateTime                  @updatedAt @map("updated_at")

  // Relations
  event       Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)
  organizer   Organizer    @relation(fields: [organizerId], references: [id], onDelete: Cascade)
  premiumTier PremiumTier? @relation(fields: [premiumTierId], references: [id], onDelete: SetNull)

  @@index([eventId])
  @@index([organizerId])
  @@index([premiumTierId])
  @@index([transactionId])
  @@map("event_premium_upgrade")
}

model EventModule {
  id      String @id @default(uuid())
  eventId String @unique @map("event_id")
  event   Event  @relation(fields: [eventId], references: [id], onDelete: Cascade)

  // Module enablement flags
  donationEnabled      Boolean @default(false) @map("donation_enabled")
  customPaymentEnabled Boolean @default(false) @map("custom_payment_enabled")

  // Relations to specific module content
  donationModule      DonationModule?
  customPaymentModule CustomPaymentModule?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("event_module")
}

// Module-specific models
model DonationModule {
  id            String      @id @default(uuid())
  eventModuleId String      @unique @map("event_module_id")
  eventModule   EventModule @relation(fields: [eventModuleId], references: [id], onDelete: Cascade)

  // Donation-specific fields
  minAmount       Decimal?
  description     Json?
  thankYouMessage String?
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relation to donations
  donations EventDonation[]

  @@map("donation_module")
}

model CustomPaymentModule {
  id            String      @id @default(uuid())
  eventModuleId String      @unique @map("event_module_id")
  eventModule   EventModule @relation(fields: [eventModuleId], references: [id], onDelete: Cascade)

  // Custom payment-specific fields
  chipsEnabled  Boolean  @default(false) @map("chips_enabled")
  stripeEnabled Boolean  @default(false) @map("stripe_enabled")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("custom_payment_module")
}

model EventDonation {
  id               String         @id @default(uuid())
  donationModuleId String         @map("donation_module_id")
  donationModule   DonationModule @relation(fields: [donationModuleId], references: [id])
  eventId          String?        @map("event_id")
  event            Event?         @relation(fields: [eventId], references: [id])
  name             String
  email            String
  phone            String?
  companyName      String?        @map("company_name")
  companyLogo      String?        @map("company_logo")
  message          String?
  amount           Decimal
  transactionId    String?        @map("transaction_id")
  paymentMethod    String?        @map("payment_method")
  status           DonationStatus @default(pending)
  paymentStatus    PaymentStatus  @default(pending) @map("payment_status")
  donatedAt        DateTime       @default(now()) @map("donated_at")
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")

  @@index([donationModuleId])
  @@index([eventId])
  @@map("event_donation")
}

//// Roadmap models

model RoadmapTask {
  id                String   @id @default(uuid())
  title             String
  description       String?
  status            String // "planned", "in-progress", "in-review", "completed"
  priority          String // "high", "medium", "low"
  category          String // "feature", "bug-fix", "enhancement", "infrastructure"
  estimatedTimeline String?  @map("estimated_timeline")
  order             Int      @default(0) // For sorting within columns
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  createdBy         String?  @map("created_by")
  user              User?    @relation(fields: [createdBy], references: [id])

  // Relationship with converted requests
  convertedFromRequest PublicRequest?

  @@map("roadmap_task")
}

model PublicRequest {
  id                String        @id @default(uuid())
  title             String
  description       String
  email             String?
  name              String?
  status            RequestStatus @default(pending)
  priority          String? // Set by admin during review
  category          String? // Set by admin during review
  adminNotes        String?       @map("admin_notes")
  convertedToTaskId String?       @unique @map("converted_to_task_id")
  convertedToTask   RoadmapTask?  @relation(fields: [convertedToTaskId], references: [id])
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @updatedAt @map("updated_at")
  reviewedBy        String?       @map("reviewed_by")
  reviewer          User?         @relation(fields: [reviewedBy], references: [id])

  @@map("public_request")
}

//// Enums

enum VerificationStatus {
  pending
  verified
  rejected
}

enum PayoutFrequency {
  weekly
  biweekly
  monthly
}

enum EventStatus {
  draft
  published
  cancelled
  sold_out
}

enum OrderStatus {
  hold // on checkout page
  pending
  completed
  cancelled
  refunded
  reserved // manual order
  void
}

enum TicketStatus {
  hold // on checkout page
  pending // pending payment
  purchased
  cancelled
  used
  reserved // manual order
  void // refunded & void
}

enum CartStatus {
  idle // default
  active // checkout page
  expired
  converted
  abandoned
}

enum EventPremiumUpgradeStatus {
  pending
  completed
  failed
  cancelled
}

enum PaymentStatus {
  pending
  paid
  cancelled
  refunded
  custom
}

enum RedemptionType {
  entry
  exit
  invalid
}

enum Visibility {
  public
  private
  unlisted
}

enum DonationStatus {
  pending
  completed
  cancelled
  refunded
  void
}

enum RequestStatus {
  pending
  reviewed
  approved
  rejected
  converted
}
