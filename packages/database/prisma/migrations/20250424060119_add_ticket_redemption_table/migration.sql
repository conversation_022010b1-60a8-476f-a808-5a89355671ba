-- CreateTable
CREATE TABLE "ticket_redemption" (
    "id" TEXT NOT NULL,
    "ticket_id" TEXT NOT NULL,
    "scanned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "scanned_by" TEXT,
    "valid" <PERSON><PERSON><PERSON>EAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ticket_redemption_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ticket_redemption_ticket_id_idx" ON "ticket_redemption"("ticket_id");

-- AddForeignKey
ALTER TABLE "ticket_redemption" ADD CONSTRAINT "ticket_redemption_ticket_id_fkey" FOREIGN KEY ("ticket_id") REFERENCES "ticket"("id") ON DELETE CASCADE ON UPDATE CASCADE;
