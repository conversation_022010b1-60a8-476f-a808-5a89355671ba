-- AlterTable
ALTER TABLE "event_module" ADD COLUMN     "custom_payment_enabled" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "custom_payment_module" (
    "id" TEXT NOT NULL,
    "event_module_id" TEXT NOT NULL,
    "chips_enabled" BOOLEAN NOT NULL DEFAULT false,
    "stripe_enabled" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "custom_payment_module_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "custom_payment_module_event_module_id_key" ON "custom_payment_module"("event_module_id");

-- AddForeign<PERSON>ey
ALTER TABLE "custom_payment_module" ADD CONSTRAINT "custom_payment_module_event_module_id_fkey" FOREIGN KEY ("event_module_id") REFERENCES "event_module"("id") ON DELETE CASCADE ON UPDATE CASCADE;
