-- CreateEnum
CREATE TYPE "RequestStatus" AS ENUM ('pending', 'reviewed', 'approved', 'rejected', 'converted');

-- CreateTable
CREATE TABLE "roadmap_task" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL,
    "priority" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "estimated_timeline" TEXT,
    "order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "created_by" TEXT,

    CONSTRAINT "roadmap_task_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public_request" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "email" TEXT,
    "name" TEXT,
    "status" "RequestStatus" NOT NULL DEFAULT 'pending',
    "priority" TEXT,
    "category" TEXT,
    "admin_notes" TEXT,
    "converted_to_task_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "reviewed_by" TEXT,

    CONSTRAINT "public_request_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "public_request_converted_to_task_id_key" ON "public_request"("converted_to_task_id");

-- AddForeignKey
ALTER TABLE "roadmap_task" ADD CONSTRAINT "roadmap_task_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public_request" ADD CONSTRAINT "public_request_converted_to_task_id_fkey" FOREIGN KEY ("converted_to_task_id") REFERENCES "roadmap_task"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public_request" ADD CONSTRAINT "public_request_reviewed_by_fkey" FOREIGN KEY ("reviewed_by") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;
