/*
  Warnings:

  - The `payment_status` column on the `order` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('pending', 'paid', 'cancelled', 'refunded', 'custom');

-- AlterEnum
ALTER TYPE "CartStatus" ADD VALUE 'idle';

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "OrderStatus" ADD VALUE 'hold';
ALTER TYPE "OrderStatus" ADD VALUE 'void';

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "TicketStatus" ADD VALUE 'hold';
ALTER TYPE "TicketStatus" ADD VALUE 'pending';
ALTER TYPE "TicketStatus" ADD VALUE 'void';

-- AlterTable
ALTER TABLE "order" DROP COLUMN "payment_status",
ADD COLUMN     "payment_status" "PaymentStatus" NOT NULL DEFAULT 'pending';
