-- CreateTable
CREATE TABLE "event_module" (
    "id" TEXT NOT NULL,
    "event_id" TEXT NOT NULL,
    "donation_enabled" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "event_module_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "donation_module" (
    "id" TEXT NOT NULL,
    "event_module_id" TEXT NOT NULL,
    "minAmount" DECIMAL(65,30),
    "description" TEXT,
    "content" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "donation_module_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "event_module_event_id_key" ON "event_module"("event_id");

-- CreateIndex
CREATE UNIQUE INDEX "donation_module_event_module_id_key" ON "donation_module"("event_module_id");

-- AddForeignKey
ALTER TABLE "event_module" ADD CONSTRAINT "event_module_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "event"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "donation_module" ADD CONSTRAINT "donation_module_event_module_id_fkey" FOREIGN KEY ("event_module_id") REFERENCES "event_module"("id") ON DELETE CASCADE ON UPDATE CASCADE;
