-- CreateEnum
CREATE TYPE "DonationStatus" AS ENUM ('pending', 'completed', 'cancelled', 'refunded', 'void');

-- CreateTable
CREATE TABLE "event_donation" (
    "id" TEXT NOT NULL,
    "donation_module_id" TEXT NOT NULL,
    "event_id" TEXT,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "company_name" TEXT,
    "company_logo" TEXT,
    "message" TEXT,
    "amount" DECIMAL(65,30) NOT NULL,
    "transaction_id" TEXT,
    "payment_method" TEXT,
    "status" "DonationStatus" NOT NULL DEFAULT 'pending',
    "payment_status" "PaymentStatus" NOT NULL DEFAULT 'pending',
    "donated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "event_donation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "event_donation_donation_module_id_idx" ON "event_donation"("donation_module_id");

-- CreateIndex
CREATE INDEX "event_donation_event_id_idx" ON "event_donation"("event_id");

-- AddForeignKey
ALTER TABLE "event_donation" ADD CONSTRAINT "event_donation_donation_module_id_fkey" FOREIGN KEY ("donation_module_id") REFERENCES "donation_module"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "event_donation" ADD CONSTRAINT "event_donation_event_id_fkey" FOREIGN KEY ("event_id") REFERENCES "event"("id") ON DELETE SET NULL ON UPDATE CASCADE;
