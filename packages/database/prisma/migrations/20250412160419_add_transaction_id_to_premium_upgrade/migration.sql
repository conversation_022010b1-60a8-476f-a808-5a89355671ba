/*
  Warnings:

  - Added the required column `transaction_id` to the `event_premium_upgrade` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "event_premium_upgrade" ADD COLUMN     "payment_status" "PaymentStatus" NOT NULL DEFAULT 'pending',
ADD COLUMN     "transaction_id" TEXT NOT NULL;

-- CreateIndex
CREATE INDEX "event_premium_upgrade_transaction_id_idx" ON "event_premium_upgrade"("transaction_id");
