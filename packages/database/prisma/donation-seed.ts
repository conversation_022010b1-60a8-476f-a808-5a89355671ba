import type { PaymentStatus } from '@prisma/client';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds sample donations for a specific event
 */
async function seedDonationsForEvent(eventId: string, count: number) {
  process.stdout.write(`Seeding ${count} donations for event ${eventId}...\n`);

  try {
    // First, find the event and its donation module
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        eventModule: {
          include: {
            donationModule: true,
          },
        },
      },
    });

    if (!event) {
      process.stderr.write(`Event with ID ${eventId} not found\n`);
      return;
    }

    if (!event.eventModule || !event.eventModule.donationEnabled) {
      process.stderr.write(
        `Event ${eventId} does not have donation module enabled\n`
      );
      return;
    }

    const donationModuleId = event.eventModule.donationModule?.id;
    if (!donationModuleId) {
      process.stderr.write(`No donation module found for event ${eventId}\n`);
      return;
    }

    // Generate random donations
    const donationStatuses = ['completed', 'pending', 'cancelled'] as const;
    const paymentMethods = ['credit_card', 'paypal', 'bank_transfer'];
    const donationAmounts = [10, 25, 50, 100, 250, 500, 1000];

    const donations = [];

    for (let i = 0; i < count; i++) {
      // Generate random data for each donation
      const status =
        donationStatuses[Math.floor(Math.random() * donationStatuses.length)];
      let paymentStatus: PaymentStatus;

      if (status === 'completed') {
        paymentStatus = 'paid';
      } else if (status === 'pending') {
        paymentStatus = 'pending';
      } else {
        paymentStatus = 'cancelled';
      }
      const amount =
        donationAmounts[Math.floor(Math.random() * donationAmounts.length)];
      const paymentMethod =
        paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

      // Create random date within the last 30 days
      const donatedAt = new Date();
      donatedAt.setDate(donatedAt.getDate() - Math.floor(Math.random() * 30));

      // Create the donation
      const donation = await prisma.eventDonation.create({
        data: {
          donationModuleId,
          eventId,
          name: `Donor ${i + 1}`,
          email: `donor${i + 1}@example.com`,
          companyName: Math.random() > 0.7 ? `Company ${i + 1}` : undefined,
          message:
            Math.random() > 0.5
              ? `Thank you for organizing this event! This is donation #${i + 1}.`
              : undefined,
          amount,
          transactionId: `tx_${Date.now()}_${i}`,
          paymentMethod,
          status,
          paymentStatus,
          donatedAt,
        },
      });

      donations.push(donation);
    }

    process.stdout.write(
      `Successfully created ${donations.length} donations for event ${eventId}\n`
    );
    return donations;
  } catch (error) {
    process.stderr.write(`Error seeding donations: ${error}\n`);
    throw error;
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  // Seed donations for the specified event ID
  seedDonationsForEvent('eb5bed10-d1f3-408d-919f-d5aaacd1ea26', 20)
    .catch((e) => {
      process.stderr.write(`${e}\n`);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
} else {
  // Export for use in other seed files
  module.exports = { seedDonationsForEvent };
}
