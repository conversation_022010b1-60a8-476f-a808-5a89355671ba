import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * This seeder creates EventModule records for all existing events
 * and enables the donation module for each event.
 */
async function seedEventModules() {
  process.stdout.write('Starting event module seed...');

  try {
    // Get all events that don't already have an EventModule
    const events = await prisma.event.findMany({
      where: {
        eventModule: null,
      },
      select: {
        id: true,
      },
    });

    process.stdout.write(`Found ${events.length} events without modules`);

    if (events.length === 0) {
      process.stdout.write('No events need module setup. Exiting.');
      return;
    }

    // Create event modules with donation enabled for each event
    const eventModules = await Promise.all(
      events.map(async (event) => {
        // Create the EventModule with donationEnabled set to true
        const eventModule = await prisma.eventModule.create({
          data: {
            eventId: event.id,
            donationEnabled: true,
            // Create the associated DonationModule
            donationModule: {
              create: {
                minAmount: 100, // Default minimum donation amount
                description: 'Support this event with your donation',
              },
            },
          },
          include: {
            donationModule: true,
          },
        });
        return eventModule;
      })
    );

    process.stdout.write(
      `Successfully created ${eventModules.length} event modules with donation enabled`
    );
  } catch (error) {
    console.error('Error seeding event modules:', error);
    throw error;
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedEventModules()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
} else {
  // Export for use in other seed files
  module.exports = { seedEventModules };
}
