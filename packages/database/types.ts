import { Prisma } from '@prisma/client';
export * from '@prisma/client';

export const MAX_ROWS = 10;

export type Pagination = {
  total: number;
  pageCount: number;
  currentPage: number;
};

// Shared selection and type for Users API responses
// Use Prisma.validator to ensure type safety and single source of truth across API and app

export const userWithOrders = Prisma.validator<Prisma.UserFindManyArgs>()({
  select: {
    id: true,
    name: true,
    firstName: true,
    lastName: true,
    email: true,
    phone: true,
    _count: {
      select: {
        orders: true,
      },
    },
    orders: {
      include: {
        tickets: {
          include: {
            event: true,
            ticketType: true,
          },
        },
      },
    },
  },
});

export type UserWithOrders = Prisma.UserGetPayload<typeof userWithOrders>;

// Helper to apply organizer-specific filter for the _count.orders in selections
export const userWithOrdersSelect = (organizerId?: string | null) =>
  ({
    ...userWithOrders.select,
    _count: {
      select: {
        orders: {
          where: {
            tickets: {
              some: {
                event: {
                  organizerId: organizerId ?? undefined,
                },
              },
            },
          },
        },
      },
    },
  }) as const;
