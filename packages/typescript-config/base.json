{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": false, "isolatedModules": true, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022", "strictNullChecks": true}}