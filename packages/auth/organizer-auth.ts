import { expo } from '@better-auth/expo';
import { database } from '@repo/database';
import { sendEmail, sendOrganizerAccountConfirmationEmail } from '@repo/email';
import { log } from '@repo/observability/log';
import { type BetterAuthOptions, betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { nextCookies } from 'better-auth/next-js';
import { admin, emailOTP, magicLink, organization } from 'better-auth/plugins';
import {
  ac,
  adminRole,
  organizerRole,
  ownerRole,
  superAdminRole,
} from './permissions';

const organizerAuthOptions = {
  user: {
    additionalFields: {
      firstName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      lastName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      phone: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      dob: {
        type: 'date',
        required: false,
        defaultValue: null,
        input: true,
      },
    },
  },
  emailVerification: {
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      try {
        await sendOrganizerAccountConfirmationEmail(user.email, url);
      } catch (error) {
        log.error(`Error sending organizer confirmation email: ${user.email}`, {
          error,
        });
      }
    },
  },
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  trustedOrigins: [
    'http://localhost:3000',
    'https://app.ticketcare.my',
    'https://api.ticketcare.my',
  ],
  database: prismaAdapter(database, {
    provider: 'postgresql',
  }),
  session: {
    cookieCache: {
      enabled: true,
    },
    cookieName: 'organizer-auth.session_token', // Organizer-specific cookie name
    additionalFields: {
      activeOrganizationName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: false,
      },
      organizerId: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: false,
      },
    },
  },
  advanced: {
    defaultCookieAttributes: {
      sameSite: 'lax',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
    },
    // Disable cross-subdomain cookies for application separation
    crossSubDomainCookies: {
      enabled: false,
    },
  },
  plugins: [
    magicLink(),
    emailOTP({
      async sendVerificationOTP({ email, otp, type }) {
        if (type === 'sign-in') {
          try {
            await sendEmail({
              to: email,
              subject: 'Your TicketCARE Organizer Verification Code',
              text: `Your verification code is: ${otp}. Enter this code to sign in to your TicketCARE organizer account.`,
              html: `
                <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Sign in to your organizer account</h2>
                  <p>Enter the verification code below to sign in to your TicketCARE organizer account:</p>
                  <div style="background-color: #F3F4F6; padding: 16px; border-radius: 4px; text-align: center; margin: 16px 0;">
                    <span style="font-size: 32px; font-weight: bold; letter-spacing: 8px;">${otp}</span>
                  </div>
                  <p>This code will expire in 10 minutes.</p>
                </div>
              `,
            });
          } catch (error) {
            log.error('Error sending organizer OTP email', { error, email });
            throw error;
          }
        }
      },
    }),
    admin({
      defaultRole: 'customer', // Will be overridden by organization membership
      ac,
      roles: {
        superAdmin: superAdminRole,
        admin: adminRole,
      },
      adminRoles: ['super-admin', 'admin'],
    }),
    organization({
      ac,
      roles: { owner: ownerRole, organizer: organizerRole },
    }),
    expo(),
    nextCookies(),
  ],
  hooks: {
    user: {
      create: {
        after: async ({ user }) => {
          try {
            // Auto-assign organizer users to default organization
            const defaultOrg = await database.organization.upsert({
              where: { slug: 'ticket-care' },
              update: {},
              create: { name: 'TicketCARE', slug: 'ticket-care' },
            });

            const member = await database.member.findFirst({
              where: {
                organizationId: defaultOrg.id,
                userId: user.id,
              },
            });

            if (!member) {
              await database.member.create({
                data: {
                  organizationId: defaultOrg.id,
                  userId: user.id,
                  role: 'organizer',
                },
              });
            }
          } catch (error) {
            log.error('organizer user.create.after failed', {
              userId: user.id,
              error,
            });
          }
        },
      },
    },
    session: {
      create: {
        before: async (context) => {
          try {
            const organizations = await database.organization.findMany({
              where: {
                members: {
                  some: {
                    userId: context.userId,
                  },
                },
              },
            });

            const organizer = await database.organizer.findUnique({
              where: {
                userId: context.userId,
              },
            });

            return {
              data: {
                ...context,
                activeOrganizationId: organizations?.[0]?.id,
                activeOrganizationName: organizations?.[0]?.name,
                organizerId: organizer?.id,
              },
            };
          } catch (error) {
            console.error('Error in organizer session creation:', error);
            return {
              data: context,
            };
          }
        },
      },
    },
  },
  onAPIError: {
    throw: true,
    onError: (error) => {
      console.error('Organizer auth error:', error);
    },
    errorURL: '/auth/error',
  },
} satisfies BetterAuthOptions;

export const organizerAuth = betterAuth(organizerAuthOptions);

export type OrganizerAuth = typeof organizerAuth.$Infer.Session;
export type OrganizerUser = typeof organizerAuth.$Infer.Session.user;
export type OrganizerSession = typeof organizerAuth.$Infer.Session.session;
