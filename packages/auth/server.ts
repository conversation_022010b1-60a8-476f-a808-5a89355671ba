import { expo } from '@better-auth/expo';
import { database } from '@repo/database';
import { sendEmail, sendOrganizerAccountConfirmationEmail } from '@repo/email';
import { log } from '@repo/observability/log';
import { type BetterAuthOptions, betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { nextCookies } from 'better-auth/next-js';
import { admin, emailOTP, magicLink, organization } from 'better-auth/plugins';
import {
  ac,
  adminRole,
  customerRole,
  organizerRole,
  ownerRole,
  superAdminRole,
} from './permissions';

const option = {
  user: {
    additionalFields: {
      firstName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      lastName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      phone: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      dob: {
        type: 'date',
        required: false,
        defaultValue: null,
        input: true,
      },
    },
  },
  emailVerification: {
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      try {
        await sendOrganizerAccountConfirmationEmail(user.email, url);
      } catch (error) {
        log.error(`Error sending organizer confirmation email: ${user.email}`, {
          error,
        });
      }
    },
  },
  emailAndPassword: {
    enabled: true,
    // autoSignIn: true,
    requireEmailVerification: true,
  },
  database: prismaAdapter(database, {
    provider: 'postgresql',
  }),
  session: {
    cookieCache: {
      enabled: true,
    },
    additionalFields: {
      activeOrganizationName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: false,
      },
      organizerId: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: false,
      },
    },
  },
  advanced: {
    defaultCookieAttributes: {
      sameSite: 'lax',
      // partitioned: true, // this breaks localhost
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
    },
    // Share auth cookies across subdomains in production so
    // app.ticketcare.my and api.ticketcare.my see the same session
    crossSubDomainCookies: {
      enabled: process.env.NODE_ENV === 'production',
      // Use the registrable domain so all first-party subdomains share cookies
      // e.g. .ticketcare.my will apply to app.ticketcare.my and api.ticketcare.my
      domain: '.ticketcare.my',
    },
  },
  // Add trusted origins for Expo deep linking and cross-domain requests
  trustedOrigins: [
    'http://localhost:8081', // Expo web
    'http://localhost:3000', // App domain
    'http://localhost:3001', // API domain
    'http://localhost:3002', // Web domain
    'http://127.0.0.1:3000', // App domain alternative
    'http://127.0.0.1:3001', // API domain alternative
    'http://127.0.0.1:3002', // Web domain alternative
    'https://api.ticketcare.my', // prod domain
    'https://app.ticketcare.my', // prod domain
    'https://www.ticketcare.my', // prod domain
    'https://ticketcare.my', // prod domain
    'https://ticketcare-api.vercel.app', // staging domain
    'https://ticketcare-app.vercel.app', // staging domain
    'https://ticketcare-web.vercel.app', // staging domain
    'ticketcare://', // Expo app scheme
    'exp://', // Expo development scheme
    'exp+ticketcare://', // Expo development scheme with app name
  ],
  plugins: [
    magicLink({
      sendMagicLink: async ({ email, token, url }, _request) => {
        log.info('sending magic link to', { email, token, url });

        // Send email using the new email service
        try {
          await sendEmail({
            to: email,
            subject: 'Your Magic Link',
            text: `Click this link to sign in: ${url}`,
            html: `
              <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Sign in to your account</h2>
                <p>Click the button below to sign in to your account:</p>
                <a href="${url}" style="display: inline-block; background-color: #4F46E5; color: white; font-weight: bold; padding: 12px 24px; border-radius: 4px; text-decoration: none; margin: 16px 0;">
                  Sign In
                </a>
                <p style="color: #6B7280; font-size: 14px; margin-top: 24px;">
                  If you didn't request this email, you can safely ignore it.
                </p>
              </div>
            `,
          });
          log.info('Magic link email sent successfully', { email });
        } catch (error) {
          log.error('Failed to send magic link email', { error, email });
        }
      },
    }),
    emailOTP({
      async sendVerificationOTP({ email, otp, type }) {
        if (type === 'sign-in') {
          // Send the OTP for sign in
          try {
            await sendEmail({
              to: email,
              subject: 'Your TicketCARE Verification Code',
              text: `Your verification code is: ${otp}. Enter this code to sign in to your TicketCARE account.`,
              html: `
                <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Sign in to your account</h2>
                  <p>Enter the verification code below to sign in to your TicketCARE account:</p>
                  <div style="background-color: #F3F4F6; padding: 16px; border-radius: 4px; text-align: center; margin: 16px 0;">
                    <span style="font-size: 32px; font-weight: bold; letter-spacing: 8px;">${otp}</span>
                  </div>
                  <p>This code will expire in 10 minutes.</p>
                  <p style="color: #6B7280; font-size: 14px; margin-top: 24px;">
                    If you didn't request this code, you can safely ignore this email.
                  </p>
                </div>
              `,
            });
            log.info('OTP sign-in email sent successfully', { email });
          } catch (error) {
            log.error('Failed to send OTP sign-in email', { error, email });
          }
        } else if (type === 'email-verification') {
          // Send the OTP for email verification
          try {
            await sendEmail({
              to: email,
              subject: 'Verify Your TicketCARE Email',
              text: `Your verification code is: ${otp}. Enter this code to verify your email address.`,
              html: `
                <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Verify your email address</h2>
                  <p>Enter the verification code below to verify your email address:</p>
                  <div style="background-color: #F3F4F6; padding: 16px; border-radius: 4px; text-align: center; margin: 16px 0;">
                    <span style="font-size: 32px; font-weight: bold; letter-spacing: 8px;">${otp}</span>
                  </div>
                  <p>This code will expire in 10 minutes.</p>
                  <p style="color: #6B7280; font-size: 14px; margin-top: 24px;">
                    If you didn't request this code, you can safely ignore this email.
                  </p>
                </div>
              `,
            });
            log.info('Email verification OTP sent successfully', { email });
          } catch (error) {
            log.error('Failed to send email verification OTP', {
              error,
              email,
            });
          }
        } else {
          // Send the OTP for password reset
          try {
            await sendEmail({
              to: email,
              subject: 'Reset Your TicketCARE Password',
              text: `Your verification code is: ${otp}. Enter this code to reset your password.`,
              html: `
                <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Reset your password</h2>
                  <p>Enter the verification code below to reset your password:</p>
                  <div style="background-color: #F3F4F6; padding: 16px; border-radius: 4px; text-align: center; margin: 16px 0;">
                    <span style="font-size: 32px; font-weight: bold; letter-spacing: 8px;">${otp}</span>
                  </div>
                  <p>This code will expire in 10 minutes.</p>
                  <p style="color: #6B7280; font-size: 14px; margin-top: 24px;">
                    If you didn't request this code, you can safely ignore this email.
                  </p>
                </div>
              `,
            });
            log.info('Password reset OTP sent successfully', { email });
          } catch (error) {
            log.error('Failed to send password reset OTP', { error, email });
          }
        }
      },
    }),
    // stripePlugin({
    //   stripeClient: stripe,
    //   stripeWebhookSecret: keys().STRIPE_WEBHOOK_SECRET ?? '',
    //   createCustomerOnSignUp: false,
    //   // TODO: test auto create customer
    //   // onCustomerCreate: async ({ customer, stripeCustomer, user }, request) => {
    //   //   console.log(
    //   //     `Customer ${customer.id}, Stripe Customer ${stripeCustomer.id} created for user ${user.id}`
    //   //   );

    //   //   // TODO: create ticket, etc
    //   // },
    //   // getCustomerCreateParams: async ({ user, session }, request) => {
    //   //   // Customize the Stripe customer creation parameters
    //   //   return {
    //   //     metadata: {
    //   //       referralSource: user.metadata?.referralSource,
    //   //     },
    //   //   };
    //   // },
    // }),
    admin({
      defaultRole: 'customer',
      ac,
      roles: {
        superAdmin: superAdminRole,
        admin: adminRole,
        customer: customerRole,
      },
      adminRoles: ['super-admin', 'admin'],
    }),
    organization({
      ac,
      roles: { owner: ownerRole, organizer: organizerRole },
    }),
    expo(),
    nextCookies(),
  ],
  databaseHooks: {
    user: {
      create: {
        after: async (user) => {
          // Best-effort intent-based assignment at user creation time
          // Note: account records should typically exist, but if not yet available,
          // the session.create.before fallback will still handle assignment.
          try {
            // If user already has memberships, skip
            const existingMembership = await database.member.findFirst({
              where: { userId: user.id },
            });
            if (existingMembership) return;

            // Determine organizer intent
            const accounts = await database.account.findMany({
              where: { userId: user.id },
              select: { providerId: true, password: true },
            });

            const hasPasswordAccount = (accounts || []).some(
              (a) => a.password !== null && a.password !== undefined
            );
            const providerIds = (accounts || []).map((a) => a.providerId);
            const credentialsProviders = new Set(['credentials', 'credential']);
            const hasCredentialsProvider = providerIds.some((p) =>
              credentialsProviders.has((p || '').toLowerCase())
            );

            const isOrganizerIntent =
              hasPasswordAccount || hasCredentialsProvider;
            if (!isOrganizerIntent) return;

            // Upsert default organization
            const defaultOrg = await database.organization.upsert({
              where: { slug: 'ticket-care' },
              update: {},
              create: { name: 'TicketCARE', slug: 'ticket-care' },
            });

            // Ensure membership exists
            const member = await database.member.findFirst({
              where: { organizationId: defaultOrg.id, userId: user.id },
            });
            if (!member) {
              await database.member.create({
                data: {
                  organizationId: defaultOrg.id,
                  userId: user.id,
                  role: 'organizer',
                },
              });
            }
          } catch (error) {
            log.error('user.create.after organizer assignment failed', {
              userId: user.id,
              error,
            });
          }
        },
      },
    },
    session: {
      create: {
        before: async (context) => {
          try {
            let organizations = await database.organization.findMany({
              where: {
                members: {
                  some: {
                    userId: context.userId,
                  },
                },
              },
            });

            const organizer = await database.organizer.findUnique({
              where: {
                userId: context.userId,
              },
            });

            // Fallback safety net: if user has NO org memberships yet, determine intent
            // and attach to default organization 'ticket-care' for organizer sign-ups.
            if (!organizations || organizations.length === 0) {
              try {
                // Determine intent: treat credentials/email-password as organizer flow,
                // magic link remains customer-only. We infer via Account.providerId.
                const accounts = await database.account.findMany({
                  where: { userId: context.userId },
                  select: { providerId: true, password: true },
                });

                // Treat organizer sign-up as accounts with a password (credentials flow)
                // OR explicit credentials provider. Do NOT rely on 'email' which could be magic link.
                const hasPasswordAccount = (accounts || []).some(
                  (a) => a.password !== null && a.password !== undefined
                );
                const providerIds = (accounts || []).map((a) => a.providerId);
                const credentialsProviders = new Set([
                  'credentials',
                  'credential',
                ]);
                const hasCredentialsProvider = providerIds.some((p) =>
                  credentialsProviders.has((p || '').toLowerCase())
                );

                const isOrganizerIntent =
                  hasPasswordAccount || hasCredentialsProvider;

                if (isOrganizerIntent) {
                  // Upsert default org
                  const defaultOrg = await database.organization.upsert({
                    where: { slug: 'ticket-care' },
                    update: {},
                    create: { name: 'TicketCARE', slug: 'ticket-care' },
                  });

                  // Ensure membership exists (idempotent)
                  const existingMember = await database.member.findFirst({
                    where: {
                      organizationId: defaultOrg.id,
                      userId: context.userId,
                    },
                  });

                  if (!existingMember) {
                    await database.member.create({
                      data: {
                        organizationId: defaultOrg.id,
                        userId: context.userId,
                        role: 'organizer',
                      },
                    });
                  }

                  // Refresh organizations for session fields
                  organizations = await database.organization.findMany({
                    where: {
                      members: { some: { userId: context.userId } },
                    },
                  });
                }
              } catch (intentError) {
                log.error('Fallback org assignment failed', {
                  userId: context.userId,
                  error: intentError,
                });
              }
            }

            return {
              data: {
                ...context,
                activeOrganizationId: organizations?.[0]?.id,
                activeOrganizationName: organizations?.[0]?.name,
                organizerId: organizer?.id,
              },
            };
          } catch (error) {
            console.error('Error listing organizations:', error);

            return {
              data: context,
            };
          }
        },
      },
    },
  },
  onAPIError: {
    throw: true,
    onError: (error) => {
      console.error('Auth error:', error);
    },
    errorURL: '/auth/error',
  },
} satisfies BetterAuthOptions;

export const auth = betterAuth(option);

export type Auth = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;
export type Session = typeof auth.$Infer.Session.session;
