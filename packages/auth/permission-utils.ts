import type { Auth } from './server';

export interface AuthResult {
  session: Auth | null;
  user: Auth['user'] | null;
  isAuthenticated: boolean;
  organizerId?: string | null;
  activeOrganizationId?: string | null;
}

/**
 * Convenience helper to convert an Auth session into an AuthResult
 * to avoid repeating mapping code at call sites.
 */
export function toAuthResult(session: Auth | null): AuthResult {
  return {
    session,
    user: session?.user ?? null,
    isAuthenticated: !!session?.user,
    organizerId: session?.session?.organizerId ?? null,
    activeOrganizationId: session?.session?.activeOrganizationId ?? null,
  };
}

/**
 * Permission utility functions for role-based access control
 */

/**
 * Check if user is a super admin
 */
export function isSuperAdmin(authResult: AuthResult): boolean {
  return authResult.user?.role === 'super-admin';
}

/**
 * Check if user is an admin (super admin or admin)
 */
export function isAdmin(authResult: AuthResult): boolean {
  return (
    authResult.user?.role === 'super-admin' || authResult.user?.role === 'admin'
  );
}

/**
 * Check if user is an organizer
 */
export function isOrganizer(authResult: AuthResult): boolean {
  return !!authResult.organizerId;
}

/**
 * Get organizer filter for database queries
 * Super admins can see all data (returns undefined)
 * Regular organizers can only see their own data (returns their organizerId)
 * Non-organizers get null (no access to any data)
 */
export function getOrganizerFilter(
  authResult: AuthResult
): undefined | string | null {
  // Super admins see everything
  if (isSuperAdmin(authResult)) {
    return undefined;
  }

  // Regular organizers only see their own data
  if (isOrganizer(authResult)) {
    return authResult.organizerId;
  }

  // Non-organizers don't have access to organizer-specific data
  // Return null to indicate no access rather than undefined (which means "no filter")
  return null;
}

/**
 * Check if user can access a specific organizer's resources
 * Super admins can access everything
 * Organizers can only access their own resources
 */
export function canAccessOrganizer(
  authResult: AuthResult,
  targetOrganizerId: string
): boolean {
  // Super admins can access everything
  if (isSuperAdmin(authResult)) {
    return true;
  }

  // Organizers can only access their own resources
  return authResult.organizerId === targetOrganizerId;
}
