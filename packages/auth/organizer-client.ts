import {
  adminClient,
  inferAdditionalFields,
  magicLinkClient,
  organizationClient,
  emailOTPClient,
} from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import {
  adminRole,
  organizerRole,
  ownerRole,
  superAdminRole,
} from './permissions';
import { ac } from './permissions';
import type { organizerAuth } from './organizer-auth';

const organizerAuthClient = createAuthClient({
  plugins: [
    inferAdditionalFields<typeof organizerAuth>(),
    adminClient({
      ac,
      roles: {
        superAdmin: superAdminRole,
        admin: adminRole,
      },
    }),
    organizationClient({
      ac,
      roles: { owner: ownerRole, organizer: organizerRole },
    }),
    magicLinkClient(),
    emailOTPClient(),
  ],
});

export const {
  signIn: organizerSignIn,
  signUp: organizerSignUp,
  signOut: organizerSignOut,
  useSession: useOrganizerSession,
  admin: organizerAdmin,
  organization: organizerOrganization,
  useActiveOrganization: useActiveOrganizerOrganization,
  emailOtp: organizerEmailOtp,
} = organizerAuthClient;

export default organizerAuthClient;
