// import { getSessionCookie } from 'better-auth/cookies';
// import {
//   NextResponse,
//   type NextFetchEvent,
//   type NextMiddleware,
//   type NextRequest,
// } from 'next/server';

// export const authMiddleware = (
//   { isAdminPanel = false }: { isAdminPanel?: boolean },
//   next?: NextMiddleware
// ) => {
//   const action = (request: NextRequest, event: NextFetchEvent) => {
//     const pathname = request.nextUrl.pathname;

//     // // for APP
//     // if (isAdminPanel && pathname.startsWith('/dashboard')) {
//     //   // Only check auth for dashboard routes (not sure where using)
//     //   const sessionCookie = getSessionCookie(request);
//     //   if (!sessionCookie) {
//     //     return NextResponse.redirect(new URL('/sign-in', request.url));
//     //   }
//     // }
//     // for WEB
//     if (!isAdminPanel) {
//       const sessionCookie = getSessionCookie(request);

//       // go login if access /account without login
//       if (!sessionCookie && pathname.includes('/account')) {
//         return NextResponse.redirect(new URL('/auth/login', request.url));
//       }
//       // go account if access /auth after logged in
//       if (sessionCookie && pathname.includes('/auth')) {
//         return NextResponse.redirect(new URL('/account', request.url));
//       }
//     }

//     return next?.(request, event) ?? NextResponse.next();
//   };

//   return action;
// };

// export const config = {
//   matcher: ['/account/:path*', '/auth/:path*'],
// };
