{"name": "@repo/auth", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@better-auth/expo": "https://pkg.pr.new/better-auth/better-auth/@better-auth/expo@dac51d3", "@better-auth/stripe": "https://pkg.pr.new/better-auth/better-auth/@better-auth/stripe@dac51d3", "@t3-oss/env-nextjs": "^0.12.0", "better-auth": "https://pkg.pr.new/better-auth/better-auth@dac51d3", "next": "15.1.7", "next-themes": "^0.4.4", "react": "^19.0.0", "server-only": "^0.0.1", "usehooks-ts": "^3.1.1", "zod": "^3.24.2", "zustand": "^5.0.8"}, "devDependencies": {"@repo/database": "workspace:*", "@repo/payments": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4", "typescript": "^5.8.2"}}