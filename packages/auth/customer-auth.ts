import { database } from '@repo/database';
import { sendEmail } from '@repo/email';
import { log } from '@repo/observability/log';
import { type BetterAuthOptions, betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { nextCookies } from 'better-auth/next-js';
import { admin, emailOTP } from 'better-auth/plugins';
import {
  ac,
  customerRole,
} from './permissions';

const customerAuthOptions = {
  user: {
    additionalFields: {
      firstName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      lastName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      phone: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      dob: {
        type: 'date',
        required: false,
        defaultValue: null,
        input: true,
      },
    },
  },
  emailVerification: {
    sendOnSignUp: false, // Customers use OTP instead
    autoSignInAfterVerification: false,
  },
  emailAndPassword: {
    enabled: false, // Customers use OTP only
  },
  trustedOrigins: [
    'http://localhost:3001',
    'https://www.ticketcare.my',
    'https://api.ticketcare.my',
  ],
  database: prismaAdapter(database, {
    provider: 'postgresql',
  }),
  session: {
    cookieCache: {
      enabled: true,
    },
    cookieName: 'customer-auth.session_token', // Customer-specific cookie name
    additionalFields: {
      // Customers don't need organization fields
    },
  },
  advanced: {
    defaultCookieAttributes: {
      sameSite: 'lax',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
    },
    // Disable cross-subdomain cookies for application separation
    crossSubDomainCookies: {
      enabled: false,
    },
  },
  plugins: [
    emailOTP({
      async sendVerificationOTP({ email, otp, type }) {
        if (type === 'sign-in') {
          try {
            await sendEmail({
              to: email,
              subject: 'Your TicketCARE Verification Code',
              text: `Your verification code is: ${otp}. Enter this code to sign in to your TicketCARE account.`,
              html: `
                <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>Sign in to your account</h2>
                  <p>Enter the verification code below to sign in to your TicketCARE account:</p>
                  <div style="background-color: #F3F4F6; padding: 16px; border-radius: 4px; text-align: center; margin: 16px 0;">
                    <span style="font-size: 32px; font-weight: bold; letter-spacing: 8px;">${otp}</span>
                  </div>
                  <p>This code will expire in 10 minutes.</p>
                </div>
              `,
            });
          } catch (error) {
            log.error('Error sending customer OTP email', { error, email });
            throw error;
          }
        }
      },
    }),
    admin({
      defaultRole: 'customer',
      ac,
      roles: {
        customer: customerRole,
      },
      adminRoles: [], // No admin roles for customer auth
    }),
    nextCookies(),
  ],
  hooks: {
    user: {
      create: {
        after: async ({ user }) => {
          try {
            // Ensure customer role is set
            if (user.role !== 'customer') {
              await database.user.update({
                where: { id: user.id },
                data: { role: 'customer' },
              });
            }
          } catch (error) {
            log.error('customer user.create.after failed', {
              userId: user.id,
              error,
            });
          }
        },
      },
    },
  },
  onAPIError: {
    throw: true,
    onError: (error) => {
      console.error('Customer auth error:', error);
    },
    errorURL: '/auth/error',
  },
} satisfies BetterAuthOptions;

export const customerAuth = betterAuth(customerAuthOptions);

export type CustomerAuth = typeof customerAuth.$Infer.Session;
export type CustomerUser = typeof customerAuth.$Infer.Session.user;
export type CustomerSession = typeof customerAuth.$Infer.Session.session;
