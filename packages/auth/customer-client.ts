import {
  adminClient,
  inferAdditionalFields,
  emailOTPClient,
} from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import {
  customerRole,
} from './permissions';
import { ac } from './permissions';
import type { customerAuth } from './customer-auth';

const customerAuthClient = createAuthClient({
  plugins: [
    inferAdditionalFields<typeof customerAuth>(),
    adminClient({
      ac,
      roles: {
        customer: customerRole,
      },
    }),
    emailOTPClient(),
  ],
});

export const {
  signIn: customerSignIn,
  signUp: customerSignUp,
  signOut: customerSignOut,
  useSession: useCustomerSession,
  admin: customerAdmin,
  emailOtp: customerEmailOtp,
} = customerAuthClient;

export default customerAuthClient;
