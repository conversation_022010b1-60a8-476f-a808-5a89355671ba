'use client';

import { PayoutFrequency } from '@repo/database/types';
import { z } from 'zod';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const organizerOnboardingSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  slug: z.string().optional().nullable(), // Slug is now auto-generated, so it's optional
  description: z.string().optional().nullable(),
  website: z
    .string()
    .url({ message: 'Invalid website URL' })
    .optional()
    .or(z.literal('')),
  address: z
    .string()
    .min(5, { message: 'Address is required' })
    .optional()
    .or(z.literal('')),
  picName: z
    .string()
    .min(2, { message: "Person in charge's name is required" })
    .optional()
    .or(z.literal('')),
  picTitle: z
    .string()
    .min(2, { message: "Person in charge's title is required" })
    .optional()
    .or(z.literal('')),
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().min(10, { message: 'Phone number is required' }),
  whatsapp: z
    .string()
    .min(10, { message: 'WhatsApp number is required' })
    .optional()
    .or(z.literal('')),
  facebook: z
    .string()
    .url({ message: 'Invalid Facebook URL' })
    .optional()
    .or(z.literal('')),
  twitter: z
    .string()
    .url({ message: 'Invalid Twitter URL' })
    .optional()
    .or(z.literal('')),
  instagram: z
    .string()
    .url({ message: 'Invalid Instagram URL' })
    .optional()
    .or(z.literal('')),
  youtube: z
    .string()
    .url({ message: 'Invalid YouTube URL' })
    .optional()
    .or(z.literal('')),
  tiktok: z
    .string()
    .url({ message: 'Invalid TikTok URL' })
    .optional()
    .or(z.literal('')),
  rednote: z
    .string()
    .url({ message: 'Invalid RedNote URL' })
    .optional()
    .or(z.literal('')),
  payoutFrequency: z
    .nativeEnum(PayoutFrequency, {
      required_error: 'Payout frequency is required',
    })
    .default('monthly'),
  commissionRate: z.coerce
    .number({
      required_error: 'Commission rate is required',
      invalid_type_error: 'Commission rate must be a number',
    })
    .min(0)
    .max(100)
    .default(0),
  emailNotifications: z.boolean().default(true),
  smsNotifications: z.boolean().default(false),
  pushNotifications: z.boolean().default(false),
});

// Step 1 onboarding: basic info
export const basicInfoSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  slug: z.string().optional().nullable(), // Slug is now auto-generated, so it's optional
  description: z.string().optional().nullable(),
  emailNotifications: z.boolean().default(true),
  smsNotifications: z.boolean().default(false),
});

// Step 2 onboarding: contact info
export const contactInfoSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().min(10, { message: 'Phone number is required' }),
  website: z
    .string()
    .url({ message: 'Invalid website URL' })
    .optional()
    .or(z.literal('')),
  address: z
    .string()
    .min(5, { message: 'Address is required' })
    .optional()
    .or(z.literal('')),
  picName: z
    .string()
    .min(2, { message: "Person in charge's name is required" })
    .optional()
    .or(z.literal('')),
  picTitle: z
    .string()
    .min(2, { message: "Person in charge's title is required" })
    .optional()
    .or(z.literal('')),
});

// Step 3 onboarding: social media
export const socialMediaSchema = z.object({
  whatsapp: z
    .string()
    .min(10, { message: 'WhatsApp number is required' })
    .optional()
    .or(z.literal('')),
  facebook: z
    .string()
    .url({ message: 'Invalid Facebook URL' })
    .optional()
    .or(z.literal('')),
  twitter: z
    .string()
    .url({ message: 'Invalid Twitter URL' })
    .optional()
    .or(z.literal('')),
  instagram: z
    .string()
    .url({ message: 'Invalid Instagram URL' })
    .optional()
    .or(z.literal('')),
  youtube: z
    .string()
    .url({ message: 'Invalid YouTube URL' })
    .optional()
    .or(z.literal('')),
  tiktok: z
    .string()
    .url({ message: 'Invalid TikTok URL' })
    .optional()
    .or(z.literal('')),
  rednote: z
    .string()
    .url({ message: 'Invalid RedNote URL' })
    .optional()
    .or(z.literal('')),
});

// Define types for use in the application
export type OrganizerOnboardingData = z.infer<typeof organizerOnboardingSchema>;
export type BasicInfoData = z.infer<typeof basicInfoSchema>;
export type ContactInfoData = z.infer<typeof contactInfoSchema>;
export type SocialMediaData = z.infer<typeof socialMediaSchema>;

interface OnboardingStore {
  // Form data
  formData: Partial<OrganizerOnboardingData>;

  // Step management
  currentStep: number;
  totalSteps: number;

  // Actions
  updateFormData: (data: Partial<OrganizerOnboardingData>) => void;
  setCurrentStep: (step: number) => void;
  reset: () => void;

  // Status
  isComplete: boolean;
  setComplete: (complete: boolean) => void;
}

// Initial form data with empty values
const initialFormData: Partial<OrganizerOnboardingData> = {
  name: '',
  slug: null, // Auto-generated, so set to null
  description: '',
  email: '',
  phone: '',
  payoutFrequency: PayoutFrequency.monthly,
  commissionRate: 0,
  emailNotifications: true,
  smsNotifications: false,
  pushNotifications: false,
};

// Create the store with persistence
export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set) => ({
      // Initial state
      formData: initialFormData,
      currentStep: 0,
      totalSteps: 4,
      isComplete: false,

      // Actions
      updateFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),

      setCurrentStep: (step) => set({ currentStep: step }),

      setComplete: (complete) => set({ isComplete: complete }),

      reset: () =>
        set({
          formData: initialFormData,
          currentStep: 0,
          isComplete: false,
        }),
    }),
    {
      name: 'onboarding-store',
    }
  )
);
