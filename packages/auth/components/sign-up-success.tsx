'use client';

import { MailCheck } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import Link from 'next/link';

interface SignUpSuccessProps {
  email?: string;
  nextHref?: string;
}

const SignUpSuccess = ({ email, nextHref = '/' }: SignUpSuccessProps) => {
  return (
    <Card className="mx-auto w-full max-w-md text-center">
      <CardHeader>
        <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100 text-emerald-700">
          <MailCheck className="h-6 w-6" />
        </div>
        <CardTitle>Check your email</CardTitle>
        <CardDescription>
          {email ? (
            <span>
              We sent a verification link to{' '}
              <span className="font-medium text-foreground">{email}</span>.
            </span>
          ) : (
            <span>
              We sent you a verification link. Please verify your email to
              continue.
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p className="text-muted-foreground text-sm">
            Didn’t get the email? Check your spam folder or try again in a few
            minutes.
          </p>
          <Button asChild className="w-full">
            <Link href={nextHref}>Return</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export { SignUpSuccess };
