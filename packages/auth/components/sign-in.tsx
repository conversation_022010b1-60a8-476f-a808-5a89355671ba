'use client';

import {
  <PERSON>,
  <PERSON>Off,
  LoaderCircle,
} from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { signIn } from '../client';

export const SignIn = () => {
  const [pending, setPending] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const form = useForm<{
    email: string;
    password: string;
  }>({
    defaultValues: {
      email: '',
      password: '',
    },
  });

  async function onSubmit(values: { email: string; password: string }) {
    let emailNotVerified = false;
    setPending(true);

    try {
      // const { error } = await signIn.magicLink({
      //   email: values.email,
      // });

      const { error } = await signIn.email(values, {
        onSuccess: () => {
          setPending(false);
          router.push('/');
        },
        // verify email before signin
        onError: (ctx) => {
          // flag the error
          if (ctx.error.status === 403) {
            emailNotVerified = true;
            throw ctx.error;
          }
        },
      });

      if (error) {
        console.error(error);
        throw error;
      }
    } catch (error) {
      setPending(false);
      console.error(error);

      if (emailNotVerified) {
        form.setError('root', {
          message: 'Please verify your email address',
        });
        return;
      }

      form.setError('root', {
        message: 'Invalid email or password',
      });
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Email"
                  disabled={pending}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Password"
                    disabled={pending}
                    {...field}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={pending}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={pending}>
          {pending ? (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Continue
        </Button>

        {form.formState.errors.root && (
          <p
            data-slot="form-message"
            className="text-destructive-foreground text-sm"
          >
            {form.formState.errors.root?.message}
          </p>
        )}

        <div className="caption mt-4 justify-items-center">
          <p>Don't have an account?</p>
          <Button type="button" asChild variant="link" className="block p-0">
            <Link href="/sign-up">Sign up as an organizer</Link>
          </Button>
        </div>
      </form>
    </Form>
  );
};
