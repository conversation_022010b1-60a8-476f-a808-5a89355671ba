'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import {
  type BasicInfoData,
  basicInfoSchema,
  useOnboardingStore,
} from '../../store/onboarding-store';

interface BasicInfoStepProps {
  onNextAction: () => void;
}

export function BasicInfoStep({ onNextAction }: BasicInfoStepProps) {
  const { formData, updateFormData } = useOnboardingStore();

  const form = useForm<BasicInfoData>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      name: formData.name || '',
      description: formData.description || '',
      emailNotifications: formData.emailNotifications || true,
      smsNotifications: formData.smsNotifications || false,
    },
  });

  const { isSubmitting } = form.formState;

  // // Update form when store data changes
  // useEffect(() => {
  //   form.reset({
  //     name: formData.name || '',
  //     description: formData.description || '',
  //     emailNotifications: formData.emailNotifications || true,
  //     smsNotifications: formData.smsNotifications || false,
  //   });
  // }, [form, formData]);

  function onSubmit(values: BasicInfoData) {
    updateFormData(values);
    onNextAction();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Organizer Name*</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your name or business name"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  This is how you'll be identified on the TicketCARE platform
                </FormDescription>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Brief description of your business"
                    className="resize-none"
                    maxLength={250}
                    disabled={isSubmitting}
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormDescription>
                  A brief description that will be displayed on your organizer
                  profile
                </FormDescription>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
