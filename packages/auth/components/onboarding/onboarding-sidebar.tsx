'use client';

import type * as React from 'react';

import { GalleryVerticalEnd } from '@repo/design-system/components/icons';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
} from '@repo/design-system/components/ui/sidebar';
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { useOnboardingStore } from '../../store/onboarding-store';
import { onboardingSteps } from './onboarding-stepper';

export function OnboardingSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { currentStep, setCurrentStep } = useOnboardingStore();

  // Convert 0-based index to 1-based step number for the stepper
  const stepperValue = currentStep + 1;

  // Handle step change from the stepper
  const handleStepChange = (step: number) => {
    // Convert 1-based step number back to 0-based index
    const targetStep = step - 1;

    // Only allow navigation to previous steps or the current step
    if (targetStep <= currentStep) {
      setCurrentStep(targetStep);
    }
  };

  return (
    <Sidebar variant="floating" {...props} className="p-2">
      <SidebarHeader>
        <div className="flex flex-row gap-0.5 px-2 py-4 leading-none">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <GalleryVerticalEnd className="size-4" />
          </div>
          <span className="ml-4 font-semibold text-lg">Onboarding</span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className="px-4">
          <Stepper
            value={stepperValue}
            onValueChange={handleStepChange}
            orientation="vertical"
          >
            {onboardingSteps.map(({ step, title }) => (
              <StepperItem
                key={step}
                step={step}
                className="relative not-last:flex-1 items-start"
                // Disable steps that are ahead of the current step
                disabled={step - 1 > currentStep}
              >
                <StepperTrigger className="items-start rounded pb-12 last:pb-0">
                  <StepperIndicator />
                  <div className="mt-0.5 space-y-0.5 px-2 text-left">
                    <StepperTitle>{title}</StepperTitle>
                  </div>
                </StepperTrigger>
                {step < onboardingSteps.length && (
                  <StepperSeparator className="-order-1 -translate-x-1/2 absolute inset-y-0 top-[calc(1.5rem+0.125rem)] left-4 m-0 group-data-[orientation=vertical]/stepper:h-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=horizontal]/stepper:w-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=horizontal]/stepper:flex-none" />
                )}
              </StepperItem>
            ))}
          </Stepper>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
