'use client';

import {
  Stepper,
  StepperIndicator,
  Stepper<PERSON><PERSON>,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { useOnboardingStore } from '../../store/onboarding-store';

export const onboardingSteps = [
  {
    step: 1,
    title: 'Basic Information',
    mobileTitle: 'Basic Info',
    description: 'Tell us about yourself and your event organizing activities',
  },
  {
    step: 2,
    title: 'Contact Information',
    mobileTitle: 'Contact',
    description: 'How can attendees reach you?',
  },
  {
    step: 3,
    title: 'Social Media',
    mobileTitle: 'Social',
    description: 'Connect your social media profiles to build trust',
  },
  {
    step: 4,
    title: 'Review & Submit',
    mobileTitle: 'Review',
    description: 'Review your organizer profile and join TicketCARE',
  },
];

export function OnboardingStepper() {
  const { currentStep, setCurrentStep } = useOnboardingStore();

  // Handle step change from the stepper
  const handleStepChange = (step: number) => {
    // Convert 1-based step number back to 0-based index
    const targetStep = step - 1;

    // Only allow navigation to previous steps or the current step
    if (targetStep <= currentStep) {
      setCurrentStep(targetStep);
    }
  };

  return (
    <Stepper
      value={currentStep + 1}
      onValueChange={handleStepChange}
      className="my-8 flex md:hidden"
    >
      {onboardingSteps.map(({ step, mobileTitle: title }) => (
        <StepperItem
          key={step}
          step={step}
          className="not-last:flex-1 max-md:items-start"
          // Disable steps that are ahead of the current step
          disabled={step - 1 > currentStep}
        >
          <StepperTrigger className="flex-1 flex-col rounded">
            <StepperIndicator />
            <div className="text-center md:text-left">
              <StepperTitle className="text-xs">{title}</StepperTitle>
            </div>
          </StepperTrigger>
          {step < onboardingSteps.length && (
            <StepperSeparator className="max-md:mt-3.5 md:mx-4" />
          )}
        </StepperItem>
      ))}
    </Stepper>
  );
}
