'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import {
  type ContactInfoData,
  contactInfoSchema,
  useOnboardingStore,
} from '../../store/onboarding-store';

interface ContactInfoStepProps {
  onNextAction: () => void;
  onBackAction: () => void;
}

export function ContactInfoStep({
  onNextAction,
  onBackAction,
}: ContactInfoStepProps) {
  const { formData, updateFormData } = useOnboardingStore();

  const form = useForm<ContactInfoData>({
    resolver: zodResolver(contactInfoSchema),
    defaultValues: {
      email: formData.email,
      phone: formData.phone,
      website: formData.website,
      address: formData.address,
      picName: formData.picName,
      picTitle: formData.picTitle,
    },
  });

  const { isSubmitting } = form.formState;

  // // Update form when store data changes
  // useEffect(() => {
  //   form.reset({
  //     email: formData.email,
  //     phone: formData.phone,
  //     website: formData.website,
  //     address: formData.address,
  //     picName: formData.picName,
  //     picTitle: formData.picTitle,
  //   });
  // }, [form, formData]);

  function onSubmit(values: ContactInfoData) {
    updateFormData(values);
    onNextAction();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Support Email Address*</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Phone Number*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="+60 12-345 6789"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://yourbusiness.com"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Business Address</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter your business address"
                    disabled={isSubmitting}
                    maxLength={250}
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="picName"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Person in Charge Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John Doe"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="picTitle"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Person in Charge Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Event Manager"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBackAction}>
            Back
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
