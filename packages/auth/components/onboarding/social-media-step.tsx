'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  type SocialMediaData,
  socialMediaSchema,
  useOnboardingStore,
} from '../../store/onboarding-store';

interface SocialMediaStepProps {
  onNextAction: () => void;
  onBackAction: () => void;
}

export function SocialMediaStep({
  onNextAction,
  onBackAction,
}: SocialMediaStepProps) {
  const { formData, updateFormData } = useOnboardingStore();

  const form = useForm<SocialMediaData>({
    resolver: zodResolver(socialMediaSchema),
    defaultValues: {
      whatsapp: formData.whatsapp,
      facebook: formData.facebook,
      twitter: formData.twitter,
      instagram: formData.instagram,
      youtube: formData.youtube,
      tiktok: formData.tiktok,
      rednote: formData.rednote,
    },
  });

  const { isSubmitting } = form.formState;

  // // Update form when store data changes
  // useEffect(() => {
  //   form.reset({
  //     whatsapp: formData.whatsapp,
  //     facebook: formData.facebook,
  //     twitter: formData.twitter,
  //     instagram: formData.instagram,
  //     youtube: formData.youtube,
  //     tiktok: formData.tiktok,
  //     rednote: formData.rednote,
  //   });
  // }, [form, formData]);

  function onSubmit(values: SocialMediaData) {
    updateFormData(values);
    onNextAction();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="whatsapp"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>WhatsApp Number</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="+60 12-345 6789"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="facebook"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Facebook URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://facebook.com/yourpage"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="twitter"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Twitter URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://twitter.com/youraccount"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="instagram"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Instagram URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://instagram.com/youraccount"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="youtube"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>YouTube URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://youtube.com/c/yourchannel"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tiktok"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>TikTok URL</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://tiktok.com/@youraccount"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="rednote"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>RedNote URL</FormLabel>
                <FormControl>
                  <Input
                    placeholder="https://rednote.com/youraccount"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBackAction}>
            Back
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
