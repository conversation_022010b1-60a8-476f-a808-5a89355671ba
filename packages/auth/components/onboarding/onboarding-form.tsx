'use client';

import {} from '@repo/design-system/components/ui/sidebar';

import {
  type OrganizerOnboardingData,
  useOnboardingStore,
} from '../../store/onboarding-store';
import { BasicInfoStep } from './basic-info-step';
import { ContactInfoStep } from './contact-info-step';
import { OnboardingStepper, onboardingSteps } from './onboarding-stepper';
import { SocialMediaStep } from './social-media-step';
import { SummaryStep } from './summary-step';

interface OnboardingFormProps {
  isSubmitting: boolean;
  onComplete?: (formData: Partial<OrganizerOnboardingData>) => Promise<void>;
  onError?: (error: unknown) => void;
}

export function OnboardingForm({
  isSubmitting,
  onComplete,
  onError,
}: OnboardingFormProps) {
  const { currentStep, setCurrentStep } = useOnboardingStore();

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  // Render the current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <BasicInfoStep onNextAction={handleNext} />;
      case 1:
        return (
          <ContactInfoStep
            onNextAction={handleNext}
            onBackAction={handleBack}
          />
        );
      case 2:
        return (
          <SocialMediaStep
            onNextAction={handleNext}
            onBackAction={handleBack}
          />
        );
      case 3:
        return (
          <SummaryStep
            isSubmitting={isSubmitting}
            onBackAction={handleBack}
            onComplete={onComplete}
            onError={onError}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <OnboardingStepper />

      <div className="flex flex-col text-left">
        <h1 className="font-semibold text-lg tracking-tight">
          {onboardingSteps[currentStep].title}
        </h1>
        <p className="text-muted-foreground text-sm">
          {onboardingSteps[currentStep].description}
        </p>
      </div>

      <div>{renderStepContent()}</div>
    </>
  );
}
