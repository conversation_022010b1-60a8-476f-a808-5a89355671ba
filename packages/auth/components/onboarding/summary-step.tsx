'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { log } from '@repo/observability/log';
import {
  type OrganizerOnboardingData,
  useOnboardingStore,
} from '../../store/onboarding-store';

interface SummaryStepProps {
  isSubmitting: boolean;
  onBackAction: () => void;
  onComplete?: (formData: Partial<OrganizerOnboardingData>) => Promise<void>;
  onError?: (error: unknown) => void;
}

export function SummaryStep({
  isSubmitting,
  onBackAction,
  onComplete,
  onError,
}: SummaryStepProps) {
  const { formData, setComplete, reset } = useOnboardingStore();

  async function handleSubmit() {
    try {
      if (!onComplete) {
        throw new Error('No onComplete function provided');
      }

      // Call the complete callback if provided
      await onComplete(formData);

      // Mark onboarding as complete and clear the store
      setComplete(true);
      reset();
    } catch (error) {
      log.warn('Failed to submit onboarding data', { error });

      // Call the error callback if provided
      if (onError) {
        onError(error);
      }
    }
  }

  return (
    <div className="space-y-6">
      <div className="rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4">
        <div className="flex">
          <div className="ml-3">
            <p className="text-blue-700 text-sm">
              <strong>You're joining TicketCARE!</strong> All event organizers
              are part of our platform community. This allows us to provide
              better support and streamlined event management for everyone.
            </p>
          </div>
        </div>
      </div>
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Organizer Information</CardTitle>
            <CardDescription>Your organizer details</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Business Name
                </dt>
                <dd className="text-sm">{formData.name}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Your organizer contact details</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div className="col-span-2">
                <dt className="font-medium text-muted-foreground text-sm">
                  Address
                </dt>
                <dd className="text-sm">{formData.address}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Phone
                </dt>
                <dd className="text-sm">{formData.phone}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Email
                </dt>
                <dd className="text-sm">{formData.email}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Website
                </dt>
                <dd className="text-sm">{formData.website ?? 'N/A'}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  WhatsApp
                </dt>
                <dd className="text-sm">{formData.whatsapp ?? 'N/A'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Social Media</CardTitle>
            <CardDescription>Your social media profiles</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Facebook
                </dt>
                <dd className="text-sm">{formData.facebook ?? 'N/A'}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Twitter
                </dt>
                <dd className="text-sm">{formData.twitter ?? 'N/A'}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Instagram
                </dt>
                <dd className="text-sm">{formData.instagram ?? 'N/A'}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  YouTube
                </dt>
                <dd className="text-sm">{formData.youtube ?? 'N/A'}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  TikTok
                </dt>
                <dd className="text-sm">{formData.tiktok ?? 'N/A'}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  RedNote
                </dt>
                <dd className="text-sm">{formData.rednote ?? 'N/A'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onBackAction}>
          Back
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Join TicketCARE
        </Button>
      </div>
    </div>
  );
}
