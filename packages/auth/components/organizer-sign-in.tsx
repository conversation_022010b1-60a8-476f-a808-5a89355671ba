'use client';

import {
  <PERSON>,
  <PERSON>Off,
  LoaderCircle,
} from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { organizerSignIn } from '../organizer-client';
import { z } from 'zod';

const signinFormSchema = z.object({
  email: z.string().email({ message: 'Invalid email' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

export const OrganizerSignIn = () => {
  const [pending, setPending] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const form = useForm<{ email: string; password: string }>({
    defaultValues: {
      email: '',
      password: '',
    },
    resolver: zodResolver(signinFormSchema),
  });

  async function onSubmit(values: { email: string; password: string }) {
    let emailNotVerified = false;
    setPending(true);

    try {
      const { error } = await organizerSignIn.email(values, {
        onSuccess: () => {
          setPending(false);
          router.push('/');
        },
        // verify email before signin
        onError: (ctx) => {
          // flag the error
          if (ctx.error.status === 403) {
            emailNotVerified = true;
            throw ctx.error;
          }
        },
      });

      if (error) {
        console.error(error);
        throw error;
      }
    } catch (error) {
      setPending(false);
      console.error(error);

      if (emailNotVerified) {
        form.setError('root', {
          message: 'Please verify your email address',
        });
        return;
      }

      form.setError('root', {
        message: 'Invalid email or password',
      });
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="grid gap-2">
              <FormControl>
                <Input
                  {...field}
                  type="email"
                  placeholder="Enter your email"
                  disabled={pending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="grid gap-2">
              <FormControl>
                <div className="relative">
                  <Input
                    {...field}
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    disabled={pending}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={pending}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={pending}>
          {pending ? (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Sign in
        </Button>

        {form.formState.errors.root && (
          <p
            data-slot="form-message"
            className="text-destructive-foreground text-sm"
          >
            {form.formState.errors.root?.message}
          </p>
        )}

        <div className="mt-4 flex items-center justify-center text-muted-foreground text-sm">
          Don't have an account? &nbsp;
          <Button type="button" asChild variant="link" className="p-0">
            <Link href="/sign-up">Sign up</Link>
          </Button>
        </div>
      </form>
    </Form>
  );
};
