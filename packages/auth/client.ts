import {
  adminClient,
  inferAdditionalFields,
  magicLinkClient,
  organizationClient,
  emailOTPClient,
} from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import {
  adminRole,
  customerRole,
  organizerRole,
  ownerRole,
  superAdminRole,
} from './permissions';
import { ac } from './permissions';
import type { auth } from './server';

// type AuthClient = ReturnType<
//   typeof createAuthClient<{
//     plugins: [
//       ReturnType<typeof adminClient>,
//       // ReturnType<typeof stripeClient>,
//       ReturnType<typeof magicLinkClient>,
//       // ReturnType<typeof organizationClient>,
//     ];
//   }>
// >;

const authClient = createAuthClient({
  plugins: [
    // stripeClient(),
    // customSessionClient<typeof auth>(),
    inferAdditionalFields<typeof auth>(),
    adminClient({
      ac,
      roles: {
        superAdmin: superAdminRole,
        admin: adminRole,
        customer: customerRole,
      },
    }),
    organizationClient({
      ac,
      roles: { owner: owner<PERSON><PERSON>, organizer: organizer<PERSON>ole },
    }),
    magicLinkClient(),
    emailOTPClient(),
  ],
});

export const {
  signIn,
  signUp,
  signOut,
  useSession,
  admin,
  organization,
  useActiveOrganization,
  emailOtp,
} = authClient;

export default authClient;
