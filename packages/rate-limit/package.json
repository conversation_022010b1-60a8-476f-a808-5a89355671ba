{"name": "@repo/rate-limit", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@t3-oss/env-nextjs": "^0.12.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.4", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.2"}}