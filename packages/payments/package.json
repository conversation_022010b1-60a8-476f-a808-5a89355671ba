{"name": "@repo/payments", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@stripe/agent-toolkit": "^0.5.1", "@t3-oss/env-nextjs": "^0.12.0", "server-only": "^0.0.1", "stripe": "^17.7.0", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.2"}}