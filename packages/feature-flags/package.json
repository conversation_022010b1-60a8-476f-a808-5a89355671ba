{"name": "@repo/feature-flags", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/design-system": "workspace:*", "@t3-oss/env-nextjs": "^0.12.0", "@vercel/toolbar": "^0.1.34", "flags": "^3.1.1", "react": "^19.0.0", "zod": "^3.24.2"}, "peerDependencies": {"next": "15.1.7"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4", "typescript": "^5.8.2"}}