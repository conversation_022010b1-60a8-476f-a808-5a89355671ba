{"name": "@repo/next-config", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "next": "15.1.7"}, "dependencies": {"@next/bundle-analyzer": "15.1.7", "@prisma/nextjs-monorepo-workaround-plugin": "^6.4.1", "@t3-oss/env-core": "^0.12.0", "@t3-oss/env-nextjs": "^0.12.0", "zod": "^3.24.2"}}