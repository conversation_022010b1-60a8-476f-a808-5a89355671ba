import { format, parseISO } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

// The timezone for Malaysia
const MALAYSIA_TIMEZONE = 'Asia/Kuala_Lumpur';

// Helper function to safely parse dates
const parseDate = (date: string | Date): Date => {
  if (date instanceof Date) {
    return date;
  }
  try {
    // Use parseISO for ISO strings which preserves timezone info
    return parseISO(date);
  } catch (_error) {
    // Fallback to regular Date constructor
    return new Date(date);
  }
};

// Format time for display in Malaysia timezone
export const formatTime = (time: string | Date, showTimezone: boolean = true) => {
  const parsedDate = parseDate(time);
  const malaysiaTime = toZonedTime(parsedDate, MALAYSIA_TIMEZONE);
  const timeStr = format(malaysiaTime, 'h:mm a');
  return showTimezone ? `${timeStr} (MYT)` : timeStr;
};

// Format date for display in Malaysia timezone
export const formatDate = (date: string | Date, formatStr?: string | null, showTimezone: boolean = false) => {
  const parsedDate = parseDate(date);
  const malaysiaTime = toZonedTime(parsedDate, MALAYSIA_TIMEZONE);
  const dateStr = format(malaysiaTime, formatStr || 'MMM d, yyyy');
  return showTimezone ? `${dateStr} (MYT)` : dateStr;
};

// Format date & time for display in Malaysia timezone
export const formatDateTime = (date: string | Date, showTimezone: boolean = true) => {
  const parsedDate = parseDate(date);
  const malaysiaTime = toZonedTime(parsedDate, MALAYSIA_TIMEZONE);
  const dateTimeStr = format(malaysiaTime, 'MMM d, yyyy h:mm a');
  return showTimezone ? `${dateTimeStr} (MYT)` : dateTimeStr;
};

// Format date & time for display in Malaysia timezone
export const formatDateTimeTable = (date: string | Date, showTimezone: boolean = true) => {
  const parsedDate = parseDate(date);
  const malaysiaTime = toZonedTime(parsedDate, MALAYSIA_TIMEZONE);
  const dateStr = format(malaysiaTime, 'MMM d, yyyy');
  const timeStr = format(malaysiaTime, 'h:mm a');

  // First line: date; Second line: time with optional timezone suffix
  return `${dateStr}\n${timeStr}${showTimezone ? ' (MYT)' : ''}`;
};

// Format date range for display in Malaysia timezone
export const formatDateRange = (
  startDate: string | Date,
  endDate: string | Date,
  showTimezone: boolean = true
) => {
  // Handle cases where dates might not be provided
  if (!startDate || !endDate) {
    return null;
  }

  // Parse dates and convert to Malaysia timezone
  const start = toZonedTime(parseDate(startDate), MALAYSIA_TIMEZONE);
  const end = toZonedTime(parseDate(endDate), MALAYSIA_TIMEZONE);

  // Check if start and end are the same day
  const isSameDay = format(start, 'yyyy-MM-dd') === format(end, 'yyyy-MM-dd');

  if (isSameDay) {
    // Same day format: "15 January, 2025 (MYT)" or "15 January, 2025"
    const dateStr = format(start, 'd MMMM, yyyy');
    return showTimezone ? `${dateStr} (MYT)` : dateStr;
  }

  const isSameMonth = format(start, 'yyyy-MM') === format(end, 'yyyy-MM');

  if (isSameMonth) {
    // Same month format: "15-20 January, 2025 (MYT)" or "15-20 January, 2025"
    const dateStr = `${format(start, 'd')}-${format(end, 'd')} ${format(start, 'MMMM, yyyy')}`;
    return showTimezone ? `${dateStr} (MYT)` : dateStr;
  }

  // Different month format: "15 January - 20 February, 2025 (MYT)" or "15 January - 20 February, 2025"
  const dateStr = `${format(start, 'd MMMM')} - ${format(end, 'd MMMM, yyyy')}`;
  return showTimezone ? `${dateStr} (MYT)` : dateStr;
};


export function calculateTimeLeft(expiryDate: Date) {
  const now = new Date()
  const difference = expiryDate.getTime() - now.getTime()

  if (difference <= 0) {
    return {
      minutes: 0,
      seconds: 0,
    }
  }

  const minutes = Math.floor(difference / 1000 / 60)
  const seconds = Math.floor((difference / 1000) % 60)

  return {
    minutes,
    seconds,
  }
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-MY', {
    style: 'currency',
    currency: 'MYR',
  }).format(amount)
}