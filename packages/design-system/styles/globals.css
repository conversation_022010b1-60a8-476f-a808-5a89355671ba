@import "tailwindcss";

@source "../**/*.{ts,tsx}";
@source "../../**/*.{ts,tsx}";

@plugin 'tailwindcss-animate';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --primary-red: #f44336;
  --primary-red-hover: #bd3a19;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  --primary-red: #f44336;
  --primary-red-hover: #bd3a19;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-red: var(--primary-red);
  --color-primary-red-hover: var(--primary-red-hover);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  --animate-line-shadow: line-shadow 15s linear infinite;
  @keyframes line-shadow {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 100% -100%;
    }
  }
}

/* This layer is added by shadcn/ui */
@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  h1,
  .heading-1 {
    @apply text-2xl font-bold;
  }

  h2,
  .heading-2 {
    @apply text-xl font-semibold;
  }

  h3,
  .heading-3 {
    @apply text-lg font-medium;
  }

  h4,
  .heading-4 {
    @apply text-base font-medium;
  }

  p,
  .body-default {
    @apply text-sm;
  }
}

/* This layer is by next-forge */
@layer base {
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    @apply border-border;
  }
  * {
    @apply min-w-0;
  }
  html {
    text-rendering: optimizelegibility;
  }
  body {
    @apply min-h-[100dvh];
  }
  input::placeholder,
  textarea::placeholder {
    @apply text-muted-foreground;
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    @apply cursor-pointer;
  }
}

/* Typography utility classes */
@layer utilities {
  /* Base typography */
  .hero-text {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  .body-large {
    @apply text-base;
  }

  .body-default {
    @apply text-sm;
  }

  .body-small {
    @apply text-xs;
  }

  .label-large {
    @apply text-base font-medium;
  }

  .label {
    @apply text-sm font-medium;
  }

  .label-small {
    @apply text-xs font-medium;
  }

  .caption-large {
    @apply text-base text-muted-foreground;
  }

  .caption {
    @apply text-sm text-muted-foreground;
  }

  .caption-small {
    @apply text-xs text-muted-foreground;
  }

  .overline {
    @apply text-xs uppercase font-medium;
  }

  .mono {
    @apply font-mono;
  }
}

/* Typography plugin */
@utility prose {
  --tw-prose-body: var(--color-foreground);
  --tw-prose-headings: var(--color-foreground);
  --tw-prose-lead: var(--color-muted-foreground);
  --tw-prose-links: var(--color-primary);
  --tw-prose-bold: var(--color-foreground);
  --tw-prose-counters: var(--color-foreground);
  --tw-prose-bullets: var(--color-muted-foreground);
  --tw-prose-hr: var(--color-muted-foreground);
  --tw-prose-quotes: var(--color-muted-foreground);
  --tw-prose-quote-borders: var(--color-border);
  --tw-prose-captions: var(--color-muted-foreground);
  --tw-prose-code: var(--color-foreground);
  --tw-prose-pre-code: var(--color-foreground);
  --tw-prose-pre-bg: var(--color-background);
  --tw-prose-th-borders: var(--color-border);
  --tw-prose-td-borders: var(--color-border);
  --tw-prose-invert-body: var(--color-foreground);
  --tw-prose-invert-headings: var(--color-foreground);
  --tw-prose-invert-lead: var(--color-muted-foreground);
  --tw-prose-invert-links: var(--color-primary);
  --tw-prose-invert-bold: var(--color-foreground);
  --tw-prose-invert-counters: var(--color-foreground);
  --tw-prose-invert-bullets: var(--color-foreground);
  --tw-prose-invert-hr: var(--color-muted-foreground);
  --tw-prose-invert-quotes: var(--color-muted-foreground);
  --tw-prose-invert-quote-borders: var(--color-border);
  --tw-prose-invert-captions: var(--color-muted-foreground);
  --tw-prose-invert-code: var(--color-foreground);
  --tw-prose-invert-pre-code: var(--color-foreground);
  --tw-prose-invert-pre-bg: var(--color-background);
  --tw-prose-invert-th-borders: var(--color-border);
  --tw-prose-invert-td-borders: var(--color-border);
}
