{"name": "@repo/design-system", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.3", "@lexical/code": "^0.31.0", "@lexical/link": "^0.31.0", "@lexical/list": "^0.31.0", "@lexical/markdown": "^0.31.0", "@lexical/react": "^0.31.0", "@lexical/rich-text": "^0.31.0", "@lexical/selection": "^0.31.0", "@lexical/table": "^0.31.0", "@lexical/utils": "^0.31.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/observability": "workspace:*", "@untitledui/icons": "^0.0.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "^8.5.2", "geist": "^1.3.1", "input-otp": "^1.4.2", "lexical": "^0.31.0", "lucide-react": "^0.477.0", "motion": "^12.23.12", "next-themes": "^0.4.4", "qs": "^6.14.0", "radash": "^12.1.0", "react": "19.0.0", "react-aria-components": "^1.12.1", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-moveable": "^0.56.0", "react-phone-number-input": "^3.4.12", "react-resizable-panels": "^2.1.7", "react-use": "^17.6.0", "recharts": "^2.15.1", "server-only": "^0.0.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-react-aria-components": "^2.0.1", "timescape": "^0.7.1", "tunnel-rat": "^0.1.2", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.9", "@types/qs": "^6.9.18", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.5", "@types/react-phone-number-input": "^3.1.37", "postcss": "^8.5.3", "tailwindcss": "^4.0.12", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.2"}}