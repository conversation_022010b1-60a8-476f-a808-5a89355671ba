{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "styles/globals.css", "baseColor": "neutral", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@repo/design-system/components", "utils": "@repo/design-system/lib/utils", "format": "@repo/design-system/lib/format", "hooks": "@repo/design-system/hooks", "lib": "@repo/design-system/lib", "ui": "@repo/design-system/components/ui", "blocks": "@repo/design-system/components/blocks", "editor": "@repo/design-system/components/editor", "editor/utils": "@repo/design-system/components/editor/utils", "editor/context": "@repo/design-system/components/editor/context", "editor/plugins": "@repo/design-system/components/editor/plugins"}, "iconLibrary": "lucide"}