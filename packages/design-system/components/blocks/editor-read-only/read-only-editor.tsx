'use client'

import {
  InitialConfigType,
  LexicalComposer,
} from '@lexical/react/LexicalComposer'
import { nodes } from '@repo/design-system/components/blocks/editor-00/nodes'
import { ReadOnlyPlugins } from '@repo/design-system/components/blocks/editor-read-only/read-only-plugins'
import { editorTheme } from '@repo/design-system/components/editor/themes/editor-theme'
import { TooltipProvider } from '@repo/design-system/components/ui/tooltip'
import { cn } from '@repo/design-system/lib/utils'
import { EditorState, SerializedEditorState } from 'lexical'

const editorConfig: InitialConfigType = {
  namespace: 'Editor',
  theme: editorTheme,
  nodes,
  editable: false,
  onError: (error: Error) => {
    console.error(error)
  },
}

export function ReadOnlyEditor({
  editorState,
  editorSerializedState,
  className,
  innerClassName,
}: {
  editorState?: EditorState
  editorSerializedState?: SerializedEditorState
  className?: string
  innerClassName?: string
}) {
  return (
    <div className={cn("overflow-hidden bg-background", className)}>
      {editorSerializedState && 
        <LexicalComposer
          initialConfig={{
            ...editorConfig,
            ...(editorState ? { editorState } : {}),
            ...(editorSerializedState
              ? { editorState: JSON.stringify(editorSerializedState) }
              : {}),
          }}
        >
          <TooltipProvider>
            <ReadOnlyPlugins className={innerClassName} />
          </TooltipProvider>
        </LexicalComposer>
      }
    </div>
  )
}
