import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import { ClickableLinkPlugin } from '@lexical/react/LexicalClickableLinkPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { TabIndentationPlugin } from "@lexical/react/LexicalTabIndentationPlugin";
import { ContentEditable } from '@repo/design-system/components/editor/editor-ui/content-editable';
import { AutoLinkPlugin } from '@repo/design-system/components/editor/plugins/auto-link-plugin';
import { FloatingLinkEditorPlugin } from '@repo/design-system/components/editor/plugins/floating-link-editor-plugin';
import { ImagesPlugin } from '@repo/design-system/components/editor/plugins/images-plugin';
import { BlockFormatDropDown } from '@repo/design-system/components/editor/plugins/toolbar/block-format-toolbar-plugin';
import { FormatBulletedList } from '@repo/design-system/components/editor/plugins/toolbar/block-format/format-bulleted-list';
import { FormatCheckList } from '@repo/design-system/components/editor/plugins/toolbar/block-format/format-check-list';
import { FormatHeading } from '@repo/design-system/components/editor/plugins/toolbar/block-format/format-heading';
import { FormatNumberedList } from '@repo/design-system/components/editor/plugins/toolbar/block-format/format-numbered-list';
import { FormatParagraph } from '@repo/design-system/components/editor/plugins/toolbar/block-format/format-paragraph';
import { FormatQuote } from '@repo/design-system/components/editor/plugins/toolbar/block-format/format-quote';
import { InsertImage } from '@repo/design-system/components/editor/plugins/toolbar/block-insert/insert-image';
import { ClearFormattingToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/clear-formatting-toolbar-plugin';
import { ElementFormatToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/element-format-toolbar-plugin';
import { FontFormatToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/font-format-toolbar-plugin';
import { FontSizeToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/font-size-toolbar-plugin';
import { HistoryToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/history-toolbar-plugin';
import { LinkToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/link-toolbar-plugin';
import { ToolbarPlugin } from '@repo/design-system/components/editor/plugins/toolbar/toolbar-plugin';
import { useState } from 'react';



export function Plugins() {
  const [floatingAnchorElem, setFloatingAnchorElem] =
    useState<HTMLDivElement | null>(null);

  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };

  return (
    <div className="relative">
      {/* toolbar plugins */}
      <ToolbarPlugin>
        {({ blockType }) => (
          <div className="vertical-align-middle sticky top-0 z-10 flex flex-wrap gap-2 overflow-auto border-b p-1">
            <HistoryToolbarPlugin />
            <BlockFormatDropDown>
              <FormatParagraph />
              <FormatHeading levels={['h1', 'h2', 'h3']} />
              <FormatNumberedList />
              <FormatBulletedList />
              <FormatCheckList />
              <FormatQuote />
            </BlockFormatDropDown>
            <FontSizeToolbarPlugin />
            <FontFormatToolbarPlugin format="bold" />
            <FontFormatToolbarPlugin format="italic" />
            <FontFormatToolbarPlugin format="underline" />
            <FontFormatToolbarPlugin format="strikethrough" />
            <LinkToolbarPlugin />
            <ClearFormattingToolbarPlugin />
            <ElementFormatToolbarPlugin />
            <InsertImage />
          </div>
        )}
      </ToolbarPlugin>
      <div className="relative">
        <RichTextPlugin
          contentEditable={
            <div className="">
              <div className="" ref={onRef}>
                <ContentEditable placeholder={'Start typing ...'} />
              </div>
            </div>
          }
          ErrorBoundary={LexicalErrorBoundary}
        />

        {/* editor plugins */}
        <HistoryPlugin />
        <ListPlugin />
        <CheckListPlugin />
        <ClickableLinkPlugin />
        <AutoLinkPlugin />
        <LinkPlugin />
        <FloatingLinkEditorPlugin anchorElem={floatingAnchorElem} />
        <TabIndentationPlugin />
        <ImagesPlugin />
      </div>
      {/* actions plugins */}
    </div>
  );
}
