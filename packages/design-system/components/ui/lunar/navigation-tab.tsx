import { createContext, useContext, useEffect, useMemo, useRef, useState } from "react"
import type { ReactNode } from "react"

interface ActiveItem {
  index: number
  size: number
  position: number
}

interface RootContextType {
  setActive: (index: number, size: number, position: number) => void
  activeItem: ActiveItem
  isReady: boolean
  setMounted: () => void
  isMounted: boolean
  isFluid: boolean
  isVertical: boolean
  duration: number
}

interface ListContextType {
  peers: Element[]
}

interface ChildrenRenderProps {
  ready: boolean
  position: string
  duration: string
  size: string
}

interface ItemChildrenRenderProps {
  setActive: (event?: boolean | Event) => void
  isActive: boolean
}

interface RootProps {
  duration?: number
  vertical?: boolean
  fluid?: boolean
  as?: React.ElementType
  children: (props: ChildrenRenderProps) => ReactNode
  className?: string
  [key: string]: any
}

interface ListProps {
  as?: React.ElementType
  children: ReactNode
  className?: string
  [key: string]: any
}

interface ItemProps {
  onActivated?: () => void
  active?: boolean
  as?: React.ElementType
  children: (props: ItemChildrenRenderProps) => ReactNode
  className?: string
  [key: string]: any
}

const RootContext = createContext<RootContextType | undefined>(undefined)
const ListContext = createContext<ListContextType | undefined>(undefined)

export function Root({
  duration = 500,
  vertical = false,
  fluid = false,
  as: Component = "div",
  children,
  className,
  ...props
}: RootProps) {
  const [isReady, setReady] = useState(false)
  const [isMounted, setMounted] = useState(false)

  const [activeItem, setActiveItem] = useState({
    index: -1,
    size: 0,
    position: 0,
  })

  // var previousSize = -1
  const [previousSize, setPreviousSize] = useState(-1)
  const [previousPosition, setPreviousPosition] = useState(0)
  const [animated, setAnimated] = useState(true)

  function handleFluidMove(targetSize: number, targetPosition: number) {
    if (!animated) {
      return
    }

    setAnimated(false)

    if (previousSize === -1) {
      const size = targetSize
      const position = targetPosition
      setActiveItem((currentState) => ({ ...currentState, size, position }))

      setAnimated(true)
    } else {
      if (targetPosition > previousPosition) {
        const size = targetSize + targetPosition - previousPosition
        setActiveItem((currentState) => ({ ...currentState, size }))

        setTimeout(() => {
          const size = targetSize
          const position = targetPosition
          setActiveItem((currentState) => ({ ...currentState, size, position }))

          setAnimated(true)
        }, duration)
      } else {
        const position = targetPosition
        const size = previousSize + previousPosition - targetPosition

        setActiveItem((currentState) => ({
          ...currentState,
          size,
          position,
        }))

        setTimeout(() => {
          const size = targetSize

          setActiveItem((currentState) => ({ ...currentState, size }))

          setAnimated(true)
        }, duration)
      }
    }

    setPreviousSize(targetSize)
    setPreviousPosition(targetPosition)
  }

  function setActive(index: number, size: number, position: number) {
    setActiveItem((currentState) => ({ ...currentState, index }))

    if (fluid) {
      handleFluidMove(size, position)
    } else {
      setActiveItem((currentState) => ({ ...currentState, size, position }))
    }

    setReady(true)
  }

  const context: RootContextType = {
    setActive,
    activeItem,
    isReady,
    setMounted: () => setMounted(true),
    isMounted,
    isFluid: fluid,
    isVertical: vertical,
    duration: duration,
  }

  return (
    <RootContext.Provider value={context}>
      <Component className={className} {...props}>
        {children({
          ready: isReady,
          position: `${activeItem.position}px`,
          duration: `${duration}ms`,
          size: `${activeItem.size}px`,
        })}
      </Component>
    </RootContext.Provider>
  )
}

export function List({ as: Component = "div", children, className, ...props }: ListProps) {
  const container = useRef<HTMLElement>(null)

  const [childElements, setChildElements] = useState<Element[]>([])

  useEffect(() => {
    if (container.current) {
      setChildElements(Array.from(container.current.children))
    }
  }, [])

  const context: ListContextType = { peers: childElements }

  return (
    <ListContext.Provider value={context}>
      <Component ref={container} className={className} {...props}>
        {children}
      </Component>
    </ListContext.Provider>
  )
}

export function Item({ onActivated = () => {}, active = false, as: Component = "div", children, className, ...props }: ItemProps) {
  const rootContext = useContext(RootContext)
  const listContext = useContext(ListContext)

  const container = useRef<HTMLElement>(null)

  if (!rootContext) {
    throw new Error('Item must be used within a Root component')
  }

  if (!listContext) {
    throw new Error('Item must be used within a List component')
  }

  const index = useMemo(() => {
    return listContext.peers && container.current ? listContext.peers.indexOf(container.current) : -1
  }, [listContext.peers])

  const isActive = useMemo(() => index === rootContext.activeItem.index, [rootContext.activeItem.index, index])

  // once
  useEffect(() => {
    if (active) {
      setActive(false)
    }

    if (index === listContext.peers.length - 1) {
      rootContext.setMounted()
    }
  }, [index])

  useEffect(() => {
    // set first element as active
    if (rootContext.activeItem.index === -1 && index === 0) {
      setActive(false)
    }
  }, [rootContext.isMounted])

  function setActive(event?: boolean | Event) {
    if (!container.current || !rootContext) return
    
    if (rootContext.isVertical) {
      rootContext.setActive(index, container.current.getBoundingClientRect().height, container.current.offsetTop)
    } else {
      rootContext.setActive(index, container.current.getBoundingClientRect().width, container.current.offsetLeft)
    }

    if (event !== false) {
      setTimeout(() => onActivated(), rootContext.duration)
    }
  }

  return (
    <Component ref={container} className={className} {...props}>
      {children({ setActive, isActive })}
    </Component>
  )
}

export const Navigation = Object.assign(Root, { List, Item })
