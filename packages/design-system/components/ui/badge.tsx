import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@repo/design-system/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground',
        cancelled:
          'border-transparent bg-secondary text-secondary-foreground',
        destructive:
          'border-transparent bg-destructive text-white',
        rejected:
          'border-transparent bg-destructive text-destructive-foreground',
        refunded:
          'border-transparent bg-destructive text-destructive-foreground',
        verified:
          'border-transparent bg-green-100 text-green-800',
        used:
          'border-transparent bg-green-100 text-green-800',
        success:
          'border-transparent bg-green-100 text-green-800',
        completed:
          'border-transparent bg-green-100 text-green-800',
        pending:
          'border-transparent bg-gray-100 text-gray-800',
        premium: "border-transparent bg-purple-500 text-white",
        hold:
          'border-transparent bg-amber-100 text-amber-800',
        void:
          'border-transparent bg-slate-300 text-slate-800',
        reserved:
          'border-transparent bg-blue-100 text-blue-800',
        purchased:
          'border-transparent bg-green-100 text-green-800',
        outline: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span"

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }
