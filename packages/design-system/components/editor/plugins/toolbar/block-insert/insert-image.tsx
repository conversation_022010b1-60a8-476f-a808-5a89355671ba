'use client'

import { ImageIcon } from 'lucide-react'

import { useToolbarContext } from '@repo/design-system/components/editor/context/toolbar-context'

import { InsertImageDialog } from '@repo/design-system/components/editor/plugins/images-plugin'
import { But<PERSON> } from '@repo/design-system/components/ui/button'

export function InsertImage() {
  const { activeEditor, showModal } = useToolbarContext()

  return (
    <Button
      type='button'
      size={'sm'}
      variant={'outline'}
      onClick={(e) => {
        showModal('Insert Image', (onClose) => (
          <InsertImageDialog activeEditor={activeEditor} onClose={onClose} />
        ))
      }}
      className=""
    >
      <div className="flex items-center gap-1">
        <ImageIcon className="size-4" />
        <span>Image</span>
      </div>
    </Button>
  )
}
