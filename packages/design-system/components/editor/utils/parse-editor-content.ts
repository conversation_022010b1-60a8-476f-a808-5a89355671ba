import { SerializedEditorState } from 'lexical';

/**
 * Safely parses a string into a Lexical editor state.
 * If the string is valid JSON, it will be parsed as an editor state.
 * If not, it will be converted into a simple paragraph node.
 * 
 * @param content The string content to parse
 * @returns A valid SerializedEditorState or undefined if content is falsy
 */
export function parseEditorContent(content?: string | null): SerializedEditorState | undefined {
  if (!content) return undefined;
  
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(content);
    return parsed;
  } catch (e) {
    // If parsing fails, create a simple paragraph node with the text
    return {
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: "normal",
                style: "",
                text: content,
                type: "text",
                version: 1
              }
            ],
            direction: "ltr",
            format: "",
            indent: 0,
            textFormat: 0,
            textStyle: "",
            type: "paragraph",
            version: 1
          }
        ],
        direction: "ltr",
        format: "",
        indent: 0,
        type: "root",
        version: 1
      }
    } as unknown as SerializedEditorState;
  }
}
