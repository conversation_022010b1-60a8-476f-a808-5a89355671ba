{"name": "@repo/seo", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"lodash.merge": "^4.6.2", "react": "^19.0.0", "schema-dts": "^1.1.5"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/lodash.merge": "^4.6.9", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4", "next": "15.1.7"}}