import { log } from '@repo/observability/log';
import sgMail from '@sendgrid/mail';
import { keys } from './keys';
import { capitalize } from '@repo/design-system/lib/utils';

// Define mail content type since it's not exported from @sendgrid/mail
interface MailContent {
  type: string;
  value: string;
}

// Define attachment type for SendGrid
interface Attachment {
  content: string;
  filename: string;
  type?: string;
  disposition?: string;
  contentId?: string;
}

/**
 * Email options for sending emails
 */
export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  from?: string;
  replyTo?: string;
  templateId?: string;
  dynamicTemplateData?: Record<string, unknown>;
  attachments?: Attachment[];
}

// Initialize SendGrid client
const config = keys();
sgMail.setApiKey(config.SENDGRID_API_KEY || '');

/**
 * Format a chip payment method string
 * @param paymentMethod The chip payment method string
 * @returns Formatted payment method string
 */
export function formatChipPaymentMethod(paymentMethod?: string | null) {
  let formattedPaymentMethod = 'Online Payment';

  if (!paymentMethod) return formattedPaymentMethod;

  if (paymentMethod.startsWith('chip-')) {
    formattedPaymentMethod = paymentMethod.replace('chip-', '').toLowerCase();
  }

  switch (formattedPaymentMethod) {
    case 'fpx':
      formattedPaymentMethod = 'FPX';
      break;

    case 'visa':
    case 'mastercard':
      formattedPaymentMethod = 'Credit/Debit Card';
      break;

    case 'duitnow_qr':
      formattedPaymentMethod = 'DuitNow QR';
      break;

    case 'razer_grabpay':
    case 'razer_shopeepay':
    case 'razer_tng':
      formattedPaymentMethod = 'E-Wallet';
      break;

    default:
      formattedPaymentMethod = capitalize(formattedPaymentMethod);
      break;
  }

  return formattedPaymentMethod;
}

/**
 * Send an email using SendGrid
 * @param options Email options
 * @returns Promise with the send result
 */
export async function sendEmail(options: EmailOptions) {
  const {
    to,
    subject,
    text,
    html,
    from,
    replyTo,
    templateId,
    dynamicTemplateData,
    attachments,
  } = options;

  try {
    // Determine reply-to address
    const replyToAddress = replyTo || config.SENDGRID_REPLY_TO;

    // Base message properties
    const baseMsg = {
      to,
      from: {
        name: 'TicketCARE',
        email: from || config.SENDGRID_FROM,
      },
      subject,
      ...(replyToAddress ? { replyTo: replyToAddress } : {}),
      ...(attachments && attachments.length > 0 ? { attachments } : {}),
    };

    // Prepare the message for SendGrid
    // Define a type for the message to avoid 'any' type
    type SendGridMessage = {
      to: string | string[];
      from: string | { name: string; email: string };
      subject: string;
      replyTo?: string;
      content?: MailContent[];
      templateId?: string;
      dynamicTemplateData?: Record<string, unknown>;
      attachments?: Attachment[];
    };

    let msg: SendGridMessage;

    if (templateId) {
      // If using a template
      msg = {
        ...baseMsg,
        templateId,
        ...(dynamicTemplateData ? { dynamicTemplateData } : {}),
      };
      log.info('Sending template email', {
        to,
        subject,
        templateId,
        hasAttachments: attachments && attachments.length > 0,
        attachmentsLength: attachments?.length ?? 0,
        dynamicTemplateData: dynamicTemplateData
          ? JSON.stringify(dynamicTemplateData)
          : undefined,
      });
    } else {
      // If using regular content
      const content: MailContent[] = [];

      if (text) {
        content.push({ type: 'text/plain', value: text });
      }

      if (html) {
        content.push({ type: 'text/html', value: html });
      }

      // If no content is provided, add an empty text content
      if (content.length === 0) {
        content.push({ type: 'text/plain', value: '' });
      }

      msg = {
        ...baseMsg,
        content,
      };
      log.info('Sending regular email', {
        to,
        subject,
        hasAttachments: attachments && attachments.length > 0,
      });
    }

    // @ts-expect-error - SendGrid's types are too restrictive
    const response = await sgMail.send(msg);
    log.info('Email sent successfully', { to, subject });

    return {
      success: true,
      id: response[0]?.headers['x-message-id'] || 'unknown',
      provider: 'sendgrid',
      templateId: templateId || undefined,
    };
  } catch (error) {
    log.error('Error sending email:', {
      error,
      to: options.to,
      subject: options.subject,
      templateId: options.templateId,
      hasAttachments: options.attachments && options.attachments.length > 0,
      attachmentsLength: attachments?.length ?? 0,
    });
    throw error;
  }
}

/**
 * SendGrid template IDs
 */
export const TEMPLATES = {
  ORGANIZER_ACCOUNT_CONFIRMATION: 'd-cb374235f5544dd3880cfeb732b210e9',
  ORGANIZER_TICKET_CONFIRMATION: 'd-1510f89fb25f4359a4bfa89a04744f6d',
  ORGANIZER_EVENT_DONATION_CONFIRMATION: 'd-4d23752fab1e4d16aa85ed9e34add82e',
};

/**
 * Send an organizer account confirmation email
 * @param options Email options
 * @returns Promise with the send result
 */
export function sendOrganizerAccountConfirmation(options: {
  to: string | string[];
  from?: string;
  replyTo?: string;
  confirmUrl: string;
}) {
  const { to, from, replyTo, confirmUrl } = options;

  return sendEmail({
    to,
    from,
    replyTo,
    subject: 'Confirm Your TicketCARE Organization Account',
    templateId: TEMPLATES.ORGANIZER_ACCOUNT_CONFIRMATION,
    dynamicTemplateData: {
      subject: 'Confirm Your TicketCARE Organization Account',
      url_confirm_account: confirmUrl,
      current_year: new Date().getFullYear().toString(),
    },
  });
}

/**
 * Send an organizer ticket confirmation email
 * @param options Email options
 * @returns Promise with the send result
 */
export function sendOrganizerTicketConfirmation(options: {
  to: string | string[];
  eventName: string;
  orderNo: string;
  orderDetails: {
    fullName: string;
    participantName: string;
    contactNumber: string;
    email: string;
    purchaseDatetime: string;
    paymentMethod: string;
    bankCharges: string;
    totalAmount: string;
    paymentStatus: string;
    ticketTypes: string[];
    itemPrices: string[];
    ticketIds: string[];
  };
  from?: string;
  replyTo?: string;
  attachments?: Attachment[];
}) {
  const { to, eventName, orderNo, orderDetails, from, replyTo, attachments } =
    options;

  // Format arrays into newline-separated strings for the template
  const ticketTypesFormatted = orderDetails.ticketTypes.join('<br>');
  const itemPricesFormatted = orderDetails.itemPrices.join('<br>');
  const ticketIdsFormatted = orderDetails.ticketIds.join('<br>');

  return sendEmail({
    subject: `✅ You're in! Here are your Tickets for ${eventName} ${orderNo.split('-').pop()}`,
    to,
    from,
    replyTo,
    templateId: TEMPLATES.ORGANIZER_TICKET_CONFIRMATION,
    dynamicTemplateData: {
      subject: `✅ You're in! Here are your Tickets for ${eventName} ${orderNo.split('-').pop()}`,
      FULL_NAME: orderDetails.fullName,
      event_name: eventName,
      order_id: orderNo,
      participant_name: orderDetails.participantName,
      contact_number: orderDetails.contactNumber,
      email: orderDetails.email,
      purchase_datetime: orderDetails.purchaseDatetime,
      payment_method: orderDetails.paymentMethod,
      bank_charges: orderDetails.bankCharges,
      total_amount: orderDetails.totalAmount,
      payment_status: orderDetails.paymentStatus,
      ticket_type: ticketTypesFormatted,
      item_price: itemPricesFormatted,
      ticket_id: ticketIdsFormatted,
      order_quantity: orderDetails.ticketTypes.length.toString(),
      current_year: new Date().getFullYear().toString(),
    },
    attachments,
  });
}

/**
 * Send an organizer event donation confirmation email
 * @param options Email options
 * @returns Promise with the send result
 */
export function sendOrganizerEventDonationConfirmation(options: {
  to: string | string[];
  eventName: string;
  donorName: string;
  donationId: string;
  donationAmount: string;
  donationDatetime: string;
  paymentMethod: string;
  thankYouMessage?: string;
  organizerName: string;
  from?: string;
  replyTo?: string;
  attachments?: Attachment[];
}) {
  const {
    to,
    eventName,
    donorName,
    donationId,
    donationAmount,
    donationDatetime,
    paymentMethod,
    thankYouMessage,
    organizerName,
    from,
    replyTo,
    attachments,
  } = options;

  const currentYear = new Date().getFullYear().toString();

  return sendEmail({
    subject: `Donation Confirmation for ${eventName} ${donationId.substring(0, 8).toUpperCase()}`,
    to,
    from,
    replyTo,
    templateId: TEMPLATES.ORGANIZER_EVENT_DONATION_CONFIRMATION,
    dynamicTemplateData: {
      subject: `Donation Confirmation for ${eventName} ${donationId.substring(0, 8).toUpperCase()}`,
      DONOR_NAME: donorName,
      donation_thank_you_message: thankYouMessage,
      organizer_name: organizerName,
      order_id: donationId,
      donor_name: donorName,
      donation_amount: donationAmount,
      donation_datetime: donationDatetime,
      payment_method: paymentMethod,
      current_year: currentYear,
    },
    attachments,
  });
}

// Export request notification utilities
export {
  sendRequestStatusNotification,
  sendRequestConversionNotification,
} from './utils/request-notification';

// Export organizer account confirmation utility
export { sendOrganizerAccountConfirmationEmail } from './utils/organizer-account-confirmation';
