{"name": "@repo/email", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@react-email/components": "0.0.33", "@sendgrid/mail": "^8.1.5", "@t3-oss/env-nextjs": "^0.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.24.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "^19.0.4", "typescript": "^5.8.2"}}