import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const keys = () =>
  createEnv({
    server: {
      // Robolly configuration
      ROBOLLY_API_KEY: z.string().min(1),
      // SendGrid configuration
      SENDGRID_FROM: z.string().min(1).email(),
      SENDGRID_API_KEY: z.string().min(1),
      SENDGRID_REPLY_TO: z.string().email().optional(),
    },
    runtimeEnv: {
      // Robolly configuration
      ROBOLLY_API_KEY: process.env.ROBOLLY_API_KEY,
      // SendGrid configuration
      SENDGRID_FROM: process.env.SENDGRID_FROM,
      SENDGRID_API_KEY: process.env.SENDGRID_API_KEY || '',
      SENDGRID_REPLY_TO: process.env.SENDGRID_REPLY_TO,
    },
  });
