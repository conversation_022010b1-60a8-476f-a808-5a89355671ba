'use server';

import { database } from '@repo/database';
import { formatCurrency, formatDate } from '@repo/design-system/lib/format';
import { log } from '@repo/observability/log';
import {
  formatChipPaymentMethod,
  sendOrganizerTicketConfirmation,
} from '../index';
import { generateTicketPdf } from './pdf';

/**
 * Simple function to capitalize the first letter of a string
 * @param str The string to capitalize
 * @returns The capitalized string
 */
function capitalize(str: string): string {
  if (!str) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Generate PDFs for all tickets in an order with failsafe retry mechanism
 * @param tickets Array of tickets to generate PDFs for
 * @returns Array of successfully generated ticket PDFs
 */
async function generateTicketPdfs(
  tickets: Array<{
    id: string;
    slug: string;
    ticketType: {
      name: string;
      price: string | number | { toString(): string }; // Handle Decimal type from Prisma
    };
  }>
): Promise<
  Array<{
    ticketId: string;
    pdfBuffer: Buffer;
    ticketType: string;
    ticketPrice: string;
  }>
> {
  // Generate PDFs for all tickets sequentially to avoid rate limiting
  const ticketPdfs: Array<{
    ticketId: string;
    pdfBuffer: Buffer;
    ticketType: string;
    ticketPrice: string;
  }> = [];
  const failedTickets: typeof tickets = [];

  // First pass: attempt to generate PDFs for all tickets
  for (const ticket of tickets) {
    try {
      const pdfBuffer = await generateTicketPdf(ticket.id);
      ticketPdfs.push({
        ticketId: ticket.id,
        pdfBuffer,
        ticketType: ticket.ticketType.name,
        ticketPrice: formatCurrency(Number(ticket.ticketType.price)),
      });
    } catch (error) {
      log.error('Failed to generate PDF for ticket (first attempt)', {
        ticketId: ticket.id,
        error,
      });
      failedTickets.push(ticket);
    }
  }

  // Failsafe: retry failed tickets after all initial attempts are complete
  if (failedTickets.length > 0) {
    log.info(
      `Retrying PDF generation for ${failedTickets.length} failed tickets`
    );

    for (const ticket of failedTickets) {
      try {
        const pdfBuffer = await generateTicketPdf(ticket.id);
        ticketPdfs.push({
          ticketId: ticket.id,
          pdfBuffer,
          ticketType: ticket.ticketType.name,
          ticketPrice: formatCurrency(Number(ticket.ticketType.price)),
        });
        log.info('Successfully generated PDF on retry', {
          ticketId: ticket.id,
        });
      } catch (error) {
        log.error('Failed to generate PDF for ticket (retry attempt)', {
          ticketId: ticket.id,
          error,
        });
        // Final failure - continue without this PDF
      }
    }
  }

  return ticketPdfs;
}

/**
 * Send an order confirmation email with ticket attachments
 * @param orderId The ID of the order to send confirmation for
 * @returns A promise that resolves to the email sending result
 */
export async function sendOrderConfirmationEmail(orderId: string) {
  const order = await database.order.findUnique({
    where: { id: orderId },
    include: {
      tickets: {
        include: {
          ticketType: true,
          timeSlot: true,
          event: {
            include: {
              organizer: true,
            },
          },
        },
      },
      user: true,
      event: {
        include: {
          organizer: true,
        },
      },
    },
  });

  if (!order) {
    throw new Error('Order not found');
  }

  if (!order.user.email) {
    throw new Error('User email not found');
  }

  // Generate PDFs for all tickets with failsafe retry mechanism
  const ticketPdfs = await generateTicketPdfs(order.tickets);

  // Prepare attachments for SendGrid
  const attachments = ticketPdfs.map((pdf, index) => ({
    content: pdf?.pdfBuffer.toString('base64') ?? '',
    filename: `Ticket-${order.tickets[index].slug}.pdf`,
    type: 'application/pdf',
    disposition: 'attachment',
  }));

  // Prepare ticket details for the email template
  const ticketTypes = order.tickets.map((ticket) => ticket.ticketType.name);
  const itemPrices = order.tickets.map((ticket) =>
    formatCurrency(Number(ticket.ticketType.price))
  );
  const ticketIds = order.tickets.map((ticket) => ticket.slug);

  // Format the purchase date
  const purchaseDate = order.createdAt
    ? formatDate(order.createdAt)
    : formatDate(new Date());

  const response = await sendOrganizerTicketConfirmation({
    to: order.user.email,
    eventName: order.event?.title ?? '',
    orderNo: order.id,
    orderDetails: {
      fullName: order.user.name ?? 'Ticket Holder',
      participantName: order.user.name ?? 'Ticket Holder',
      contactNumber: order.user.phone ?? 'N/A',
      email: order.user.email,
      purchaseDatetime: purchaseDate,
      paymentMethod: formatChipPaymentMethod(order.paymentMethod),
      bankCharges: '0.00',
      totalAmount: formatCurrency(Number(order.totalAmount)),
      paymentStatus: capitalize(order.paymentStatus),
      ticketTypes,
      itemPrices,
      ticketIds,
    },
    attachments,
  });

  return { success: response.success };
}
