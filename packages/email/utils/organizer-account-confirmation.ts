'use server';

import { log } from '@repo/observability/log';
import { sendOrganizerAccountConfirmation } from '../index';

/**
 * Send an organizer account confirmation email
 * @param email The email address to send the confirmation to
 * @param confirmUrl The URL for account confirmation
 * @returns A promise that resolves to the email sending result
 */
export async function sendOrganizerAccountConfirmationEmail(
  email: string,
  confirmUrl: string
) {
  if (!email) {
    throw new Error('Email address is required');
  }

  if (!confirmUrl) {
    throw new Error('Confirmation URL is required');
  }

  try {
    const response = await sendOrganizerAccountConfirmation({
      to: email,
      confirmUrl,
    });

    log.info('Organizer account confirmation email sent successfully', {
      email,
    });

    return { success: response.success };
  } catch (error) {
    log.error('Failed to send organizer account confirmation email', {
      email,
      error,
    });
    throw error;
  }
}
