'use server';

import { database } from '@repo/database';
import { formatDateTime } from '@repo/design-system/lib/format';
import { log } from '@repo/observability/log';
import { formatChipPaymentMethod } from '../index';
import { getSignedRenderLink } from './sign-render-link';

export async function downloadTicket(ticketId: string) {
  try {
    const ticket = await database.ticket.findUnique({
      where: { id: ticketId },
      include: {
        event: {
          include: {
            organizer: true,
          },
        },
        ticketType: true,
        timeSlot: true,
      },
    });

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    if (ticket.status !== 'purchased' && ticket.status !== 'reserved') {
      throw new Error('Invalid ticket status');
    }

    if (!ticket.event) {
      throw new Error('Ticket event not found');
    }

    const signedRenderLink = getSignedRenderLink({
      format: 'pdf',
      mode: 'ticket',
      modifications: {
        txt_event_name: ticket.event.title,
        img_event_poster: ticket.event.heroImageUrl ?? '',
        txt_event_datetimeday: formatDateTime(ticket.timeSlot.startTime),
        txt_ticket_type: ticket.ticketType.name,
        txt_ticket_seating: '-', // TODO seating module
        txt_event_venue: `${ticket.event.venueName} | ${ticket.event.venueAddress}`,
        txt_ticket_code: ticket.slug,
        qr_ticket_code: ticket.slug, // TODO: standardize code to use slug for now
        txt_contact: `${ticket.event.organizer.name} | ${ticket.event.organizer.phone} | ${ticket.event.organizer.email}`,
        img_sponsors_showcase: ticket.event.sponsorBannerUrl ?? '',
        scale: '1',
        txt_page_no: '1',
        // img_bg: '',
        // btn_goto_ticketcare: 'hex, rgb, or rgba',
      },
    });

    log.info('Signed render link -->', { signedRenderLink });

    return signedRenderLink;
  } catch (e) {
    log.error('downloadTicket failed', { e });
    throw new Error('Download failed, please try again.');
  }
}

/**
 * Generate a PDF ticket for a given ticket ID
 * @param ticketId The ID of the ticket to generate a PDF for
 * @returns A Buffer containing the PDF data
 */
export async function generateTicketPdf(ticketId: string): Promise<Buffer> {
  try {
    const ticket = await database.ticket.findUnique({
      where: { id: ticketId },
      include: {
        event: {
          include: {
            organizer: true,
          },
        },
        ticketType: true,
        timeSlot: true,
      },
    });

    if (!ticket) {
      throw new Error('Ticket not found');
    }

    if (ticket.status !== 'purchased' && ticket.status !== 'reserved') {
      throw new Error('Invalid ticket status');
    }

    if (!ticket.event) {
      throw new Error('Ticket event not found');
    }

    const signedRenderLink = getSignedRenderLink({
      format: 'pdf',
      mode: 'ticket',
      modifications: {
        txt_event_name: ticket.event.title,
        img_event_poster: ticket.event.heroImageUrl ?? '',
        txt_event_datetimeday: formatDateTime(ticket.timeSlot.startTime),
        txt_ticket_type: ticket.ticketType.name,
        txt_ticket_seating: '-', // TODO seating module
        txt_event_venue: `${ticket.event.venueName} | ${ticket.event.venueAddress}`,
        txt_ticket_code: ticket.slug,
        qr_ticket_code: ticket.slug,
        txt_contact: `${ticket.event.organizer.name} | ${ticket.event.organizer.phone} | ${ticket.event.organizer.email}`,
        img_sponsors_showcase: ticket.event.sponsorBannerUrl ?? '',
        scale: '1',
        txt_page_no: '1',
      },
    });

    log.info('Generating PDF for ticket', { ticketId });

    // Fetch the PDF from the signed render link
    const response = await fetch(signedRenderLink);
    if (!response.ok) {
      throw new Error(`Failed to fetch PDF: ${response.statusText}`);
    }

    // Convert the response to a buffer
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return buffer;
  } catch (e) {
    log.error('generateTicketPdf failed', { e });
    throw new Error('PDF generation failed, please try again.');
  }
}

export async function generateDonationReceipt(donationId: string) {
  try {
    const donation = await database.eventDonation.findUnique({
      where: { id: donationId },
      include: {
        event: {
          include: {
            organizer: true,
          },
        },
      },
    });

    if (!donation) {
      throw new Error('Donation not found');
    }

    if (donation.status !== 'completed') {
      throw new Error('Invalid donation status');
    }

    if (!donation.event) {
      throw new Error('Donation event not found');
    }

    // Format date
    const formattedDate = formatDateTime(donation.donatedAt);

    // Generate receipt number (using donation ID)
    const receiptNo = `${donation.id.substring(0, 8).toUpperCase()}`;

    const signedRenderLink = getSignedRenderLink({
      format: 'pdf',
      mode: 'donation-receipt',
      modifications: {
        receipt_no: receiptNo,
        receipt_datetime: formattedDate,
        organizer_name: donation.event.organizer.name || '',
        // organizer_brm: donation.event.organizer.brm || '',
        organizer_address: donation.event.organizer.address || '',
        organizer_contact_no: donation.event.organizer.phone || '',
        organizer_contact_whatsapp: donation.event.organizer.phone || '',
        organizer_email: donation.event.organizer.email || '',
        organizer_website: donation.event.organizer.website || '',
        organizer_logo: donation.event.organizer.logo || '',
        donor_name: donation.name || '',
        donation_campaign_name: donation.event.title || '',
        donation_amount: donation.amount.toFixed(2),
        organizer_pic_name: donation.event.organizer.picName || '',
        payment_method: formatChipPaymentMethod(donation.paymentMethod),
      },
    });

    log.info('Generating PDF for donation receipt', { donationId });

    // Fetch the PDF from the signed render link
    const response = await fetch(signedRenderLink);
    if (!response.ok) {
      throw new Error(`Failed to fetch PDF: ${response.statusText}`);
    }

    // Convert the response to a buffer
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return buffer;
  } catch (e) {
    log.error('generateDonationReceipt failed', { e });
    throw new Error('PDF generation failed, please try again.');
  }
}

export async function downloadDonationReceipt(donationId: string) {
  try {
    const donation = await database.eventDonation.findUnique({
      where: { id: donationId },
      include: {
        event: {
          include: {
            organizer: true,
          },
        },
      },
    });

    if (!donation) {
      throw new Error('Donation not found');
    }

    if (donation.status !== 'completed') {
      throw new Error('Invalid donation status');
    }

    if (!donation.event) {
      throw new Error('Donation event not found');
    }

    // Format date
    const formattedDate = formatDateTime(donation.donatedAt);

    // Generate receipt number (using donation ID)
    const receiptNo = `${donation.id.substring(0, 8).toUpperCase()}`;

    const signedRenderLink = getSignedRenderLink({
      format: 'pdf',
      mode: 'donation-receipt',
      modifications: {
        receipt_no: receiptNo,
        receipt_datetime: formattedDate,
        organizer_name: donation.event.organizer.name || '',
        // organizer_brm: donation.event.organizer.brm || '',
        organizer_address: donation.event.organizer.address || '',
        organizer_contact_no: donation.event.organizer.phone || '',
        organizer_contact_whatsapp: donation.event.organizer.phone || '',
        organizer_email: donation.event.organizer.email || '',
        organizer_website: donation.event.organizer.website || '',
        organizer_logo: donation.event.organizer.logo || '',
        donor_name: donation.name || '',
        donation_campaign_name: donation.event.title || '',
        donation_amount: donation.amount.toFixed(2),
        organizer_pic_name: donation.event.organizer.picName || '',
        payment_method: formatChipPaymentMethod(donation.paymentMethod),
      },
    });

    log.info('Signed render link -->', { signedRenderLink });

    return signedRenderLink;
  } catch (e) {
    log.error('downloadDonationReceipt failed', { e });
    throw new Error('Download failed, please try again.');
  }
}
