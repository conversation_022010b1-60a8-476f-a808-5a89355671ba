import { log } from '@repo/observability/log';
import { sendEmail } from '../index';

interface RequestStatusNotificationOptions {
  to: string;
  requestTitle: string;
  status: string;
  adminNotes?: string;
  submitterName?: string;
}

/**
 * Send a notification email when a request status is updated
 * @param options Email notification options
 * @returns A promise that resolves to the email sending result
 */
export async function sendRequestStatusNotification(
  options: RequestStatusNotificationOptions
) {
  const { to, requestTitle, status, adminNotes, submitterName } = options;

  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'reviewed':
        return 'has been reviewed by our team';
      case 'approved':
        return 'has been approved and will be considered for our roadmap';
      case 'rejected':
        return 'has been reviewed but will not be implemented at this time';
      case 'converted':
        return 'has been approved and added to our development roadmap';
      default:
        return `status has been updated to ${status}`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
      case 'converted':
        return '#22c55e'; // green
      case 'rejected':
        return '#ef4444'; // red
      case 'reviewed':
        return '#3b82f6'; // blue
      default:
        return '#6b7280'; // gray
    }
  };

  const statusMessage = getStatusMessage(status);
  const statusColor = getStatusColor(status);
  const greeting = submitterName ? `Hi ${submitterName}` : 'Hello';

  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Request Status Update</title>
      </head>
      <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
          <h1 style="color: #1a1a1a; margin: 0 0 10px 0; font-size: 24px;">Request Status Update</h1>
          <p style="margin: 0; color: #666; font-size: 16px;">Your feature request has been updated</p>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 8px; border: 1px solid #e5e7eb;">
          <p style="margin: 0 0 20px 0; font-size: 16px;">${greeting},</p>
          
          <p style="margin: 0 0 20px 0; font-size: 16px;">
            Your feature request "<strong>${requestTitle}</strong>" ${statusMessage}.
          </p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; border-left: 4px solid ${statusColor}; margin: 20px 0;">
            <p style="margin: 0; font-weight: 600; color: ${statusColor}; text-transform: uppercase; font-size: 14px; letter-spacing: 0.5px;">
              Status: ${status.replace('-', ' ')}
            </p>
          </div>
          
          ${
            adminNotes
              ? `
            <div style="margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #1a1a1a;">Additional Notes:</h3>
              <p style="margin: 0; color: #666; font-size: 15px; background: #f8f9fa; padding: 15px; border-radius: 6px;">
                ${adminNotes}
              </p>
            </div>
          `
              : ''
          }
          
          <p style="margin: 20px 0 0 0; font-size: 16px;">
            Thank you for your feedback and for helping us improve our product!
          </p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; color: #666; font-size: 14px;">
              Best regards,<br>
              The TicketCARE Team
            </p>
          </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #999; font-size: 12px;">
            This is an automated message. Please do not reply to this email.
          </p>
        </div>
      </body>
    </html>
  `;

  const textContent = `
${greeting},

Your feature request "${requestTitle}" ${statusMessage}.

Status: ${status.replace('-', ' ').toUpperCase()}

${adminNotes ? `Additional Notes:\n${adminNotes}\n\n` : ''}Thank you for your feedback and for helping us improve our product!

Best regards,
The TicketCARE Team

---
This is an automated message. Please do not reply to this email.
  `;

  try {
    const result = await sendEmail({
      to,
      subject: `Request Update: ${requestTitle}`,
      html: htmlContent,
      text: textContent,
    });

    log.info('Request status notification sent successfully', {
      to,
      requestTitle,
      status,
    });

    return result;
  } catch (error) {
    log.error('Failed to send request status notification', {
      to,
      requestTitle,
      status,
      error,
    });
    throw error;
  }
}

interface RequestConversionNotificationOptions {
  to: string;
  requestTitle: string;
  taskTitle: string;
  submitterName?: string;
}

/**
 * Send a notification email when a request is converted to a task
 * @param options Email notification options
 * @returns A promise that resolves to the email sending result
 */
export async function sendRequestConversionNotification(
  options: RequestConversionNotificationOptions
) {
  const { to, requestTitle, taskTitle, submitterName } = options;

  const greeting = submitterName ? `Hi ${submitterName}` : 'Hello';

  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Request Converted to Task</title>
      </head>
      <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #22c55e, #16a34a); padding: 30px; border-radius: 8px; margin-bottom: 20px;">
          <h1 style="color: white; margin: 0 0 10px 0; font-size: 24px;">Great News!</h1>
          <p style="margin: 0; color: rgba(255,255,255,0.9); font-size: 16px;">Your request has been added to our roadmap</p>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 8px; border: 1px solid #e5e7eb;">
          <p style="margin: 0 0 20px 0; font-size: 16px;">${greeting},</p>
          
          <p style="margin: 0 0 20px 0; font-size: 16px;">
            Excellent news! Your feature request "<strong>${requestTitle}</strong>" has been approved and converted into a development task.
          </p>
          
          <div style="background: #f0fdf4; padding: 20px; border-radius: 6px; border-left: 4px solid #22c55e; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #15803d;">Roadmap Task Created:</h3>
            <p style="margin: 0; font-weight: 600; color: #166534; font-size: 15px;">
              ${taskTitle}
            </p>
          </div>
          
          <p style="margin: 20px 0; font-size: 16px;">
            This means your suggestion is now part of our official development roadmap and will be worked on by our team. 
            You can track the progress of this task on our public roadmap page.
          </p>
          
          <p style="margin: 20px 0 0 0; font-size: 16px;">
            Thank you for your valuable feedback and for helping us make our product better!
          </p>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; color: #666; font-size: 14px;">
              Best regards,<br>
              The TicketCARE Team
            </p>
          </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
          <p style="margin: 0; color: #999; font-size: 12px;">
            This is an automated message. Please do not reply to this email.
          </p>
        </div>
      </body>
    </html>
  `;

  const textContent = `
${greeting},

Excellent news! Your feature request "${requestTitle}" has been approved and converted into a development task.

Roadmap Task Created: ${taskTitle}

This means your suggestion is now part of our official development roadmap and will be worked on by our team. You can track the progress of this task on our public roadmap page.

Thank you for your valuable feedback and for helping us make our product better!

Best regards,
The TicketCARE Team

---
This is an automated message. Please do not reply to this email.
  `;

  try {
    const result = await sendEmail({
      to,
      subject: `🎉 Your Request Added to Roadmap: ${taskTitle}`,
      html: htmlContent,
      text: textContent,
    });

    log.info('Request conversion notification sent successfully', {
      to,
      requestTitle,
      taskTitle,
    });

    return result;
  } catch (error) {
    log.error('Failed to send request conversion notification', {
      to,
      requestTitle,
      taskTitle,
      error,
    });
    throw error;
  }
}
