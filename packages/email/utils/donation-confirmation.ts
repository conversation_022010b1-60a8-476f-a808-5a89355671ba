'use server';

import { database } from '@repo/database';
import { formatCurrency, formatDate } from '@repo/design-system/lib/format';
import { log } from '@repo/observability/log';
import {
  formatChipPaymentMethod,
  sendOrganizerEventDonationConfirmation,
} from '../index';
import { generateDonationReceipt } from './pdf';

/**
 * Send a donation confirmation email
 * @param donationId The ID of the donation to send confirmation for
 * @returns A promise that resolves to the email sending result
 */
export async function sendDonationConfirmationEmail(donationId: string) {
  const donation = await database.eventDonation.findUnique({
    where: { id: donationId },
    include: {
      event: {
        include: {
          organizer: true,
        },
      },
      donationModule: true,
    },
  });

  if (!donation) {
    throw new Error('Donation not found');
  }

  if (!donation.email) {
    throw new Error('Donor email not found');
  }

  // Format the donation date
  const donationDate = donation.donatedAt
    ? formatDate(donation.donatedAt)
    : formatDate(new Date());

  // Format the donation amount
  const formattedAmount = formatCurrency(Number(donation.amount));

  // Get organizer name
  const organizerName = donation.event?.organizer?.name || 'Event Organizer';

  // Generate PDF receipt
  let pdfBuffer: Buffer | undefined;
  try {
    pdfBuffer = await generateDonationReceipt(donationId);
  } catch (error) {
    log.error('Failed to generate PDF receipt for donation', {
      donationId,
      error,
    });
    // Continue without attachment if PDF generation fails
  }

  // Prepare attachment for SendGrid if PDF was generated
  const attachments = pdfBuffer
    ? [
        {
          content: pdfBuffer.toString('base64'),
          filename: `Donation-Receipt-${donation.id.substring(0, 8).toUpperCase()}.pdf`,
          type: 'application/pdf',
          disposition: 'attachment',
        },
      ]
    : [];

  try {
    const response = await sendOrganizerEventDonationConfirmation({
      to: donation.email,
      eventName: donation.event?.title ?? 'Event',
      donorName: donation.name ?? 'Donor',
      donationId: donation.id,
      donationAmount: formattedAmount,
      donationDatetime: donationDate,
      paymentMethod: formatChipPaymentMethod(donation.paymentMethod),
      thankYouMessage:
        donation.donationModule?.thankYouMessage ??
        `Thank you for your generous donation to ${donation.event?.title}.`,
      organizerName,
      attachments,
    });

    log.info('Donation confirmation email sent successfully', {
      donationId,
      email: donation.email,
    });

    return { success: response.success };
  } catch (error) {
    log.error('Failed to send donation confirmation email', {
      donationId,
      error,
    });
    throw error;
  }
}
