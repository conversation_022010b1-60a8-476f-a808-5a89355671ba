import { createHmac } from 'node:crypto';
import { log } from '@repo/observability/log';
import { keys } from '../keys';

const config = keys();

// robolly template ids
const TEMPLATES = {
  TICKET: '66f23d3df82083e869392757',
  DONATION_RECEIPT: '68245c95234a7537dc96a973',
};

/**
 * Generate a signed render link for <PERSON><PERSON> templates
 * @param options Options for generating the render link
 * @returns A URL string for the rendered template
 */
export function getSignedRenderLink({
  format = 'jpg',
  mode,
  modifications,
}: {
  modifications: Record<string, string>;
  mode: 'ticket' | 'donation-receipt';
  format?: string;
}) {
  try {
    const templateId =
      mode === 'ticket' ? TEMPLATES.TICKET : TEMPLATES.DONATION_RECEIPT;

    if (!config.ROBOLLY_API_KEY || !templateId) {
      throw new Error('Robolly API key is not defined');
    }

    const query = new URLSearchParams(modifications).toString();
    const signature = createHmac('sha256', config.ROBOLLY_API_KEY)
      .update([templateId, format, query].join(':'))
      .digest('hex');
    const url = `https://api.robolly.com/templates/${templateId}/render.${format}?${query}&sig=${signature}`;
    return url;
  } catch (e) {
    log.error(`getSignedRenderLink failed: ${mode}`, { e });
    throw new Error('Failed to generate link');
  }
}
