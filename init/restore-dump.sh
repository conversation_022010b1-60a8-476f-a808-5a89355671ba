#!/bin/bash
set -e

# Wait for PostgreSQL to be ready
until pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB"; do
  echo "Waiting for PostgreSQL..."
  sleep 2
done

# Restore from dump file (adjust filename as needed)
if [ -f "/docker-entrypoint-initdb.d/ticketcare.dump" ]; then
  echo "Restoring database from dump file..."
  pg_restore -U "$POSTGRES_USER" -d "$POSTGRES_DB" -v /docker-entrypoint-initdb.d/ticketcare.dump
  echo "Database restore completed!"
fi
