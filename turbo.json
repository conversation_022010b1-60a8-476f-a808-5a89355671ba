{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "ui": "tui", "envMode": "loose", "tasks": {"build": {"dependsOn": ["^build", "test"], "outputs": [".next/**", "!.next/cache/**", ".basehub/**", "**/generated/**", ".react-email/**", "node_modules/.prisma/**", "node_modules/@prisma/client/**"]}, "@repo/database#build": {"outputs": ["node_modules/.prisma/**", "node_modules/@prisma/client/**"]}, "test": {"dependsOn": ["^test"]}, "analyze": {"dependsOn": ["^analyze"]}, "dev": {"cache": false, "persistent": true}, "translate": {"dependsOn": ["^translate"], "cache": false}, "clean": {"cache": false}, "//#clean": {"cache": false}}}